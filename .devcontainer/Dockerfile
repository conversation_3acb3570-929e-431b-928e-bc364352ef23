FROM mcr.microsoft.com/devcontainers/php:1-8.2-bookworm

# Install MariaDB client
RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
    && apt-get install -y mariadb-client \ 
    && apt-get install -y libpng-dev libjpeg-dev libfreetype6-dev \
    && apt-get clean -y && rm -rf /var/lib/apt/lists/*

# Install php-mysql driver
RUN docker-php-ext-install mysqli pdo pdo_mysql

# Install GD library and PHP extension
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd

RUN echo "" > /usr/local/etc/php/conf.d/xdebug.ini
RUN echo "memory_limit = 4096M" > /usr/local/etc/php/conf.d/memroy_limit.ini