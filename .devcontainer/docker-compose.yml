version: '3.8'

services:
  app:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile

    volumes:
      - ../..:/workspaces:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity
    environment:
      OPENAI_API_KEY: "***************************************************"

  preprocessor:
    platform: linux/amd64
    build:
      context: ./preprocessor
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
    command: sleep infinity

  db:
    image: mariadb:10.4
    restart: unless-stopped
    volumes:
      - mariadb-data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: mariadb
      MYSQL_DATABASE: mariadb
      MYSQL_USER: mariadb
      MYSQL_PASSWORD: mariadb

  mailer:
    image: axllent/mailpit
    ports:
      - "1025"
      - "8025"
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1

  adminer:
    image: adminer:latest

volumes:
  mariadb-data:
