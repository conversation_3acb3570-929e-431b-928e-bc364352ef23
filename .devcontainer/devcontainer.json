// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/php-mariadb
{
	"name": "Indian Oil Admin",
	"dockerComposeFile": "docker-compose.yml",
	"service": "app",
	"workspaceFolder": "/workspaces/indian_oil_admin",
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},
	// For use with PHP or Apache (e.g.php -S localhost:8080 or apache2ctl start)
	"forwardPorts": [
		8080,
		3306
	],
	"features": {
		"ghcr.io/shyim/devcontainers-features/symfony-cli:0": {},
		"ghcr.io/devcontainers-extra/features/composer:1": {},
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"emallin.phpunit"
			]
		}
	}
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "sudo chmod a+x \"$(pwd)\" && sudo rm -rf /var/www/html && sudo ln -s \"$(pwd)\" /var/www/html"
	// Configure tool-specific properties.
	// "customizations": {},
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}