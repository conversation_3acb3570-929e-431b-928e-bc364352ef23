<?php

namespace App\Security;

use App\Entity\SonataUserUser;
use Sonata\UserBundle\Model\UserManagerInterface;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

class UserProvider implements UserProviderInterface
{
    private UserManagerInterface $userManager;

    public function __construct(UserManagerInterface $userManager)
    {
        $this->userManager = $userManager;
    }

    /**
     * @throws UserNotFoundException if the user is not found
     */
    public function loadUserByIdentifier(string $identifier): UserInterface
    {
        // Try loading by email first
        $user = $this->userManager->findUserByEmail($identifier);

        if ($user instanceof SonataUserUser && $user->isEnabled()) {
            return $user;
        }

        // If not found by email, try by phone number
        $sanitizedPhoneNumber = SonataUserUser::sanitizePhoneNumber($identifier);

        // It's common for UserManager to not have a direct findByPhone method.
        // We might need to use the repository for this.
        $user = $this->userManager->findOneBy(['mobilePhoneCanonical' => $sanitizedPhoneNumber]);

        if ($user instanceof SonataUserUser && $user->isEnabled()) {
            return $user;
        }

        $ex = new UserNotFoundException(sprintf('User with identifier "%s" not found.', $identifier));
        $ex->setUserIdentifier($identifier);
        throw $ex;
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof SonataUserUser) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }

        $reloadedUser = $this->userManager->findOneBy(['id' => $user->getId()]);
        if (null === $reloadedUser) {
            $ex = new UserNotFoundException(sprintf('User with ID "%s" could not be reloaded.', $user->getId()));
            $ex->setUserIdentifier((string) $user->getId());
            throw $ex;
        }

        return $reloadedUser;
    }

    public function supportsClass(string $class): bool
    {
        return $class === SonataUserUser::class || is_subclass_of($class, SonataUserUser::class);
    }
}
