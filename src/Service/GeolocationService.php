<?php

namespace App\Service;

use App\Entity\FillingStation;
use App\Entity\PaymentDetails;
use App\Repository\FillingStationRepository;

class GeolocationService
{
    private FillingStationRepository $fillingStationRepository;

    public function __construct(FillingStationRepository $fillingStationRepository)
    {
        $this->fillingStationRepository = $fillingStationRepository;
    }

    /**
     * Calculate the distance between two geographic coordinates using the Haversine formula
     *
     * @param float $lat1 Latitude of the first point
     * @param float $lon1 Longitude of the first point
     * @param float $lat2 Latitude of the second point
     * @param float $lon2 Longitude of the second point
     * @param string $unit Unit of measurement ('km' for kilometers, 'mi' for miles)
     * @return float Distance between the two points in the specified unit
     */
    public function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2, string $unit = 'km'): float
    {
        if ($lat1 == $lat2 && $lon1 == $lon2) {
            return 0;
        }

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        // Earth's radius in kilometers
        $radius = 6371;

        // Calculate distance
        $distance = $radius * $c;

        // Convert to miles if requested
        if ($unit === 'mi') {
            $distance *= 0.621371;
        }

        return $distance;
    }

    /**
     * Calculate the distance between a payment detail and its associated filling station
     *
     * @param PaymentDetails $paymentDetails
     * @return float|null Distance in kilometers, or null if coordinates are missing
     */
    public function calculatePaymentToStationDistance(PaymentDetails $paymentDetails): ?float
    {
        $fillingStation = $paymentDetails->getFillingStation();

        if (!$fillingStation) {
            return null;
        }

        return $this->calculateDistanceToFillingStation(
            $paymentDetails->getLatitude(),
            $paymentDetails->getLongitude(),
            $fillingStation
        );
    }

    /**
     * Calculate the distance from coordinates to a filling station
     *
     * @param float $latitude
     * @param float $longitude
     * @param FillingStation $fillingStation
     * @return float|null Distance in kilometers, or null if coordinates are missing
     */
    public function calculateDistanceToFillingStation(float $latitude, float $longitude, FillingStation $fillingStation): ?float
    {
        $geolocation = $fillingStation->getGeolocation();

        if (!$geolocation) {
            return null;
        }

        // Parse the geolocation string (latitude,longitude)
        $coordinates = explode(',', $geolocation);

        if (count($coordinates) !== 2) {
            return null;
        }

        $stationLatitude = (float) trim($coordinates[0]);
        $stationLongitude = (float) trim($coordinates[1]);

        return $this->calculateDistance($latitude, $longitude, $stationLatitude, $stationLongitude);
    }

    /**
     * Find the closest filling station to the given coordinates
     *
     * @param float $latitude
     * @param float $longitude
     * @return FillingStation|null The closest filling station or null if none found
     */
    public function findClosestFillingStation(float $latitude, float $longitude): ?FillingStation
    {
        $fillingStations = $this->fillingStationRepository->findAll();
        $closestStation = null;
        $minDistance = PHP_FLOAT_MAX;

        foreach ($fillingStations as $station) {
            $distance = $this->calculateDistanceToFillingStation($latitude, $longitude, $station);

            if ($distance !== null && $distance < $minDistance) {
                $minDistance = $distance;
                $closestStation = $station;
            }
        }

        return $closestStation;
    }

    /**
     * Check if the payment was made at the closest filling station
     *
     * @param PaymentDetails $paymentDetails
     * @return bool True if the payment was made at the closest station, false otherwise
     */
    public function isPaymentAtClosestStation(PaymentDetails $paymentDetails): bool
    {
        $latitude = $paymentDetails->getLatitude();
        $longitude = $paymentDetails->getLongitude();
        $fillingStation = $paymentDetails->getFillingStation();

        if (!$fillingStation || $latitude === 0.0 || $longitude === 0.0) {
            return false;
        }

        $closestStation = $this->findClosestFillingStation($latitude, $longitude);

        if (!$closestStation) {
            return false;
        }

        return $closestStation->getId() === $fillingStation->getId();
    }

    /**
     * Get the distance threshold for considering a payment to be at a filling station
     *
     * @return float Distance threshold in kilometers
     */
    public function getProximityThreshold(): float
    {
        // Consider a payment to be at a filling station if it's within 100 meters
        return 0.1;
    }

    /**
     * Check if the payment was made within the proximity threshold of its filling station
     *
     * @param PaymentDetails $paymentDetails
     * @return bool True if the payment was made within the threshold, false otherwise
     */
    public function isPaymentWithinStationProximity(PaymentDetails $paymentDetails): bool
    {
        $distance = $this->calculatePaymentToStationDistance($paymentDetails);

        if ($distance === null) {
            return false;
        }

        return $distance <= $this->getProximityThreshold();
    }

    /**
     * Calculate geolocation accuracy by time period
     *
     * @param array $paymentDetails Array of PaymentDetails objects
     * @param string $periodType Type of time period ('month', 'week', 'day')
     * @return array Accuracy data grouped by time periods
     */
    public function calculateAccuracyByTimePeriod(array $paymentDetails, string $periodType = 'month'): array
    {
        $periodFormat = match ($periodType) {
            'month' => 'Y-m',
            'week' => 'Y-W',
            'day' => 'Y-m-d',
            default => 'Y-m'
        };

        $periodData = [];

        foreach ($paymentDetails as $payment) {
            $date = $payment->getCreatedAt();
            if (!$date) {
                continue;
            }

            $period = $date->format($periodFormat);

            if (!isset($periodData[$period])) {
                $periodData[$period] = [
                    'total' => 0,
                    'correct' => 0
                ];
            }

            $periodData[$period]['total']++;

            if ($this->isPaymentAtClosestStation($payment)) {
                $periodData[$period]['correct']++;
            }
        }

        // Calculate accuracy percentages and format for chart
        $labels = [];
        $accuracyValues = [];
        $counts = [];

        foreach ($periodData as $period => $data) {
            $labels[] = $period;
            $accuracyValues[] = $data['total'] > 0 ? round(($data['correct'] / $data['total']) * 100, 1) : 0;
            $counts[] = [
                'total' => $data['total'],
                'correct' => $data['correct']
            ];
        }

        return [
            'labels' => $labels,
            'accuracy' => $accuracyValues,
            'counts' => $counts
        ];
    }

    /**
     * Calculate geolocation accuracy by filling station
     *
     * @param array $paymentDetails Array of PaymentDetails objects
     * @return array Accuracy data grouped by filling station
     */
    public function calculateAccuracyByFillingStation(array $paymentDetails): array
    {
        $stationData = [];

        foreach ($paymentDetails as $payment) {
            $fillingStation = $payment->getFillingStation();
            if (!$fillingStation) {
                continue;
            }

            $stationId = $fillingStation->getId();
            $stationName = $fillingStation->getName();

            if (!isset($stationData[$stationId])) {
                $stationData[$stationId] = [
                    'name' => $stationName,
                    'total' => 0,
                    'correct' => 0
                ];
            }

            $stationData[$stationId]['total']++;

            if ($this->isPaymentAtClosestStation($payment)) {
                $stationData[$stationId]['correct']++;
            }
        }

        // Sort stations alphabetically by name
        uasort($stationData, function ($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        // Calculate accuracy percentages and format for chart
        $labels = [];
        $accuracyValues = [];
        $counts = [];

        foreach ($stationData as $stationId => $data) {
            // Include all stations regardless of payment count
            $labels[] = $data['name'];
            $accuracyValues[] = $data['total'] > 0 ? round(($data['correct'] / $data['total']) * 100, 1) : 0;
            $counts[] = [
                'total' => $data['total'],
                'correct' => $data['correct']
            ];
        }

        return [
            'labels' => $labels,
            'accuracy' => $accuracyValues,
            'counts' => $counts
        ];
    }
}
