<?php

namespace App\Service;

use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Entity\UserPointsTransaction;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class OrderService
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Confirm an order after successful OTP verification
     *
     * @param RewardsOrder $order The order to confirm
     * @return void
     */
    public function confirmOrder(RewardsOrder $order): void
    {
        // Update order status to delivered instead of confirmed
        $order->setState(RewardsOrder::STATUS_DELIVERED);
        
        $this->logger->info('Order {orderId} delivered', [
            'orderId' => $order->getOrderNumber(),
            'customerId' => $order->getUser()->getId(),
        ]);
        
        // Save changes
        $this->entityManager->flush();
    }

    /**
     * Check if a points transaction exists for an order
     *
     * @param RewardsOrder $order The order to check
     * @return bool True if a transaction exists, false otherwise
     */
    public function hasPointsTransaction(RewardsOrder $order): bool
    {
        $transactionRepository = $this->entityManager->getRepository(UserPointsTransaction::class);
        $existingTransaction = $transactionRepository->findOneBy(['rewardsOrder' => $order]);
        
        return $existingTransaction !== null;
    }

    /**
     * Create a points transaction for an order
     *
     * @param RewardsOrder $order The order to create a transaction for
     * @param SonataUserUser $customer The customer
     * @return UserPointsTransaction The created transaction
     */
    public function createPointsTransaction(RewardsOrder $order, SonataUserUser $customer): UserPointsTransaction
    {
        $item = $order->getItem();
        $pointsToDeduct = $item->getPoints();
        
        // Create points transaction record
        $pointsTransaction = UserPointsTransaction::createSpendTransaction(
            $customer,
            $pointsToDeduct,
            'Redeemed for ' . $item->getName() . ' (Order #' . $order->getOrderNumber() . ')',
            $order
        );
        
        $this->entityManager->persist($pointsTransaction);
        $this->entityManager->flush();
        
        $this->logger->info('Created points transaction for order {orderId} and customer {customerId}', [
            'orderId' => $order->getOrderNumber(),
            'customerId' => $customer->getId(),
            'points' => $pointsToDeduct,
        ]);
        
        return $pointsTransaction;
    }
}
