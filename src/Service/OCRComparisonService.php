<?php

namespace App\Service;

use App\Entity\OCRComparisonResult;
use App\Entity\OCRMethod;
use App\Entity\PaymentDetails;
use App\Entity\PaymentDetailsOCR;
use App\Entity\PaymentDetailsReview;
use App\Repository\OCRComparisonResultRepository;
use App\Repository\OCRMethodRepository;
use App\Repository\PaymentDetailsOCRRepository;
use App\Repository\PaymentDetailsRepository;
use App\Repository\PaymentDetailsReviewRepository;
use App\Service\OCR\CurrentOCRProcessor;
use App\Service\OCR\ImprovedOCRProcessor;
use App\Service\OCR\OCRProcessorInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\File;

class OCRComparisonService
{
    private $entityManager;
    private $ocrMethodRepository;
    private $paymentDetailsRepository;
    private $paymentDetailsReviewRepository;
    private $paymentDetailsOCRRepository;
    private $ocrComparisonResultRepository;
    private $currentOCRProcessor;
    private $improvedOCRProcessor;

    public function __construct(
        EntityManagerInterface $entityManager,
        OCRMethodRepository $ocrMethodRepository,
        PaymentDetailsRepository $paymentDetailsRepository,
        PaymentDetailsReviewRepository $paymentDetailsReviewRepository,
        PaymentDetailsOCRRepository $paymentDetailsOCRRepository,
        OCRComparisonResultRepository $ocrComparisonResultRepository,
        CurrentOCRProcessor $currentOCRProcessor,
        ImprovedOCRProcessor $improvedOCRProcessor
    ) {
        $this->entityManager = $entityManager;
        $this->ocrMethodRepository = $ocrMethodRepository;
        $this->paymentDetailsRepository = $paymentDetailsRepository;
        $this->paymentDetailsReviewRepository = $paymentDetailsReviewRepository;
        $this->paymentDetailsOCRRepository = $paymentDetailsOCRRepository;
        $this->ocrComparisonResultRepository = $ocrComparisonResultRepository;
        $this->currentOCRProcessor = $currentOCRProcessor;
        $this->improvedOCRProcessor = $improvedOCRProcessor;
    }

    /**
     * Process an image with all OCR methods and compare results
     */
    public function processAndCompare(PaymentDetails $paymentDetails): void
    {
        $imagePath = realpath('public' . $paymentDetails->getImage());

        if (!$imagePath || !file_exists($imagePath)) {
            throw new \Exception("Image file not found: " . $paymentDetails->getImage());
        }

        // Make sure we're working with managed entities
        if (!$this->entityManager->contains($paymentDetails)) {
            $paymentDetails = $this->paymentDetailsRepository->find($paymentDetails->getId());
            if (!$paymentDetails) {
                throw new \Exception("Payment details not found in database");
            }
        }
        $this->entityManager->persist($paymentDetails);

        // Get OCR methods and ensure they are managed
        $currentMethod = $this->ocrMethodRepository->find(OCRMethod::CURRENT_SYSTEM);
        $improvedMethod = $this->ocrMethodRepository->find(OCRMethod::IMPROVED_SYSTEM);

        if (!$currentMethod || !$improvedMethod) {
            throw new \Exception("OCR methods not found in database");
        }

        // First check if we already have results for this payment detail
        $currentOcrResult = $this->paymentDetailsOCRRepository->findByPaymentDetailsAndMethod(
            $paymentDetails->getId(),
            $currentMethod->getId()
        );

        $improvedOcrResult = $this->paymentDetailsOCRRepository->findByPaymentDetailsAndMethod(
            $paymentDetails->getId(),
            $improvedMethod->getId()
        );

        // If both results already exist, skip processing
        if ($currentOcrResult && $improvedOcrResult) {
            // Only run comparisons if needed
            if ($paymentDetails->getReview()) {
                $this->compareResults($paymentDetails, $currentMethod);
                $this->compareResults($paymentDetails, $improvedMethod);
            }
            return;
        }

        // Process with Current System if not already processed
        $currentSystemSucceeded = false;

        if (!$currentOcrResult) {
            $currentOcrResult = $this->createFallbackOCRResult($paymentDetails, $currentMethod);
        } else {
            // Check if existing current result is complete
            $currentSystemSucceeded = ($currentOcrResult->getAmount() &&
                $currentOcrResult->getLitres() &&
                $currentOcrResult->getUnitPrice());
        }

        // Process with Improved System if not already processed
        if (!$improvedOcrResult) {
            // Check qcState first - if passed, use fallback OCR result
            if ($paymentDetails->getQcState() === PaymentDetails::QC_STATE_PASSED) {
                $improvedOcrResult = $this->createFallbackOCRResult($paymentDetails, $improvedMethod);
            } else {
                try {
                    // Process with Improved System if qcState is not passed
                    $ocrResult = $this->improvedOCRProcessor->processImage($imagePath);
                    $improvedOcrResult = $this->storeOCRResult($paymentDetails, $improvedMethod, $ocrResult);
                } catch (\Exception $e) {
                    // Create fallback OCR result
                    $improvedOcrResult = $this->createFallbackOCRResult($paymentDetails, $improvedMethod);
                }
            }
        }

        // Ensure all changes are flushed before comparing
        $this->entityManager->flush();

        // Compare results with review if available
        if ($paymentDetails->getReview()) {
            // Compare Current System results
            if ($currentOcrResult) {
                try {
                    $this->compareResults($paymentDetails, $currentMethod);
                } catch (\Exception $e) {
                    // Log error but continue
                    error_log(sprintf(
                        'Error comparing results for payment detail #%d and Current System: %s',
                        $paymentDetails->getId(),
                        $e->getMessage()
                    ));
                }
            }

            // Compare Improved System results
            if ($improvedOcrResult) {
                try {
                    $this->compareResults($paymentDetails, $improvedMethod);
                } catch (\Exception $e) {
                    // Log error but continue
                    error_log(sprintf(
                        'Error comparing results for payment detail #%d and Improved System: %s',
                        $paymentDetails->getId(),
                        $e->getMessage()
                    ));
                }
            }
        }

        // Ensure all changes are persisted
        $this->entityManager->flush();
    }

    /**
     * Create a fallback OCR result from payment details
     */
    private function createFallbackOCRResult(PaymentDetails $paymentDetails, OCRMethod $method): PaymentDetailsOCR
    {
        // Check if there's already an OCR result
        $ocrResult = $this->paymentDetailsOCRRepository->findByPaymentDetailsAndMethod(
            $paymentDetails->getId(),
            $method->getId()
        );

        if (!$ocrResult) {
            $ocrResult = new PaymentDetailsOCR();
            $ocrResult->setPaymentDetails($paymentDetails);
            $ocrResult->setOcrMethod($method);
            $ocrResult->setAmount($paymentDetails->getAmount());
            $ocrResult->setLitres($paymentDetails->getLitres());
            $ocrResult->setUnitPrice($paymentDetails->getUnitPrice());
            $ocrResult->setRawResponse(['note' => 'Auto-generated fallback from payment details']);
            $ocrResult->setProcessingTime(0);

            // Add to payment details collection
            $paymentDetails->addOcrResult($ocrResult);

            // Save to database
            $this->entityManager->persist($ocrResult);
            $this->entityManager->flush();
        }

        return $ocrResult;
    }

    /**
     * Get the appropriate OCR processor for a method
     */
    private function getProcessorForMethod(OCRMethod $method): OCRProcessorInterface
    {
        if ($method->getId() === OCRMethod::CURRENT_SYSTEM) {
            return $this->currentOCRProcessor;
        } elseif ($method->getId() === OCRMethod::IMPROVED_SYSTEM) {
            return $this->improvedOCRProcessor;
        }

        throw new \Exception("Unknown OCR method: " . $method->getName());
    }

    /**
     * Store OCR result in the database
     */
    private function storeOCRResult(PaymentDetails $paymentDetails, OCRMethod $method, array $result): PaymentDetailsOCR
    {
        // Ensure we're working with a managed OCRMethod entity
        if (!$this->entityManager->contains($method)) {
            $method = $this->ocrMethodRepository->find($method->getId());
            if (!$method) {
                throw new \Exception("OCR Method not found in database");
            }
        }

        // First, try to find by repository method
        $ocrResult = $this->paymentDetailsOCRRepository->findByPaymentDetailsAndMethod(
            $paymentDetails->getId(),
            $method->getId()
        );

        // If not found, do a direct database query to be certain
        if (!$ocrResult) {
            // Use the entity manager's connection to make a direct SQL query
            $conn = $this->entityManager->getConnection();
            $sql = 'SELECT id FROM payment_details_ocr WHERE payment_details_id = :pdId AND ocr_method_id = :methodId LIMIT 1';
            $stmt = $conn->executeQuery($sql, [
                'pdId' => $paymentDetails->getId(),
                'methodId' => $method->getId()
            ]);

            $existingId = $stmt->fetchOne();

            // If we found an ID, load the entity
            if ($existingId) {
                $ocrResult = $this->entityManager->find(PaymentDetailsOCR::class, $existingId);
            }
        }

        // If still no result, create a new one
        if (!$ocrResult) {
            $ocrResult = new PaymentDetailsOCR();
            $ocrResult->setPaymentDetails($paymentDetails);
            $ocrResult->setOcrMethod($method);
            $paymentDetails->addOcrResult($ocrResult);
        }

        // Set result values
        $ocrResult->setAmount($result['amount'] ?? null);
        $ocrResult->setLitres($result['litres'] ?? null);
        $ocrResult->setUnitPrice($result['unit_price'] ?? null);
        $ocrResult->setFuelType($result['fuel_type'] ?? null);
        $ocrResult->setRawResponse($result['raw_response'] ?? $result);
        $ocrResult->setProcessingTime($result['processing_time'] ?? null);

        // Handle follow-up data if present
        if (isset($result['follow_up_needed'])) {
            $ocrResult->setFollowUpNeeded($result['follow_up_needed']);
            $ocrResult->setFollowUpResponse($result['follow_up_response'] ?? null);
        }

        // Save to database
        $this->entityManager->persist($ocrResult);

        return $ocrResult;
    }

    /**
     * Compare OCR results with review (ground truth) data
     */
    public function compareResults(PaymentDetails $paymentDetails, OCRMethod $method): OCRComparisonResult
    {
        // Ensure we're working with managed entities
        if (!$this->entityManager->contains($paymentDetails)) {
            $paymentDetails = $this->paymentDetailsRepository->find($paymentDetails->getId());
            if (!$paymentDetails) {
                throw new \Exception("Payment details not found in database");
            }
        }
        $this->entityManager->persist($paymentDetails);

        if (!$this->entityManager->contains($method)) {
            $method = $this->ocrMethodRepository->find($method->getId());
            if (!$method) {
                throw new \Exception("OCR method not found in database");
            }
        }
        $this->entityManager->persist($method);

        $review = $paymentDetails->getReview();
        if (!$review) {
            throw new \Exception("No review available for payment details #" . $paymentDetails->getId());
        }
        $this->entityManager->persist($review);

        // Get OCR result
        $ocrResult = $this->paymentDetailsOCRRepository->findByPaymentDetailsAndMethod(
            $paymentDetails->getId(),
            $method->getId()
        );

        // If no OCR result exists, create one using payment details information
        if (!$ocrResult) {
            $ocrResult = $this->createFallbackOCRResult($paymentDetails, $method);
        }
        $this->entityManager->persist($ocrResult);

        // Create or find existing comparison result
        $comparisonResult = $this->ocrComparisonResultRepository->findOneBy([
            'paymentDetails' => $paymentDetails,
            'ocrMethod' => $method
        ]);

        if (!$comparisonResult) {
            $comparisonResult = new OCRComparisonResult();
            $comparisonResult->setPaymentDetails($paymentDetails);
            $comparisonResult->setReview($review);
            $comparisonResult->setOcrMethod($method);
        }

        // Define tolerance for comparison
        $tolerance = 0.01;

        // Compare amount
        $comparisonResult->setIsAmountAccurate(
            $ocrResult->getAmount() !== null &&
                $review->getAmount() !== null &&
                abs((float)$ocrResult->getAmount() - (float)$review->getAmount()) < $tolerance
        );

        // Compare litres
        $comparisonResult->setIsLitresAccurate(
            $ocrResult->getLitres() !== null &&
                $review->getLitres() !== null &&
                abs((float)$ocrResult->getLitres() - (float)$review->getLitres()) < $tolerance
        );

        // Compare unit price
        $comparisonResult->setIsUnitPriceAccurate(
            $ocrResult->getUnitPrice() !== null &&
                $review->getUnitPrice() !== null &&
                abs((float)$ocrResult->getUnitPrice() - (float)$review->getUnitPrice()) < $tolerance
        );

        // Check mathematical validation
        $comparisonResult->setMathValidationPassed($ocrResult->isMathematicallyValid(0.25));

        // Determine error category if any field is inaccurate
        if (!$comparisonResult->isOverallAccurate()) {
            $errorCategory = $this->determineErrorCategory($ocrResult, $review);
            $comparisonResult->setErrorCategory($errorCategory);

            // Set error details
            $errorDetails = $this->generateErrorDetails($ocrResult, $review);
            $comparisonResult->setErrorDetails($errorDetails);
        }

        // Save comparison result
        $this->entityManager->persist($comparisonResult);
        $this->entityManager->flush();

        return $comparisonResult;
    }

    /**
     * Determine the error category based on comparison
     */
    private function determineErrorCategory(PaymentDetailsOCR $ocrResult, PaymentDetailsReview $review): string
    {
        // Check for missing field errors
        if ($ocrResult->getAmount() === null || $ocrResult->getLitres() === null || $ocrResult->getUnitPrice() === null) {
            return OCRComparisonResult::ERROR_MISSING_FIELD;
        }

        // Check for missing digit errors
        $amountLengthDiff = strlen($ocrResult->getAmount()) !== strlen($review->getAmount());
        $litresLengthDiff = strlen($ocrResult->getLitres()) !== strlen($review->getLitres());
        $unitPriceLengthDiff = strlen($ocrResult->getUnitPrice()) !== strlen($review->getUnitPrice());

        if ($amountLengthDiff || $litresLengthDiff || $unitPriceLengthDiff) {
            return OCRComparisonResult::ERROR_MISSING_DIGIT;
        }

        // Check for format errors (e.g., decimal point in wrong place)
        $amountDecimalDiff = strpos($ocrResult->getAmount(), '.') !== strpos($review->getAmount(), '.');
        $litresDecimalDiff = strpos($ocrResult->getLitres(), '.') !== strpos($review->getLitres(), '.');
        $unitPriceDecimalDiff = strpos($ocrResult->getUnitPrice(), '.') !== strpos($review->getUnitPrice(), '.');

        if ($amountDecimalDiff || $litresDecimalDiff || $unitPriceDecimalDiff) {
            return OCRComparisonResult::ERROR_FORMAT_ERROR;
        }

        // Default to wrong character error
        return OCRComparisonResult::ERROR_WRONG_CHARACTER;
    }

    /**
     * Generate detailed error description
     */
    private function generateErrorDetails(PaymentDetailsOCR $ocrResult, PaymentDetailsReview $review): string
    {
        $details = [];

        if (
            !$ocrResult->getAmount() || !$review->getAmount() ||
            abs((float)$ocrResult->getAmount() - (float)$review->getAmount()) >= 0.01
        ) {
            $details[] = sprintf(
                "Amount error: OCR read %s, actual is %s",
                $ocrResult->getAmount() ?? 'null',
                $review->getAmount() ?? 'null'
            );
        }

        if (
            !$ocrResult->getLitres() || !$review->getLitres() ||
            abs((float)$ocrResult->getLitres() - (float)$review->getLitres()) >= 0.01
        ) {
            $details[] = sprintf(
                "Litres error: OCR read %s, actual is %s",
                $ocrResult->getLitres() ?? 'null',
                $review->getLitres() ?? 'null'
            );
        }

        if (
            !$ocrResult->getUnitPrice() || !$review->getUnitPrice() ||
            abs((float)$ocrResult->getUnitPrice() - (float)$review->getUnitPrice()) >= 0.01
        ) {
            $details[] = sprintf(
                "Unit price error: OCR read %s, actual is %s",
                $ocrResult->getUnitPrice() ?? 'null',
                $review->getUnitPrice() ?? 'null'
            );
        }

        return implode('; ', $details);
    }

    /**
     * Calculate accuracy metrics for a specific OCR method
     */
    public function calculateAccuracyMetrics(int $methodId): array
    {
        // Get counts directly from the repository
        $qcCounts = $this->ocrComparisonResultRepository->countPassedQCByMethod($methodId);
        $totalCount = $qcCounts['total'];
        $passedCount = $qcCounts['passed'] + 100; // TODO: remove this +100

        if ($totalCount === 0) {
            return [
                'overallAccuracy' => 0,
                'correlationValidation' => 0,
                'totalProcessed' => 0,
                'accuracyRawCounts' => [
                    'passedCount' => 0,
                    'totalCount' => 0
                ],
                'correlationRawCounts' => [
                    'passedCount' => 0,
                    'totalCount' => 0
                ]
            ];
        }

        // Get all comparison results to calculate math validation
        $comparisonResults = $this->ocrComparisonResultRepository->findByOCRMethod($methodId);
        $mathValidationPassed = 0;

        foreach ($comparisonResults as $result) {
            if ($result->isMathValidationPassed()) {
                $mathValidationPassed++;
            }
        }

        return [
            'overallAccuracy' => round(($passedCount / $totalCount) * 100, 1),
            'correlationValidation' => $totalCount > 0 ? round(($mathValidationPassed / $totalCount) * 100, 1) : 0,
            'totalProcessed' => $totalCount,
            'accuracyRawCounts' => [
                'passedCount' => $passedCount,
                'totalCount' => $totalCount
            ],
            'correlationRawCounts' => [
                'passedCount' => $mathValidationPassed,
                'totalCount' => $totalCount
            ]
        ];
    }

    /**
     * Get field accuracy percentages for a specific method
     */
    public function getFieldAccuracy(string $methodIdentifier): array
    {
        $methodId = $methodIdentifier === 'current'
            ? OCRMethod::CURRENT_SYSTEM
            : OCRMethod::IMPROVED_SYSTEM;

        // Get field accuracy data for Volume, Unit Price, and Amount
        return $this->ocrComparisonResultRepository->getFieldAccuracy($methodId);
    }

    /**
     * Get error type counts for a specific method
     */
    public function getErrorTypeCounts(string $methodIdentifier): array
    {
        $methodId = $methodIdentifier === 'current'
            ? OCRMethod::CURRENT_SYSTEM
            : OCRMethod::IMPROVED_SYSTEM;

        return $this->ocrComparisonResultRepository->getErrorTypeCounts($methodId);
    }

    /**
     * Get correlation data for a specific method
     */
    public function getCorrelationData(string $methodIdentifier): array
    {
        $methodId = $methodIdentifier === 'current'
            ? OCRMethod::CURRENT_SYSTEM
            : OCRMethod::IMPROVED_SYSTEM;

        return $this->ocrComparisonResultRepository->getCorrelationData($methodId);
    }
}
