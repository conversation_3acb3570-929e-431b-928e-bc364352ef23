<?php

namespace App\Service;



/**
 * Service for generating and detecting auto-generated email addresses
 */
class EmailGenerationService
{
    private const EMAIL_DOMAIN = 'rewards.ioml.mu';
    private const USER_SUFFIX = '_user';

    private PhoneValidationService $phoneValidationService;

    public function __construct(PhoneValidationService $phoneValidationService)
    {
        $this->phoneValidationService = $phoneValidationService;
    }

    /**
     * Generate an email address from a phone number
     *
     * @param string $phoneNumber The phone number to use for email generation
     *
     * @return string The generated email address
     */
    public function generateFromPhone(string $phoneNumber): string
    {
        // Sanitize the phone number using the phone validation service
        $sanitizedPhone = $this->phoneValidationService->format($phoneNumber);

        // Remove the "+230" prefix and any other non-digit characters
        $cleanPhone = preg_replace('/[^0-9]/', '', $sanitizedPhone);

        // Generate the email address in the format {sanitized_phone_number}<EMAIL>
        return $cleanPhone . self::USER_SUFFIX . '@' . self::EMAIL_DOMAIN;
    }

    /**
     * Check if an email is auto-generated
     *
     * @param string $email The email to check
     *
     * @return bool True if the email is auto-generated, false otherwise
     */
    public function isAutoGenerated(string $email): bool
    {
        // Check if the email matches the pattern {numbers}<EMAIL>
        $pattern = '/^[0-9]+' . preg_quote(self::USER_SUFFIX, '/') . '@' . preg_quote(self::EMAIL_DOMAIN, '/') . '$/';

        return (bool) preg_match($pattern, $email);
    }
}
