<?php

namespace App\Service;

use App\Entity\OTPOrderVerification;
use App\Entity\RegistrationOTP;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Repository\OTPOrderVerificationRepository;
use App\Repository\RegistrationOTPRepository;
use App\Service\OrderService;
use App\Service\PhoneValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class OTPService
{
    private EntityManagerInterface $entityManager;
    private OTPOrderVerificationRepository $otpRepository;
    private ?RegistrationOTPRepository $registrationOTPRepository;
    private LoggerInterface $logger;
    private OrderService $orderService;
    private ?PhoneValidationService $phoneValidationService;

    public function __construct(
        EntityManagerInterface $entityManager,
        OTPOrderVerificationRepository $otpRepository,
        LoggerInterface $logger,
        OrderService $orderService,
        RegistrationOTPRepository $registrationOTPRepository = null,
        PhoneValidationService $phoneValidationService = null
    ) {
        $this->entityManager = $entityManager;
        $this->otpRepository = $otpRepository;
        $this->registrationOTPRepository = $registrationOTPRepository;
        $this->logger = $logger;
        $this->orderService = $orderService;
        $this->phoneValidationService = $phoneValidationService;
    }

    /**
     * Generate a random 6-digit OTP code
     */
    public function generateOTP(): string
    {
        return str_pad((string) random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create and store OTP for a specific order and customer
     *
     * @param RewardsOrder $order The order
     * @param SonataUserUser $customer The customer
     * @return OTPOrderVerification The created OTP verification entity
     */
    public function createOTPVerification(RewardsOrder $order, SonataUserUser $customer): OTPOrderVerification
    {
        // Invalidate previous OTPs for this order and customer
        $this->otpRepository->invalidatePreviousOTPs($order, $customer);
        $this->logger->info('Invalidated previous OTPs for order {orderId} and customer {customerId}', [
            'orderId' => $order->getOrderNumber(),
            'customerId' => $customer->getId(),
        ]);

        // Generate new OTP code
        $otpCode = $this->generateOTP();

        // Create new OTP verification
        $otpVerification = new OTPOrderVerification();
        $otpVerification->setRewardOrder($order);
        $otpVerification->setCustomer($customer);
        $otpVerification->setCode($otpCode);

        // Persist and flush
        $this->entityManager->persist($otpVerification);
        $this->entityManager->flush();

        $this->logger->info('Created new OTP verification for order {orderId} and customer {customerId}', [
            'orderId' => $order->getOrderNumber(),
            'customerId' => $customer->getId(),
        ]);

        return $otpVerification;
    }

    /**
     * Send OTP via SMS
     *
     * @param OTPOrderVerification $otpVerification The OTP verification entity
     * @param string $phoneNumber The phone number to send the OTP to
     * @return bool True if the SMS was sent successfully, false otherwise
     */
    public function sendOTP(OTPOrderVerification $otpVerification, string $phoneNumber): bool
    {
        $customer = $otpVerification->getCustomer();
        $order = $otpVerification->getRewardOrder();
        $otpCode = $otpVerification->getCode();

        // Create message template
        $message = sprintf(
            'Your verification code for order %s is %s. This code will expire in 10 minutes.',
            $order->getOrderNumber(),
            $otpCode
        );

        try {
            // Send SMS using the existing sendSingleMessage function
            $result = $this->sendSingleMessage($phoneNumber, $message);

            $this->logger->info('Sent OTP SMS to {phoneNumber} for order {orderId}', [
                'phoneNumber' => $phoneNumber,
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Failed to send OTP SMS: {error}', [
                'error' => $e->getMessage(),
                'phoneNumber' => $phoneNumber,
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
            ]);

            return false;
        }
    }

    /**
     * Verify an OTP code
     *
     * @param RewardsOrder $order The order
     * @param SonataUserUser $customer The customer
     * @param string $otpCode The OTP code to verify
     * @return array Result with status and message
     */
    public function verifyOTP(RewardsOrder $order, SonataUserUser $customer, string $otpCode): array
    {
        // Find active OTP verification
        $otpVerification = $this->otpRepository->findActiveByOrderAndCustomer($order, $customer);

        if (!$otpVerification) {
            $this->logger->warning('No active OTP found for order {orderId} and customer {customerId}', [
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
            ]);

            return [
                'success' => false,
                'message' => 'No active OTP found for this order',
            ];
        }

        // Check if OTP has reached maximum attempts
        if ($otpVerification->hasReachedMaxAttempts()) {
            $this->logger->warning('Maximum verification attempts reached for order {orderId} and customer {customerId}', [
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
            ]);

            return [
                'success' => false,
                'message' => 'Maximum verification attempts reached',
            ];
        }

        // Increment attempts counter
        $otpVerification->incrementAttempts();
        $this->entityManager->flush();

        // Verify OTP code
        if ($otpVerification->getCode() !== $otpCode) {
            $this->logger->warning('Invalid OTP code for order {orderId} and customer {customerId}', [
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
                'attempts' => $otpVerification->getAttempts(),
            ]);

            return [
                'success' => false,
                'message' => 'Invalid OTP code',
            ];
        }

        // Check if OTP is expired
        if ($otpVerification->isExpired()) {
            $this->logger->warning('Expired OTP for order {orderId} and customer {customerId}', [
                'orderId' => $order->getOrderNumber(),
                'customerId' => $customer->getId(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP has expired',
            ];
        }

        // Mark OTP as verified
        $otpVerification->setVerifiedAt(new \DateTimeImmutable());

        // Save the OTP verification status
        $this->entityManager->flush();

        // Confirm the order using the OrderService
        $this->orderService->confirmOrder($order);

        $this->logger->info('OTP verified successfully for order {orderId} and customer {customerId}', [
            'orderId' => $order->getOrderNumber(),
            'customerId' => $customer->getId(),
        ]);

        return [
            'success' => true,
            'message' => 'OTP verified successfully',
        ];
    }

    /**
     * Create and store OTP for registration
     *
     * @param string $mobilePhone The phone number
     * @param string|null $email The email (optional)
     * @param string|null $firstName The first name (optional)
     * @param string|null $lastName The last name (optional)
     * @return RegistrationOTP The created registration OTP entity
     * @throws \Exception If the registration OTP repository is not available
     */
    public function createRegistrationOTP(
        string $mobilePhone,
        ?string $email = null,
        ?string $firstName = null,
        ?string $lastName = null
    ): RegistrationOTP {
        if (!$this->registrationOTPRepository) {
            throw new \Exception('Registration OTP repository not available');
        }

        // Format phone number if phone validation service is available
        if ($this->phoneValidationService) {
            $mobilePhone = $this->phoneValidationService->format($mobilePhone);
        }

        // Invalidate previous OTPs for this phone number
        $tempUser = new SonataUserUser();
        $tempUser->setMobilePhone($mobilePhone);
        $canonicalPhone = $tempUser->getMobilePhoneCanonical();

        $this->registrationOTPRepository->invalidatePreviousOTPs($canonicalPhone);
        $this->logger->info('Invalidated previous registration OTPs for phone {phone}', [
            'phone' => $mobilePhone,
        ]);

        // Generate new OTP code
        $otpCode = $this->generateOTP();

        // Generate unique registration ID
        $registrationId = 'reg-' . bin2hex(random_bytes(8));

        // Create new registration OTP
        $registrationOTP = new RegistrationOTP();
        $registrationOTP->setMobilePhone($mobilePhone);



        $registrationOTP->setEmail($email);
        $registrationOTP->setFirstName($firstName);
        $registrationOTP->setLastName($lastName);
        $registrationOTP->setOtpCode($otpCode);
        $registrationOTP->setRegistrationId($registrationId);

        // Persist and flush
        $this->entityManager->persist($registrationOTP);
        $this->entityManager->flush();

        $this->logger->info('Created new registration OTP for phone {phone}', [
            'phone' => $mobilePhone,
            'registrationId' => $registrationId,
        ]);

        return $registrationOTP;
    }

    /**
     * Send registration OTP via SMS
     *
     * @param RegistrationOTP $registrationOTP The registration OTP entity
     * @return bool True if the SMS was sent successfully, false otherwise
     */
    public function sendRegistrationOTP(RegistrationOTP $registrationOTP): bool
    {
        $phoneNumber = $registrationOTP->getMobilePhone();
        $otpCode = $registrationOTP->getOtpCode();

        // Create message template
        $message = sprintf(
            'Your verification code for registration is %s. This code will expire in 10 minutes.',
            $otpCode
        );

        try {
            // Send SMS using the existing sendSingleMessage function
            $this->sendSingleMessage($phoneNumber, $message);

            $this->logger->info('Sent registration OTP SMS to {phoneNumber}', [
                'phoneNumber' => $phoneNumber,
                'registrationId' => $registrationOTP->getRegistrationId(),
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Failed to send registration OTP SMS: {error}', [
                'error' => $e->getMessage(),
                'phoneNumber' => $phoneNumber,
                'registrationId' => $registrationOTP->getRegistrationId(),
            ]);

            return false;
        }
    }

    /**
     * Verify registration OTP
     *
     * @param string $registrationId The registration ID
     * @param string $otpCode The OTP code to verify
     * @return array Result with status and message
     * @throws \Exception If the registration OTP repository is not available
     */
    public function verifyRegistrationOTP(string $registrationId, string $otpCode): array
    {
        if (!$this->registrationOTPRepository) {
            throw new \Exception('Registration OTP repository not available');
        }

        // Find active registration OTP
        $registrationOTP = $this->registrationOTPRepository->findActiveByRegistrationId($registrationId);

        if (!$registrationOTP) {
            $this->logger->warning('No active registration OTP found for ID {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => false,
                'message' => 'No active registration found or registration has expired',
            ];
        }

        // Check if OTP has reached maximum attempts
        if ($registrationOTP->hasReachedMaxAttempts()) {
            $this->logger->warning('Maximum verification attempts reached for registration {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => false,
                'message' => 'Maximum verification attempts reached',
            ];
        }

        // Increment attempts counter
        $registrationOTP->incrementAttempts();
        $this->entityManager->flush();

        // Verify OTP code
        if ($registrationOTP->getOtpCode() !== $otpCode) {
            $this->logger->warning('Invalid OTP code for registration {registrationId}', [
                'registrationId' => $registrationId,
                'attempts' => $registrationOTP->getAttempts(),
            ]);

            return [
                'success' => false,
                'message' => 'Invalid OTP code',
            ];
        }

        // Check if OTP is expired
        if ($registrationOTP->isExpired()) {
            $this->logger->warning('Expired OTP for registration {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => false,
                'message' => 'OTP has expired',
            ];
        }

        // Mark OTP as verified
        $registrationOTP->setVerifiedAt(new \DateTimeImmutable());

        // Save the OTP verification status
        $this->entityManager->flush();

        $this->logger->info('Registration OTP verified successfully for {registrationId}', [
            'registrationId' => $registrationId,
        ]);

        return [
            'success' => true,
            'message' => 'OTP verified successfully',
            'registrationOTP' => $registrationOTP,
        ];
    }

    /**
     * Resend registration OTP
     *
     * @param string $registrationId The registration ID
     * @return array Result with status, message, and updated registration OTP
     * @throws \Exception If the registration OTP repository is not available
     */
    public function resendRegistrationOTP(string $registrationId): array
    {
        if (!$this->registrationOTPRepository) {
            throw new \Exception('Registration OTP repository not available');
        }

        // Find registration OTP
        $registrationOTP = $this->registrationOTPRepository->findByRegistrationId($registrationId);

        if (!$registrationOTP) {
            $this->logger->warning('No registration OTP found for ID {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => false,
                'message' => 'Registration not found',
            ];
        }

        // Check if already verified
        if ($registrationOTP->isVerified()) {
            $this->logger->warning('Registration OTP already verified for ID {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => false,
                'message' => 'Registration already verified',
            ];
        }

        // Generate new OTP code
        $otpCode = $this->generateOTP();
        $registrationOTP->setOtpCode($otpCode);

        // Reset attempts counter
        $registrationOTP->setAttempts(0);

        // Update expiration time
        $registrationOTP->setExpiresAt(new \DateTimeImmutable('+10 minutes'));

        // Save the updated registration OTP
        $this->entityManager->flush();

        // Send the new OTP
        $smsSent = $this->sendRegistrationOTP($registrationOTP);

        if (!$smsSent) {
            $this->logger->warning('Failed to send resent OTP SMS for registration {registrationId}', [
                'registrationId' => $registrationId,
            ]);

            return [
                'success' => true,
                'message' => 'OTP regenerated but SMS sending failed',
                'registrationOTP' => $registrationOTP,
            ];
        }

        $this->logger->info('Registration OTP resent successfully for {registrationId}', [
            'registrationId' => $registrationId,
        ]);

        return [
            'success' => true,
            'message' => 'OTP resent successfully',
            'registrationOTP' => $registrationOTP,
        ];
    }

    /**
     * Send a single SMS message
     *
     * @param string $number The phone number to send the SMS to
     * @param string $message The message to send
     * @param int $device The device ID to use for sending
     * @param string|null $schedule The schedule for sending
     * @param bool $isMMS Whether the message is an MMS
     * @param string|null $attachments Attachments for MMS
     * @param bool $prioritize Whether to prioritize the message
     * @return array The result of the SMS sending
     */
    private function sendSingleMessage(
        string $number,
        string $message,
        int $device = 3,
        ?string $schedule = null,
        bool $isMMS = false,
        ?string $attachments = null,
        bool $prioritize = false
    ): array {
        $SERVER = "https://sms.kantartns.io";
        $API_KEY = "aa42bf5fec589aeee93f50ca948f8650427b5446";

        $url = $SERVER . "/services/send.php";
        $postData = array(
            'number' => $number,
            'message' => $message,
            'schedule' => $schedule,
            'key' => $API_KEY,
            'devices' => $device,
            'type' => $isMMS ? "mms" : "sms",
            'attachments' => $attachments,
            'prioritize' => $prioritize ? 1 : 0
        );

        return $this->sendRequest($url, $postData)["messages"][0];
    }

    /**
     * Send an HTTP request
     *
     * @param string $url The URL to send the request to
     * @param array $postData The data to send
     * @return array The response
     */
    private function sendRequest(string $url, array $postData): array
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new \Exception(curl_error($ch));
        }

        curl_close($ch);

        if ($httpCode == 200) {
            $json = json_decode($response, true);

            if ($json == false) {
                if (empty($response)) {
                    throw new \Exception("Missing data in request. Please provide all the required information to send messages.");
                } else {
                    throw new \Exception($response);
                }
            } else {
                if ($json["success"]) {
                    return $json["data"];
                } else {
                    throw new \Exception($json["error"]["message"]);
                }
            }
        } else {
            throw new \Exception("HTTP Error Code : {$httpCode}");
        }
    }
}
