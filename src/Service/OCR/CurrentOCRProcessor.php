<?php

namespace App\Service\OCR;

use App\FuelMeterScanner;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class CurrentOCRProcessor implements OCRProcessorInterface
{
    private $fuelMeterScanner;
    private $parameterBag;

    public function __construct(FuelMeterScanner $fuelMeterScanner, ParameterBagInterface $parameterBag)
    {
        $this->fuelMeterScanner = $fuelMeterScanner;
        $this->parameterBag = $parameterBag;
    }

    /**
     * Process image and extract structured data using the current OCR system
     *
     * @param string $imagePath Path to the image file
     * @return array Extracted data
     */
    public function processImage(string $imagePath): array
    {
        $startTime = microtime(true);

        // Get the Gemini API key
        $apiKey = $this->parameterBag->get('env(GEMINI_API_KEY)');

        // Process using Gemini API
        $result = $this->fuelMeterScanner->scanWithGemini($imagePath, $apiKey);

        // If result is null, return a default result
        if ($result === null) {
            $result = [
                'amount' => 0,
                'litres' => 0,
                'unit_price' => 0,
                'fuel_type' => 'unknown',
                'error' => 'Failed to extract data from image'
            ];
        }

        // Calculate processing time
        $processingTime = (int)((microtime(true) - $startTime) * 1000);

        // Add processing time to result
        $result['processing_time'] = $processingTime;

        return $result;
    }
}
