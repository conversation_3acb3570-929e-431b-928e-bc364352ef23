<?php

namespace App\Service\OCR;

use App\FuelMeterScanner;
use OpenAI;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ImprovedOCRProcessor implements OCRProcessorInterface
{
    private $fuelMeterScanner;
    private $apiKey;
    private $parameterBag;

    public function __construct(FuelMeterScanner $fuelMeterScanner, string $apiKey, ParameterBagInterface $parameterBag)
    {
        $this->fuelMeterScanner = $fuelMeterScanner;
        $this->apiKey = $apiKey;
        $this->parameterBag = $parameterBag;
    }

    /**
     * Process image and extract structured data using the improved OCR system
     * with mathematical validation and follow-up questioning
     *
     * @param string $imagePath Path to the image file
     * @return array Extracted data
     */
    public function processImage(string $imagePath): array
    {
        $startTime = microtime(true);

        // Get the Gemini API key
        $geminiApiKey = $this->parameterBag->get('env(GEMINI_API_KEY)');

        // Process using Gemini API
        $initialResult = $this->fuelMeterScanner->scanWithGemini($imagePath, $geminiApiKey);

        // If result is null, use OpenAI as fallback
        if ($initialResult === null) {
            // Read image file and convert to base64
            $imageData = file_get_contents($imagePath);
            $base64Image = base64_encode($imageData);

            // Process using OpenAI as fallback
            $initialResult = $this->fuelMeterScanner->scan($base64Image);
        }

        // Create response structure
        $result = [
            'amount' => $initialResult['amount'] ?? null,
            'litres' => $initialResult['litres'] ?? null,
            'unit_price' => $initialResult['unit_price'] ?? null,
            'fuel_type' => $initialResult['fuel_type'] ?? null,
            'raw_response' => $initialResult,
            'follow_up_needed' => false,
            'follow_up_response' => null,
        ];

        // Validate mathematical relationship
        if ($this->validateMathematicalRelationship(
            $result['amount'],
            $result['litres'],
            $result['unit_price']
        ) === false) {
            $result['follow_up_needed'] = true;

            // Send follow-up question
            $followUpResult = $this->sendFollowUpQuestion($imagePath, $initialResult);
            $result['follow_up_response'] = $followUpResult;

            // Update values from follow-up if provided
            if (isset($followUpResult['amount'])) {
                $result['amount'] = $followUpResult['amount'];
            }
            if (isset($followUpResult['litres'])) {
                $result['litres'] = $followUpResult['litres'];
            }
            if (isset($followUpResult['unit_price'])) {
                $result['unit_price'] = $followUpResult['unit_price'];
            }
        }

        // Calculate processing time
        $processingTime = (int)((microtime(true) - $startTime) * 1000);
        $result['processing_time'] = $processingTime;

        return $result;
    }

    /**
     * Validate the mathematical relationship between amount, litres, and unit price
     */
    private function validateMathematicalRelationship(
        ?float $amount,
        ?float $litres,
        ?float $unitPrice,
        float $tolerance = 0.01
    ): bool {
        // If any value is missing, validation fails
        if ($amount === null || $litres === null || $unitPrice === null) {
            return false;
        }

        // Check if amount ≈ litres × unitPrice within tolerance
        $calculatedAmount = $litres * $unitPrice;

        return abs($calculatedAmount - $amount) < $tolerance;
    }

    /**
     * Send follow-up question to ChatGPT Vision API
     */
    private function sendFollowUpQuestion(string $imagePath, array $initialResults): array
    {
        $imageData = file_get_contents($imagePath);
        $base64Image = base64_encode($imageData);

        $client = OpenAI::client($this->apiKey);

        $followUpPrompt = <<<EOD
I need you to look at this fuel dispenser screen and verify the values. Here's what I initially detected:

Amount: {$initialResults['amount']} Rupees
Volume: {$initialResults['litres']} Litres
Price per litre: {$initialResults['unit_price']} Rupees

There appears to be a mathematical inconsistency because Amount should approximately equal Volume × Price per litre.

Please carefully check these values again and return the correct values in JSON format.

In your response, pay special attention to:
1. Any digits that might be misread (especially look for 8/B confusion, 5/S confusion, 0/O confusion)
2. Decimal point placement
3. Missing or extra digits

Respond only with a JSON object containing the correct values:
{
  "amount": [corrected amount],
  "litres": [corrected volume],
  "unit_price": [corrected price per litre]
}
EOD;

        $result = $client->chat()->create([
            'model' => 'gpt-4o',
            'temperature' => 0.2,
            'messages' => [
                [
                    "role" => "user",
                    "content" => [
                        ["type" => "text", "text" => $followUpPrompt],
                        [
                            "type" => "image_url",
                            "image_url" => [
                                "url" => "data:image/png;base64,$base64Image",
                                "detail" => "high",
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $content = $result->choices[0]->message->content;

        // Parse JSON response
        return $this->extractJson($content);
    }

    /**
     * Extract JSON from response text
     */
    private function extractJson($responseText)
    {
        // Use regex to find text between ``` or ```json and ```
        $pattern = '/```(?:json)?\s*(\{.*?\})\s*```/s';
        preg_match($pattern, $responseText, $matches);

        // If any matches are found, take the first one and parse it as JSON
        if (!empty($matches)) {
            try {
                // Parse the first JSON match
                $jsonContent = json_decode(trim($matches[1]), true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonContent;
                }
            } catch (\Exception $e) {
                // Failed to parse JSON inside code block
            }
        }

        // Try parsing the entire response as JSON
        return json_decode(trim($responseText), true) ?: [];
    }
}
