<?php

namespace App\Entity;

use App\Repository\FillingStationServiceDetailsRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FillingStationServiceDetailsRepository::class)]
class FillingStationServiceDetails
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: "text", nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    private ?SonataMediaMedia $featuredImage = null;

    #[ORM\ManyToOne(inversedBy: 'serviceDetails')]
    #[ORM\JoinColumn(nullable: false)]
    private ?FillingStationService $service = null;

    #[ORM\ManyToOne(inversedBy: 'serviceDetails')]
    #[ORM\JoinColumn(nullable: false)]
    private ?FillingStation $fillingStation = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getFeaturedImage(): ?SonataMediaMedia
    {
        return $this->featuredImage;
    }

    public function setFeaturedImage(?SonataMediaMedia $featuredImage): static
    {
        $this->featuredImage = $featuredImage;

        return $this;
    }

    public function getService(): ?FillingStationService
    {
        return $this->service;
    }

    public function setService(?FillingStationService $service): static
    {
        $this->service = $service;

        return $this;
    }

    public function getFillingStation(): ?FillingStation
    {
        return $this->fillingStation;
    }

    public function setFillingStation(?FillingStation $fillingStation): static
    {
        $this->fillingStation = $fillingStation;

        return $this;
    }
}
