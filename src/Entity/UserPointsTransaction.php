<?php

namespace App\Entity;

use App\Repository\UserPointsTransactionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UserPointsTransactionRepository::class)]
class UserPointsTransaction
{
    const TYPE_EARN = 'earn';
    const TYPE_SPEND = 'spend';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?SonataUserUser $user = null;

    #[ORM\Column(length: 255)]
    private ?string $type = null;

    #[ORM\Column]
    private ?int $points = null;

    #[ORM\Column(length: 255)]
    private ?string $description = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\ManyToOne]
    private ?PaymentDetails $paymentDetails = null;

    #[ORM\ManyToOne]
    private ?RewardsOrder $rewardsOrder = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    public static function createEarnTransaction(
        SonataUserUser $user,
        int $points,
        string $description,
        ?PaymentDetails $paymentDetails = null
    ): self {
        $transaction = new self();
        $transaction->setUser($user);
        $transaction->setType(self::TYPE_EARN);
        $transaction->setPoints($points);
        $transaction->setDescription($description);
        $transaction->setPaymentDetails($paymentDetails);
        
        return $transaction;
    }

    public static function createSpendTransaction(
        SonataUserUser $user,
        int $points,
        string $description,
        ?RewardsOrder $rewardsOrder = null
    ): self {
        $transaction = new self();
        $transaction->setUser($user);
        $transaction->setType(self::TYPE_SPEND);
        $transaction->setPoints($points);
        $transaction->setDescription($description);
        $transaction->setRewardsOrder($rewardsOrder);
        
        return $transaction;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?SonataUserUser
    {
        return $this->user;
    }

    public function setUser(?SonataUserUser $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function setPoints(int $points): static
    {
        $this->points = $points;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getPaymentDetails(): ?PaymentDetails
    {
        return $this->paymentDetails;
    }

    public function setPaymentDetails(?PaymentDetails $paymentDetails): static
    {
        $this->paymentDetails = $paymentDetails;

        return $this;
    }

    public function getRewardsOrder(): ?RewardsOrder
    {
        return $this->rewardsOrder;
    }

    public function setRewardsOrder(?RewardsOrder $rewardsOrder): static
    {
        $this->rewardsOrder = $rewardsOrder;

        return $this;
    }
}
