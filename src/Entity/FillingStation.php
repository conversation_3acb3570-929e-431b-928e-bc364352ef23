<?php

namespace App\Entity;

use App\Repository\FillingStationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;

#[ORM\Entity(repositoryClass: FillingStationRepository::class)]
class FillingStation
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;
    #[ORM\Column(type: Types::TEXT)]
    private ?string $address = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phone = null;

    /**
     * @var Collection<int, FillingStationService>
     */
    #[ORM\ManyToMany(targetEntity: FillingStationService::class)]
    private Collection $services;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $geolocation = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    private ?SonataMediaMedia $featuredImage = null;

    #[ORM\Column]
    private ?bool $active = true;

    /**
     * @var Collection<int, FillingStationServiceDetails>
     */
    #[ORM\OneToMany(targetEntity: FillingStationServiceDetails::class, mappedBy: 'fillingStation', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $serviceDetails;

    public function __construct()
    {
        $this->services = new ArrayCollection();
        $this->serviceDetails = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): static
    {
        $this->address = $address;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * @return Collection<int, FillingStationService>
     */
    public function getServices(): Collection
    {
        return $this->services;
    }

    public function addService(FillingStationService $service): static
    {
        if (!$this->services->contains($service)) {
            $this->services->add($service);
        }

        return $this;
    }

    public function removeService(FillingStationService $service): static
    {
        $this->services->removeElement($service);

        return $this;
    }

    public function getGeolocation(): ?string
    {
        return $this->geolocation;
    }

    public function setGeolocation(?string $geolocation): static
    {
        $this->geolocation = $geolocation;

        return $this;
    }

    public function getFeaturedImage(): ?SonataMediaMedia
    {
        return $this->featuredImage;
    }

    public function setFeaturedImage(?SonataMediaMedia $featuredImage): static
    {
        $this->featuredImage = $featuredImage;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): static
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection<int, FillingStationServiceDetails>
     */
    public function getServiceDetails(): Collection
    {
        return $this->serviceDetails;
    }

    public function addServiceDetail(FillingStationServiceDetails $serviceDetail): static
    {
        if (!$this->serviceDetails->contains($serviceDetail)) {
            $this->serviceDetails->add($serviceDetail);
            $serviceDetail->setFillingStation($this);
        }

        return $this;
    }

    public function removeServiceDetail(FillingStationServiceDetails $serviceDetail): static
    {
        if ($this->serviceDetails->removeElement($serviceDetail)) {
            // set the owning side to null (unless already changed)
            if ($serviceDetail->getFillingStation() === $this) {
                $serviceDetail->setFillingStation(null);
            }
        }

        return $this;
    }
}
