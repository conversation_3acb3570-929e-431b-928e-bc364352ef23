<?php

namespace App\Entity;

use App\Repository\PaymentDetailsReviewRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PaymentDetailsReviewRepository::class)]
#[ORM\HasLifecycleCallbacks]
class PaymentDetailsReview
{
    const REVIEW_STATUS_PENDING = 'pending_review';
    const REVIEW_STATUS_APPROVED = 'approved';
    const REVIEW_STATUS_REJECTED = 'rejected';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $amount = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $litres = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $unitPrice = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $rejected = false;

    #[ORM\OneToOne(inversedBy: 'review', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?PaymentDetails $paymentDetails = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?SonataUserUser $reviewer = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $reviewedAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $reviewNotes = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $originalValues = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $modifiedValues = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $reviewStatus = self::REVIEW_STATUS_PENDING;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $reviewHistory = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2, nullable: true)]
    private ?string $pointsCalculation = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $pointsBeforeReview = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $pointsAfterReview = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(?string $amount): static
    {
        $this->amount = $amount;

        return $this;
    }

    public function getLitres(): ?string
    {
        return $this->litres;
    }

    public function setLitres(?string $litres): static
    {
        $this->litres = $litres;

        return $this;
    }

    public function getUnitPrice(): ?string
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(?string $unitPrice): static
    {
        $this->unitPrice = $unitPrice;

        return $this;
    }

    public function isRejected(): bool
    {
        return $this->rejected;
    }

    public function setRejected(bool $rejected): static
    {
        $this->rejected = $rejected;

        return $this;
    }

    public function getPaymentDetails(): ?PaymentDetails
    {
        return $this->paymentDetails;
    }

    public function setPaymentDetails(PaymentDetails $paymentDetails): static
    {
        $this->paymentDetails = $paymentDetails;

        return $this;
    }

    public function getReviewer(): ?SonataUserUser
    {
        return $this->reviewer;
    }

    public function setReviewer(?SonataUserUser $reviewer): static
    {
        $this->reviewer = $reviewer;

        return $this;
    }

    public function getReviewedAt(): ?\DateTimeImmutable
    {
        return $this->reviewedAt;
    }

    public function setReviewedAt(?\DateTimeImmutable $reviewedAt): static
    {
        $this->reviewedAt = $reviewedAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getReviewNotes(): ?string
    {
        return $this->reviewNotes;
    }

    public function setReviewNotes(?string $reviewNotes): static
    {
        $this->reviewNotes = $reviewNotes;

        return $this;
    }

    public function getOriginalValues(): ?array
    {
        return $this->originalValues;
    }

    public function setOriginalValues(?array $originalValues): static
    {
        $this->originalValues = $originalValues;

        return $this;
    }

    public function getModifiedValues(): ?array
    {
        return $this->modifiedValues;
    }

    public function setModifiedValues(?array $modifiedValues): static
    {
        $this->modifiedValues = $modifiedValues;

        return $this;
    }

    public function getReviewStatus(): ?string
    {
        return $this->reviewStatus;
    }

    public function setReviewStatus(?string $reviewStatus): static
    {
        $this->reviewStatus = $reviewStatus;

        return $this;
    }

    /**
     * Store original values before modifications
     */
    public function storeOriginalValues(): void
    {
        $this->originalValues = [
            'amount' => $this->paymentDetails->getAmount(),
            'litres' => $this->paymentDetails->getLitres(),
            'unitPrice' => $this->paymentDetails->getUnitPrice(),
            'points' => $this->paymentDetails->getPoints(),
        ];
    }

    /**
     * Store modified values after review
     */
    public function storeModifiedValues(): void
    {
        $this->modifiedValues = [
            'amount' => $this->amount,
            'litres' => $this->litres,
            'unitPrice' => $this->unitPrice,
            'points' => $this->pointsCalculation,
        ];
    }

    /**
     * Mark review as approved
     */
    public function approve(SonataUserUser $reviewer): void
    {
        $this->reviewer = $reviewer;
        $this->reviewedAt = new \DateTimeImmutable();
        $this->reviewStatus = self::REVIEW_STATUS_APPROVED;
        $this->rejected = false;
        $this->storeOriginalValues();
        $this->storeModifiedValues();

        // Calculate points based on the amount
        $this->calculatePoints();

        // Store customer's points before and after review
        if ($this->paymentDetails && $this->paymentDetails->getCustomerUser()) {
            $customer = $this->paymentDetails->getCustomerUser();
            $this->pointsBeforeReview = $customer->getPoints();
            $this->pointsAfterReview = $this->pointsBeforeReview + (int)$this->pointsCalculation;
        }

        // Add to review history
        $this->addToReviewHistory('approved', $this->reviewNotes);
    }

    /**
     * Mark review as rejected
     */
    public function reject(SonataUserUser $reviewer, ?string $notes = null): void
    {
        $this->reviewer = $reviewer;
        $this->reviewedAt = new \DateTimeImmutable();
        $this->reviewStatus = self::REVIEW_STATUS_REJECTED;
        $this->rejected = true;
        $this->reviewNotes = $notes;
        $this->storeOriginalValues();
        $this->addToReviewHistory('rejected', $notes);
    }

    /**
     * Get review history
     */
    public function getReviewHistory(): ?array
    {
        return $this->reviewHistory;
    }

    /**
     * Set review history
     */
    public function setReviewHistory(?array $reviewHistory): static
    {
        $this->reviewHistory = $reviewHistory;

        return $this;
    }

    /**
     * Add an entry to the review history
     */
    public function addToReviewHistory(string $action, ?string $notes = null): static
    {
        if ($this->reviewHistory === null) {
            $this->reviewHistory = [];
        }

        $this->reviewHistory[] = [
            'action' => $action,
            'timestamp' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
            'reviewer' => $this->reviewer ? $this->reviewer->getId() : null,
            'notes' => $notes,
        ];

        return $this;
    }

    /**
     * Get points calculation
     */
    public function getPointsCalculation(): ?string
    {
        return $this->pointsCalculation;
    }

    /**
     * Set points calculation
     */
    public function setPointsCalculation(?string $pointsCalculation): static
    {
        $this->pointsCalculation = $pointsCalculation;

        return $this;
    }

    /**
     * Calculate points based on amount (Rs100 = 1 point)
     */
    public function calculatePoints(): ?string
    {
        if ($this->amount === null) {
            return null;
        }

        // Calculate points (Rs100 = 1 point)
        $this->pointsCalculation = (string) ((float) $this->amount / 100);

        return $this->pointsCalculation;
    }

    /**
     * Get points before review
     */
    public function getPointsBeforeReview(): ?int
    {
        return $this->pointsBeforeReview;
    }

    /**
     * Set points before review
     */
    public function setPointsBeforeReview(?int $pointsBeforeReview): static
    {
        $this->pointsBeforeReview = $pointsBeforeReview;

        return $this;
    }

    /**
     * Get points after review
     */
    public function getPointsAfterReview(): ?int
    {
        return $this->pointsAfterReview;
    }

    /**
     * Set points after review
     */
    public function setPointsAfterReview(?int $pointsAfterReview): static
    {
        $this->pointsAfterReview = $pointsAfterReview;

        return $this;
    }
}
