<?php

namespace App\Entity;

use App\Repository\ImageValidationTrainingDataRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ImageValidationTrainingDataRepository::class)]
class ImageValidationTrainingData
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?PaymentDetails $payemtDetailsId = null;

    #[ORM\Column(length: 255)]
    private ?string $imagePath = null;

    #[ORM\Column]
    private ?bool $isValid = null;

    #[ORM\Column]
    private ?bool $mathematicalValidation = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $validationNote = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column(options: ["default" => false])]
    private bool $includeInExport = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPayemtDetailsId(): ?PaymentDetails
    {
        return $this->payemtDetailsId;
    }

    public function setPayemtDetailsId(?PaymentDetails $payemtDetailsId): static
    {
        $this->payemtDetailsId = $payemtDetailsId;

        return $this;
    }

    public function getImagePath(): ?string
    {
        return $this->imagePath;
    }

    public function setImagePath(string $imagePath): static
    {
        $this->imagePath = $imagePath;

        return $this;
    }

    public function isValid(): ?bool
    {
        return $this->isValid;
    }

    public function setValid(bool $isValid): static
    {
        $this->isValid = $isValid;

        return $this;
    }

    public function isMathematicalValidation(): ?bool
    {
        return $this->mathematicalValidation;
    }

    public function setMathematicalValidation(bool $mathematicalValidation): static
    {
        $this->mathematicalValidation = $mathematicalValidation;

        return $this;
    }

    public function getValidationNote(): ?string
    {
        return $this->validationNote;
    }

    public function setValidationNote(?string $validationNote): static
    {
        $this->validationNote = $validationNote;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function isIncludeInExport(): bool
    {
        return $this->includeInExport;
    }

    public function setIncludeInExport(bool $includeInExport): static
    {
        $this->includeInExport = $includeInExport;

        return $this;
    }
}
