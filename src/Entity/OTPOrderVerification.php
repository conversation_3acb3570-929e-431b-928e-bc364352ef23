<?php

namespace App\Entity;

use App\Repository\OTPOrderVerificationRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OTPOrderVerificationRepository::class)]
#[ORM\HasLifecycleCallbacks]
class OTPOrderVerification
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?RewardsOrder $rewardOrder = null;

    #[ORM\Column(length: 6)]
    private ?string $code = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?SonataUserUser $customer = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $expiredAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $verifiedAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(options: ["default" => 0])]
    private ?int $attempts = 0;

    #[ORM\PrePersist]
    public function setCreatedAtValue(): void
    {
        $this->createdAt = new \DateTimeImmutable();

        if ($this->expiredAt === null) {
            // Set expiration time to 10 minutes from now
            $this->expiredAt = new \DateTimeImmutable('+10 minutes');
        }

        if ($this->attempts === null) {
            $this->attempts = 0;
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRewardOrder(): ?RewardsOrder
    {
        return $this->rewardOrder;
    }

    public function setRewardOrder(?RewardsOrder $rewardOrder): static
    {
        $this->rewardOrder = $rewardOrder;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getCustomer(): ?SonataUserUser
    {
        return $this->customer;
    }

    public function setCustomer(?SonataUserUser $customer): static
    {
        $this->customer = $customer;

        return $this;
    }

    public function getExpiredAt(): ?\DateTimeImmutable
    {
        return $this->expiredAt;
    }

    public function setExpiredAt(\DateTimeImmutable $expiredAt): static
    {
        $this->expiredAt = $expiredAt;

        return $this;
    }

    public function getVerifiedAt(): ?\DateTimeImmutable
    {
        return $this->verifiedAt;
    }

    public function setVerifiedAt(?\DateTimeImmutable $verifiedAt): static
    {
        $this->verifiedAt = $verifiedAt;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getAttempts(): ?int
    {
        return $this->attempts;
    }

    public function setAttempts(int $attempts): static
    {
        $this->attempts = $attempts;

        return $this;
    }

    /**
     * Increment the attempts counter
     */
    public function incrementAttempts(): static
    {
        $this->attempts++;

        return $this;
    }

    /**
     * Check if the OTP is expired
     */
    public function isExpired(): bool
    {
        return $this->expiredAt < new \DateTimeImmutable();
    }

    /**
     * Check if the OTP is verified
     */
    public function isVerified(): bool
    {
        return $this->verifiedAt !== null;
    }

    /**
     * Check if the OTP has reached maximum attempts (3)
     */
    public function hasReachedMaxAttempts(): bool
    {
        return $this->attempts >= 3;
    }
}
