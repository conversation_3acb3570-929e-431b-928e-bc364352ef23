<?php

namespace App\Entity;

use App\Repository\RewardsOrderRepository;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Persistence\Event\LifecycleEventArgs;

#[ORM\Entity(repositoryClass: RewardsOrderRepository::class)]
#[ORM\HasLifecycleCallbacks]
class RewardsOrder
{
    public const STATUS_DELIVERED = 'delivered';
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $number = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $orderNumber = null;

    #[ORM\Column(length: 255)]
    private ?string $state = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?RewardsItem $item = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?SonataUserUser $user = null;

    #[ORM\PrePersist]
    public function setOrderNumberBeforePersist(LifecycleEventArgs $args): void
    {
        $em = $args->getObjectManager();
        $repo = $em->getRepository(self::class);

        $maxOrderNumber = $repo->createQueryBuilder('o')
            ->select('MAX(o.number)')
            ->getQuery()
            ->getSingleScalarResult();

        // If there are no orders yet, $maxOrderNumber will be null, so we start at 1
        $currentMax = $maxOrderNumber ? (int) $maxOrderNumber : 0;
        $this->number = (string) ($currentMax + 1);

        $this->orderNumber = 'ORD-' . str_pad($this->number, 5, '0', STR_PAD_LEFT);
    }

    static function fromArray(
        array $data,
        SonataUserUser $user,
        RewardsItem $item
    ): static {
        $order = new self();
        $order->setState(self::STATUS_PENDING);
        $order->setCreatedAt(new \DateTimeImmutable());
        $order->setUser($user);
        $order->setItem($item);

        return $order;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(string $number): static
    {
        $this->number = $number;

        return $this;
    }

    public function getItem(): ?RewardsItem
    {
        return $this->item;
    }

    public function setItem(?RewardsItem $item): static
    {
        $this->item = $item;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getUser(): ?SonataUserUser
    {
        return $this->user;
    }

    public function setUser(?SonataUserUser $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }

    public function getOrderNumber(): ?string
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(string $orderNumber): static
    {
        $this->orderNumber = $orderNumber;

        return $this;
    }
}
