<?php

namespace App\Entity;

use App\Repository\PaymentDetailsRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[ORM\Entity(repositoryClass: PaymentDetailsRepository::class)]
class PaymentDetails
{
    public const FUEL_TYPE_GASOLINE = 1;
    public const FUEL_TYPE_DIESEL = 2;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'paymentDetails')]
    #[ORM\JoinColumn(nullable: false)]
    private ?SonataUserUser $customer_user = null;

    #[ORM\ManyToOne()]
    #[ORM\JoinColumn(nullable: true)]
    private ?FillingStation $fillingStation = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    private ?string $amount = "0";

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    private ?string $milage = "0";

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    private ?string $points = "0";

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    private ?string $litres = "0";

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    private ?string $unitPrice = "0";

    #[ORM\Column(type: Types::STRING)]
    private ?string $image = "";

    #[ORM\Column(type: Types::TEXT)]
    private ?string $position = "";

    #[ORM\Column(type: Types::FLOAT)]
    private ?float $longitude = 0.0;

    #[ORM\Column(type: Types::FLOAT)]
    private ?float $latitude = 0.0;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?SonataUserUser $created_by = null;

    #[ORM\OneToOne(mappedBy: 'paymentDetails', cascade: ['persist', 'remove'])]
    private ?PaymentDetailsReview $review = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    private ?string $qcState = null;

    #[ORM\OneToMany(mappedBy: 'paymentDetails', targetEntity: PaymentDetailsOCR::class, cascade: ['persist', 'remove'])]
    private Collection $ocrResults;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $isManualEntry = false;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $ocrFailReason = null;

    const QC_STATE_PASSED = 'passed';
    const QC_STATE_CORRECTED = 'corrected';
    const QC_STATE_REJECTED = 'rejected';

    static public function fromArray(
        array $data,
        SonataUserUser $customerUser,
        SonataUserUser $createdBy,
        FillingStation $fillingStation,
    ): static {
        $paymentDetails = new self();
        $paymentDetails->setCustomerUser($customerUser);
        $paymentDetails->setFillingStation($fillingStation);
        $paymentDetails->setAmount($data['amount']);
        $paymentDetails->setMilage($data['milage'] ?? 0);
        $paymentDetails->setPoints($data['amount'] / 100);
        $paymentDetails->setLitres($data['litres'] ?? 0);
        $paymentDetails->setUnitPrice($data['unitPrice'] ?? 0);
        $paymentDetails->setImage($data['image'] ?? '');
        $paymentDetails->setLatitude($data['latitude'] ?? 0.0);
        $paymentDetails->setLongitude($data['longitude'] ?? 0.0);
        $paymentDetails->setPosition(json_encode($data['position'] ?? []));
        $paymentDetails->setCreatedBy($createdBy);

        // Set manual entry flag if provided
        if (isset($data['isManualEntry'])) {
            $paymentDetails->setIsManualEntry((bool) $data['isManualEntry']);
        }

        // Set OCR fail reason if provided
        if (isset($data['ocrFailReason'])) {
            $paymentDetails->setOcrFailReason($data['ocrFailReason']);
        }

        return $paymentDetails;
    }

    public function __construct()
    {
        $this->created_at = new \DateTimeImmutable();
        $this->ocrResults = new ArrayCollection();
    }

    public function __toString()
    {
        return 'Payment Details ' . $this->getNumber();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCustomerUser(): ?SonataUserUser
    {
        return $this->customer_user;
    }

    public function setCustomerUser(?SonataUserUser $customer_user): static
    {
        $this->customer_user = $customer_user;

        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;

        return $this;
    }

    public function getMilage(): ?string
    {
        return $this->milage;
    }

    public function setMilage(string $milage): static
    {
        $this->milage = $milage;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getCreatedBy(): ?SonataUserUser
    {
        return $this->created_by;
    }

    public function setCreatedBy(?SonataUserUser $created_by): static
    {
        $this->created_by = $created_by;

        return $this;
    }

    public function getPoints(): ?string
    {
        return $this->points;
    }

    public function setPoints(string $points): static
    {
        $this->points = $points;

        return $this;
    }

    public function getLitres(): ?string
    {
        return $this->litres;
    }

    public function setLitres(string $litres): static
    {
        $this->litres = $litres;

        return $this;
    }

    public function getUnitPrice(): ?string
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(string $unitPrice): static
    {
        $this->unitPrice = $unitPrice;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): static
    {
        $this->image = $image;

        return $this;
    }

    public function getFillingStation(): ?FillingStation
    {
        return $this->fillingStation;
    }

    public function setFillingStation(?FillingStation $fillingStation): static
    {
        $this->fillingStation = $fillingStation;

        return $this;
    }

    public function getPosition(): ?string
    {
        return $this->position;
    }

    public function setPosition(string $position): static
    {
        $this->position = $position;

        return $this;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function setLongitude(float $longitude): static
    {
        $this->longitude = $longitude;

        return $this;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function setLatitude(float $latitude): static
    {
        $this->latitude = $latitude;

        return $this;
    }

    public function getPositionImageUrl(): string
    {
        return sprintf(
            'https://maps.googleapis.com/maps/api/staticmap?center=%f,%f&zoom=15&size=600x300&maptype=roadmap&markers=color:red%%7Clabel:C%%7C%f,%f&key=%s',
            $this->latitude,
            $this->longitude,
            $this->latitude,
            $this->longitude,
            "AIzaSyDX00SBtCUl-daFyRl3TL2WiCbU3vME_lE"
        );
    }

    public function getNumber(): string
    {
        if ($this->id === null) {
            return 'New Payment Details';
        }

        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getReview(): ?PaymentDetailsReview
    {
        return $this->review;
    }

    public function setReview(PaymentDetailsReview $review): static
    {
        // set the owning side of the relation if necessary
        if ($review->getPaymentDetails() !== $this) {
            $review->setPaymentDetails($this);
        }

        $this->review = $review;

        return $this;
    }

    public function getQcState(): ?string
    {
        return $this->qcState;
    }

    public function setQcState(?string $qcState): static
    {
        $this->qcState = $qcState;

        return $this;
    }

    public function setQcStateFromReview(PaymentDetailsReview $review): void
    {
        if ($review->isRejected()) {
            $this->setQcState(self::QC_STATE_REJECTED);
            return;
        }

        $tolerance = 0.01; // Define a tolerance for comparing decimal values

        if (
            abs($this->getAmount() - $review->getAmount()) < $tolerance &&
            abs($this->getLitres() - $review->getLitres()) < $tolerance &&
            abs($this->getUnitPrice() - $review->getUnitPrice()) < $tolerance
        ) {
            $this->setQcState(self::QC_STATE_PASSED);
        } else {
            $this->setQcState(self::QC_STATE_CORRECTED);
        }
    }

    /**
     * @return Collection<int, PaymentDetailsOCR>
     */
    public function getOcrResults(): Collection
    {
        return $this->ocrResults;
    }

    public function addOcrResult(PaymentDetailsOCR $ocrResult): static
    {
        if (!$this->ocrResults->contains($ocrResult)) {
            $this->ocrResults->add($ocrResult);
            $ocrResult->setPaymentDetails($this);
        }

        return $this;
    }

    public function removeOcrResult(PaymentDetailsOCR $ocrResult): static
    {
        if ($this->ocrResults->removeElement($ocrResult)) {
            // set the owning side to null (unless already changed)
            if ($ocrResult->getPaymentDetails() === $this) {
                $ocrResult->setPaymentDetails(null);
            }
        }

        return $this;
    }

    /**
     * Get OCR result by method
     */
    public function getOcrResultByMethod(int $methodId): ?PaymentDetailsOCR
    {
        foreach ($this->ocrResults as $ocrResult) {
            if ($ocrResult->getOcrMethod()->getId() === $methodId) {
                return $ocrResult;
            }
        }

        return null;
    }

    public function isManualEntry(): ?bool
    {
        return $this->isManualEntry;
    }

    public function setIsManualEntry(bool $isManualEntry): static
    {
        $this->isManualEntry = $isManualEntry;

        return $this;
    }

    public function getOcrFailReason(): ?string
    {
        return $this->ocrFailReason;
    }

    public function setOcrFailReason(?string $ocrFailReason): static
    {
        $this->ocrFailReason = $ocrFailReason;

        return $this;
    }
}
