<?php

namespace App\Entity;

use App\Repository\FillingStationServiceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FillingStationServiceRepository::class)]
class FillingStationService
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $code = null;

    #[ORM\Column(type: "text", nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    private ?SonataMediaMedia $featuredImage = null;

    /**
     * @var Collection<int, FillingStationServiceDetails>
     */
    #[ORM\OneToMany(targetEntity: FillingStationServiceDetails::class, mappedBy: 'service', orphanRemoval: true)]
    private Collection $serviceDetails;

    public function __construct()
    {
        $this->serviceDetails = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getFeaturedImage(): ?SonataMediaMedia
    {
        return $this->featuredImage;
    }

    public function setFeaturedImage(?SonataMediaMedia $featuredImage): static
    {
        $this->featuredImage = $featuredImage;

        return $this;
    }

    /**
     * @return Collection<int, FillingStationServiceDetails>
     */
    public function getServiceDetails(): Collection
    {
        return $this->serviceDetails;
    }

    public function addServiceDetail(FillingStationServiceDetails $serviceDetail): static
    {
        if (!$this->serviceDetails->contains($serviceDetail)) {
            $this->serviceDetails->add($serviceDetail);
            $serviceDetail->setService($this);
        }

        return $this;
    }

    public function removeServiceDetail(FillingStationServiceDetails $serviceDetail): static
    {
        if ($this->serviceDetails->removeElement($serviceDetail)) {
            // set the owning side to null (unless already changed)
            if ($serviceDetail->getService() === $this) {
                $serviceDetail->setService(null);
            }
        }

        return $this;
    }
}
