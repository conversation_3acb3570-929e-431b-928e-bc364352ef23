<?php

namespace App\Entity;

use App\Service\PhoneValidationService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Sonata\UserBundle\Entity\BaseUser;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
// or `Sonata\\UserBundle\\Entity\\BaseUser3` as BaseUser if you upgrade to doctrine/orm ^3

#[ORM\Entity]
#[ORM\Table(name: 'user__user')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_MOBILE_PHONE_CANONICAL', columns: ['mobile_phone_canonical'])]
class SonataUserUser extends BaseUser
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue]
    protected $id;

    #[ORM\Column(type: Types::STRING, length: 255)]
    private ?string $firstname = '';

    #[ORM\Column(type: Types::STRING, length: 255)]
    private ?string $lastname = '';

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $mobilePhone = '';

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $mobilePhoneCanonical = '';

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTime $dob;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $address = '';

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $district = '';

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $idCardNumber = '';

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    private ?string $otp = '';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTime $otpValidUntil;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    private ?SonataMediaMedia $picture = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $points = 0;

    /**
     * @deprecated Use PhoneValidationService::format() instead
     */
    public static function sanitizePhoneNumber(string $phone): string
    {
        // Remove any plus sign that is not at the very start
        // (e.g., if the input contains multiple plus signs)
        $phone = preg_replace('/(?!^)\+/', '', $phone);

        // Keep only digits and a leading '+' (if present)
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // If it starts with '00', replace '00' with '+'
        if (substr($phone, 0, 2) === '00') {
            $phone = '+' . substr($phone, 2);
        }

        // If it doesn't start with '+230', then prepend '+230'
        if (strpos($phone, '+230') !== 0) {
            $phone = '+230' . ltrim($phone, '+');
        }

        return $phone;
    }

    private ?PhoneValidationService $phoneValidationService = null;

    #[Autowire]
    public function setPhoneValidationService(PhoneValidationService $phoneValidationService): void
    {
        $this->phoneValidationService = $phoneValidationService;
    }

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname): self
    {
        $this->firstname = $firstname;
        return $this;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname): self
    {
        $this->lastname = $lastname;
        return $this;
    }

    /**
     * @var Collection<int, PaymentDetails>
     */
    #[ORM\OneToMany(targetEntity: PaymentDetails::class, mappedBy: 'customer_user', orphanRemoval: true)]
    private Collection $paymentDetails;

    /**
     * @var Collection<int, Vehicle>
     */
    #[ORM\OneToMany(targetEntity: Vehicle::class, mappedBy: 'driver', cascade: ['persist', 'remove'])]
    private Collection $vehicles;

    public function __construct()
    {
        $this->paymentDetails = new ArrayCollection();
        $this->vehicles = new ArrayCollection();
    }

    /**
     * @return Collection<int, PaymentDetails>
     */
    public function getPaymentDetails(): Collection
    {
        return $this->paymentDetails;
    }

    public function addPaymentDetail(PaymentDetails $paymentDetail): static
    {
        if (!$this->paymentDetails->contains($paymentDetail)) {
            $this->paymentDetails->add($paymentDetail);
            $paymentDetail->setCustomerUser($this);
        }

        return $this;
    }

    public function removePaymentDetail(PaymentDetails $paymentDetail): static
    {
        if ($this->paymentDetails->removeElement($paymentDetail)) {
            // set the owning side to null (unless already changed)
            if ($paymentDetail->getCustomerUser() === $this) {
                $paymentDetail->setCustomerUser(null);
            }
        }

        return $this;
    }

    public function getMobilePhone(): ?string
    {
        return $this->mobilePhone;
    }

    public function setMobilePhone(string $mobilePhone): self
    {
        $this->mobilePhone = $mobilePhone;

        // Use the PhoneValidationService if available, otherwise fall back to the static method
        if ($this->phoneValidationService !== null) {
            $this->mobilePhoneCanonical = $this->phoneValidationService->format($mobilePhone);
        } else {
            $this->mobilePhoneCanonical = static::sanitizePhoneNumber($mobilePhone);
        }

        return $this;
    }

    public function getDob(): \DateTime|null
    {
        return $this->dob;
    }

    public function setDob(?\DateTime $dob = null): self
    {
        $this->dob = $dob;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;
        return $this;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(string $district): self
    {
        $this->district = $district;
        return $this;
    }

    public function getIdCardNumber(): ?string
    {
        return $this->idCardNumber;
    }

    public function setIdCardNumber(string $idCardNumber): self
    {
        $this->idCardNumber = $idCardNumber;
        return $this;
    }

    /**
     * @return Collection<int, Vehicle>
     */
    public function getVehicles(): Collection
    {
        return $this->vehicles;
    }

    public function addVehicle(Vehicle $vehicle): static
    {
        if (!$this->vehicles->contains($vehicle)) {
            $this->vehicles->add($vehicle);
            $vehicle->setDriver($this);
        }

        return $this;
    }

    public function removeVehicle(Vehicle $vehicle): static
    {
        if ($this->vehicles->removeElement($vehicle)) {
            // set the owning side to null (unless already changed)
            if ($vehicle->getDriver() === $this) {
                $vehicle->setDriver(null);
            }
        }

        return $this;
    }

    public function setFromFlatArray(array $json): static
    {
        $this->setFirstname($json['first_name'] ?? '');
        $this->setLastname($json['last_name'] ?? '');
        $this->setEmail($json['email'] ?? '');
        $this->setUsername($this->getEmail());
        $this->setMobilePhone($json['mobilePhone'] ?? '');
        $this->setDistrict($json['district'] ?? '');
        $this->setIdCardNumber($json['idCardNumber'] ?? '');
        $this->setAddress($json['address'] ?? '');
        $dob = $json['dob'] ?? '';
        try {
            if ($dob) {
                $parsedDOB = \DateTime::createFromFormat('Y-m-d', $json['dob']);
                $this->setDob($parsedDOB);
            }
        } catch (\Exception $e) {
            // ignore error
        }

        $password = $json['password'] ?? '';
        if (!empty($password)) {
            $this->setPlainPassword($password);
        }

        if (
            isset($json['vehicleNumber']) ||
            isset($json['model']) ||
            isset($json['fuel']) ||
            isset($json['vehicleType'])
        ) {
            $vehicles = $this->getVehicles();
            if (count($vehicles) === 0) {
                $vehicle = new Vehicle();
                $vehicle->setDriver($this);
                $vehicles->add($vehicle);
            } else {
                $vehicle = $vehicles[0];
            }
            $vehicle->setLicensePlate($json['vehicleNumber']);
            $vehicle->setModel($json['model']);
            $vehicle->setFuel($json['fuel']);
            $vehicle->setVehicleType($json['vehicleType']);
        }

        return $this;
    }

    public function getOtp(): ?string
    {
        return $this->otp;
    }

    public function setOtp(?string $otp): self
    {
        $this->otp = $otp;
        return $this;
    }

    public function getOtpValidUntil(): ?\DateTime
    {
        return $this->otpValidUntil;
    }

    public function setOtpValidUntil(?\DateTime $otpValidUntil): self
    {
        $this->otpValidUntil = $otpValidUntil;
        return $this;
    }

    public function getMobilePhoneCanonical(): ?string
    {
        return $this->mobilePhoneCanonical;
    }

    public function setMobilePhoneCanonical(?string $mobilePhoneCanonical): self
    {
        // Ensure canonical form is always set via setMobilePhone to maintain consistency
        // However, if direct setting is needed, ensure it's sanitized or comes from a trusted source.
        // For now, let's assume it might be set directly by Doctrine or Sonata internals.
        $this->mobilePhoneCanonical = $mobilePhoneCanonical;
        return $this;
    }

    public function getPicture(): ?SonataMediaMedia
    {
        return $this->picture;
    }

    public function setPicture(?SonataMediaMedia $picture): self
    {
        $this->picture = $picture;
        return $this;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function setPoints(?int $points): self
    {
        $this->points = $points;
        return $this;
    }

    public function addPoints(?int $points): self
    {
        $this->points += $points;
        return $this;
    }
}
