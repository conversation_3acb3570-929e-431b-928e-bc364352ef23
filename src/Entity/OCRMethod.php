<?php

namespace App\Entity;

use App\Repository\OCRMethodRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OCRMethodRepository::class)]
#[ORM\Table(name: 'ocr_methods')]
class OCRMethod
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(options: ["default" => true])]
    private ?bool $isActive = true;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'ocrMethod', targetEntity: PaymentDetailsOCR::class)]
    private Collection $paymentDetailsOCRs;

    #[ORM\OneToMany(mappedBy: 'ocrMethod', targetEntity: OCRComparisonResult::class)]
    private Collection $comparisonResults;

    /**
     * Current system method ID (original implementation)
     */
    const CURRENT_SYSTEM = 1;

    /**
     * Improved system method ID (with follow-up questioning)
     */
    const IMPROVED_SYSTEM = 2;

    public function __construct()
    {
        $this->paymentDetailsOCRs = new ArrayCollection();
        $this->comparisonResults = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function __toString(): string
    {
        return $this->name ?? 'New OCR Method';
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function isIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return Collection<int, PaymentDetailsOCR>
     */
    public function getPaymentDetailsOCRs(): Collection
    {
        return $this->paymentDetailsOCRs;
    }

    public function addPaymentDetailsOCR(PaymentDetailsOCR $paymentDetailsOCR): static
    {
        if (!$this->paymentDetailsOCRs->contains($paymentDetailsOCR)) {
            $this->paymentDetailsOCRs->add($paymentDetailsOCR);
            $paymentDetailsOCR->setOcrMethod($this);
        }

        return $this;
    }

    public function removePaymentDetailsOCR(PaymentDetailsOCR $paymentDetailsOCR): static
    {
        if ($this->paymentDetailsOCRs->removeElement($paymentDetailsOCR)) {
            // set the owning side to null (unless already changed)
            if ($paymentDetailsOCR->getOcrMethod() === $this) {
                $paymentDetailsOCR->setOcrMethod(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, OCRComparisonResult>
     */
    public function getComparisonResults(): Collection
    {
        return $this->comparisonResults;
    }

    public function addComparisonResult(OCRComparisonResult $comparisonResult): static
    {
        if (!$this->comparisonResults->contains($comparisonResult)) {
            $this->comparisonResults->add($comparisonResult);
            $comparisonResult->setOcrMethod($this);
        }

        return $this;
    }

    public function removeComparisonResult(OCRComparisonResult $comparisonResult): static
    {
        if ($this->comparisonResults->removeElement($comparisonResult)) {
            // set the owning side to null (unless already changed)
            if ($comparisonResult->getOcrMethod() === $this) {
                $comparisonResult->setOcrMethod(null);
            }
        }

        return $this;
    }
} 