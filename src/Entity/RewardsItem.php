<?php

namespace App\Entity;

use App\Repository\RewardsItemRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: RewardsItemRepository::class)]
#[ORM\HasLifecycleCallbacks]
class RewardsItem
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column]
    private ?int $points = null;

    #[ORM\Column]
    private ?int $quantity = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updated_at = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    private ?SonataMediaMedia $featuredImage = null;

    /**
     * @var Collection<int, RewardsItemInventoryMovement>
     */
    #[ORM\OneToMany(targetEntity: RewardsItemInventoryMovement::class, mappedBy: 'item', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $inventoryMovements;

    #[ORM\PrePersist]
    public function createInitialInventoryMovement(): void
    {
        if ($this->quantity === null) {
            return;
        }

        $initialMovement = new RewardsItemInventoryMovement();
        $initialMovement->setItem($this);
        $initialMovement->setQuantity($this->quantity);
        $initialMovement->setMovementType(RewardsItemInventoryMovement::MOVEMENT_TYPE_INITIAL);
        $initialMovement->setReason(RewardsItemInventoryMovement::REASON_INITIAL);

        $this->addInventoryMovement($initialMovement);
    }

    public function __construct()
    {
        $this->created_at = new \DateTimeImmutable();
        $this->inventoryMovements = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function setPoints(int $points): static
    {
        $this->points = $points;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updated_at;
    }

    public function setUpdatedAt(?\DateTimeInterface $updated_at): static
    {
        $this->updated_at = $updated_at;

        return $this;
    }

    public function getFeaturedImage(): ?SonataMediaMedia
    {
        return $this->featuredImage;
    }

    public function setFeaturedImage(?SonataMediaMedia $featuredImage): static
    {
        $this->featuredImage = $featuredImage;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    /**
     * @return Collection<int, RewardsItemInventoryMovement>
     */
    public function getInventoryMovements(): Collection
    {
        return $this->inventoryMovements;
    }

    public function addInventoryMovement(RewardsItemInventoryMovement $inventoryMovement): static
    {
        if (!$this->inventoryMovements->contains($inventoryMovement)) {
            $this->inventoryMovements->add($inventoryMovement);
            $inventoryMovement->setItem($this);
        }

        return $this;
    }

    public function removeInventoryMovement(RewardsItemInventoryMovement $inventoryMovement): static
    {
        if ($this->inventoryMovements->removeElement($inventoryMovement)) {
            // set the owning side to null (unless already changed)
            if ($inventoryMovement->getItem() === $this) {
                $inventoryMovement->setItem(null);
            }
        }

        return $this;
    }
}
