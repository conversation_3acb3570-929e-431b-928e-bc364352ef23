<?php

namespace App\Entity;

use App\Repository\RegistrationOTPRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: RegistrationOTPRepository::class)]
#[ORM\HasLifecycleCallbacks]
class RegistrationOTP implements \Stringable
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $mobilePhone = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mobilePhoneCanonical = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $email = null;



    #[ORM\Column(length: 255, nullable: true)]
    private ?string $firstName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $lastName = null;

    #[ORM\Column(length: 6)]
    private ?string $otpCode = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $registrationId = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $expiresAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(options: ["default" => 0])]
    private ?int $attempts = 0;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $verifiedAt = null;

    #[ORM\PrePersist]
    public function setCreatedAtValue(): void
    {
        $this->createdAt = new \DateTimeImmutable();

        if ($this->expiresAt === null) {
            // Set expiration time to 10 minutes from now
            $this->expiresAt = new \DateTimeImmutable('+10 minutes');
        }

        if ($this->attempts === null) {
            $this->attempts = 0;
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMobilePhone(): ?string
    {
        return $this->mobilePhone;
    }

    public function setMobilePhone(string $mobilePhone): static
    {
        $this->mobilePhone = $mobilePhone;

        // Set canonical phone number
        $tempUser = new SonataUserUser();
        $tempUser->setMobilePhone($mobilePhone);
        $this->mobilePhoneCanonical = $tempUser->getMobilePhoneCanonical();

        return $this;
    }

    public function getMobilePhoneCanonical(): ?string
    {
        return $this->mobilePhoneCanonical;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;
        return $this;
    }



    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): static
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): static
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getOtpCode(): ?string
    {
        return $this->otpCode;
    }

    public function setOtpCode(string $otpCode): static
    {
        $this->otpCode = $otpCode;
        return $this;
    }

    public function getRegistrationId(): ?string
    {
        return $this->registrationId;
    }

    public function setRegistrationId(string $registrationId): static
    {
        $this->registrationId = $registrationId;
        return $this;
    }

    public function getExpiresAt(): ?\DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function setExpiresAt(\DateTimeImmutable $expiresAt): static
    {
        $this->expiresAt = $expiresAt;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getAttempts(): ?int
    {
        return $this->attempts;
    }

    public function setAttempts(int $attempts): static
    {
        $this->attempts = $attempts;
        return $this;
    }

    public function incrementAttempts(): static
    {
        $this->attempts++;
        return $this;
    }

    public function getVerifiedAt(): ?\DateTimeImmutable
    {
        return $this->verifiedAt;
    }

    public function setVerifiedAt(?\DateTimeImmutable $verifiedAt): static
    {
        $this->verifiedAt = $verifiedAt;
        return $this;
    }

    /**
     * Check if the OTP is expired
     */
    public function isExpired(): bool
    {
        return $this->expiresAt < new \DateTimeImmutable();
    }

    /**
     * Check if the OTP is verified
     */
    public function isVerified(): bool
    {
        return $this->verifiedAt !== null;
    }

    /**
     * Check if the OTP has reached maximum attempts (3)
     */
    public function hasReachedMaxAttempts(): bool
    {
        return $this->attempts >= 3;
    }

    /**
     * Get the time remaining until expiration in seconds
     */
    public function getExpiresInSeconds(): int
    {
        $now = new \DateTimeImmutable();
        $diff = $this->expiresAt->getTimestamp() - $now->getTimestamp();
        return max(0, $diff);
    }

    public function __toString(): string
    {
        return $this->registrationId ?? '';
    }
}
