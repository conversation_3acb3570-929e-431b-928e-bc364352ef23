<?php

namespace App\Entity;

use App\Repository\LotteryTicketRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LotteryTicketRepository::class)]
class LotteryTicket
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private string $number = '';

    #[ORM\Column]
    private \DateTimeImmutable $created_at;

    #[ORM\ManyToOne(targetEntity: SonataUserUser::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?SonataUserUser $assignTo = null;

    public function __construct()
    {
        $this->created_at = new \DateTimeImmutable();
    }

    public function __toString(): string
    {
        return 'Lottery Ticket #' . $this->number;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(string $number): static
    {
        $this->number = $number;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getAssignTo(): ?SonataUserUser
    {
        return $this->assignTo;
    }

    public function setAssignTo(?SonataUserUser $assignTo): static
    {
        $this->assignTo = $assignTo;

        return $this;
    }
}
