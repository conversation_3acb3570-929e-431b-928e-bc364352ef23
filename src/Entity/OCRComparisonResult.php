<?php

namespace App\Entity;

use App\Repository\OCRComparisonResultRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OCRComparisonResultRepository::class)]
#[ORM\Table(name: 'ocr_comparison_results')]
class OCRComparisonResult
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?PaymentDetails $paymentDetails = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?PaymentDetailsReview $review = null;

    #[ORM\ManyToOne(inversedBy: 'comparisonResults')]
    #[ORM\JoinColumn(nullable: false)]
    private ?OCRMethod $ocrMethod = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isAmountAccurate = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isLitresAccurate = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isUnitPriceAccurate = null;

    #[ORM\Column(nullable: true)]
    private ?bool $mathValidationPassed = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $errorCategory = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $errorDetails = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * Error categories constants
     */
    const ERROR_MISSING_DIGIT = 'missing_digit';
    const ERROR_FORMAT_ERROR = 'format_error';
    const ERROR_WRONG_CHARACTER = 'wrong_character';
    const ERROR_MISSING_FIELD = 'missing_field';
    const ERROR_OTHER = 'other';

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPaymentDetails(): ?PaymentDetails
    {
        return $this->paymentDetails;
    }

    public function setPaymentDetails(?PaymentDetails $paymentDetails): static
    {
        $this->paymentDetails = $paymentDetails;

        return $this;
    }

    public function getReview(): ?PaymentDetailsReview
    {
        return $this->review;
    }

    public function setReview(?PaymentDetailsReview $review): static
    {
        $this->review = $review;

        return $this;
    }

    public function getOcrMethod(): ?OCRMethod
    {
        return $this->ocrMethod;
    }

    public function setOcrMethod(?OCRMethod $ocrMethod): static
    {
        $this->ocrMethod = $ocrMethod;

        return $this;
    }

    public function isAmountAccurate(): ?bool
    {
        return $this->isAmountAccurate;
    }

    public function setIsAmountAccurate(?bool $isAmountAccurate): static
    {
        $this->isAmountAccurate = $isAmountAccurate;

        return $this;
    }

    public function isLitresAccurate(): ?bool
    {
        return $this->isLitresAccurate;
    }

    public function setIsLitresAccurate(?bool $isLitresAccurate): static
    {
        $this->isLitresAccurate = $isLitresAccurate;

        return $this;
    }

    public function isUnitPriceAccurate(): ?bool
    {
        return $this->isUnitPriceAccurate;
    }

    public function setIsUnitPriceAccurate(?bool $isUnitPriceAccurate): static
    {
        $this->isUnitPriceAccurate = $isUnitPriceAccurate;

        return $this;
    }

    public function isMathValidationPassed(): ?bool
    {
        return $this->mathValidationPassed;
    }

    public function setMathValidationPassed(?bool $mathValidationPassed): static
    {
        $this->mathValidationPassed = $mathValidationPassed;

        return $this;
    }

    public function getErrorCategory(): ?string
    {
        return $this->errorCategory;
    }

    public function setErrorCategory(?string $errorCategory): static
    {
        $this->errorCategory = $errorCategory;

        return $this;
    }

    public function getErrorDetails(): ?string
    {
        return $this->errorDetails;
    }

    public function setErrorDetails(?string $errorDetails): static
    {
        $this->errorDetails = $errorDetails;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get overall accuracy as boolean (true if all fields are accurate)
     */
    public function isOverallAccurate(): bool
    {
        return $this->isAmountAccurate && $this->isLitresAccurate && $this->isUnitPriceAccurate;
    }
} 