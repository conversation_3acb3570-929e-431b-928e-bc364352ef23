<?php

namespace App\Entity;

use App\Repository\PaymentDetailsOCRRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PaymentDetailsOCRRepository::class)]
#[ORM\Table(name: 'payment_details_ocr')]
class PaymentDetailsOCR
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'paymentDetailsOCRs')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PaymentDetails $paymentDetails = null;

    #[ORM\ManyToOne(inversedBy: 'paymentDetailsOCRs')]
    #[ORM\JoinColumn(nullable: false)]
    private ?OCRMethod $ocrMethod = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $amount = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $litres = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $unitPrice = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $fuelType = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $transactionDate = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $rawResponse = null;

    #[ORM\Column(nullable: true)]
    private ?int $processingTime = null;

    #[ORM\Column(options: ["default" => false])]
    private ?bool $followUpNeeded = false;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $followUpResponse = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPaymentDetails(): ?PaymentDetails
    {
        return $this->paymentDetails;
    }

    public function setPaymentDetails(?PaymentDetails $paymentDetails): static
    {
        $this->paymentDetails = $paymentDetails;

        return $this;
    }

    public function getOcrMethod(): ?OCRMethod
    {
        return $this->ocrMethod;
    }

    public function setOcrMethod(?OCRMethod $ocrMethod): static
    {
        $this->ocrMethod = $ocrMethod;

        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(?string $amount): static
    {
        $this->amount = $amount;

        return $this;
    }

    public function getLitres(): ?string
    {
        return $this->litres;
    }

    public function setLitres(?string $litres): static
    {
        $this->litres = $litres;

        return $this;
    }

    public function getUnitPrice(): ?string
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(?string $unitPrice): static
    {
        $this->unitPrice = $unitPrice;

        return $this;
    }

    public function getFuelType(): ?string
    {
        return $this->fuelType;
    }

    public function setFuelType(?string $fuelType): static
    {
        $this->fuelType = $fuelType;

        return $this;
    }

    public function getTransactionDate(): ?\DateTimeInterface
    {
        return $this->transactionDate;
    }

    public function setTransactionDate(?\DateTimeInterface $transactionDate): static
    {
        $this->transactionDate = $transactionDate;

        return $this;
    }

    public function getRawResponse(): ?array
    {
        return $this->rawResponse;
    }

    public function setRawResponse(?array $rawResponse): static
    {
        $this->rawResponse = $rawResponse;

        return $this;
    }

    public function getProcessingTime(): ?int
    {
        return $this->processingTime;
    }

    public function setProcessingTime(?int $processingTime): static
    {
        $this->processingTime = $processingTime;

        return $this;
    }

    public function isFollowUpNeeded(): ?bool
    {
        return $this->followUpNeeded;
    }

    public function setFollowUpNeeded(bool $followUpNeeded): static
    {
        $this->followUpNeeded = $followUpNeeded;

        return $this;
    }

    public function getFollowUpResponse(): ?array
    {
        return $this->followUpResponse;
    }

    public function setFollowUpResponse(?array $followUpResponse): static
    {
        $this->followUpResponse = $followUpResponse;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Checks if the mathematical relationship between amount, litres, and unitPrice is valid
     * 
     * @param float $tolerance The allowed tolerance for the comparison
     * @return bool
     */
    public function isMathematicallyValid(float $tolerance = 0.25): bool
    {
        // If any value is missing, we can't validate
        if ($this->amount === null || $this->litres === null || $this->unitPrice === null) {
            return false;
        }

        $amount = (float) $this->amount;
        $litres = (float) $this->litres;
        $unitPrice = (float) $this->unitPrice;
        
        $calculatedAmount = $litres * $unitPrice;
        
        return abs($calculatedAmount - $amount) < $tolerance;
    }
} 