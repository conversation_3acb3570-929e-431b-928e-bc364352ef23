<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Show\ShowMapper;

class RewardsOrderAdmin extends AbstractAdmin
{
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'DESC';
        $sortValues[DatagridInterface::SORT_BY] = 'id';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('orderNumber')
            ->add('created_at')
            ->add('state')
            ->add('user')
            ->add('item.name')
            ->add('item.points')
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('orderNumber')
            ->add('created_at')
            ->add('state')
            ->add('user')
            ->add('item')
            ->add('item.points')
        ;
    }
}
