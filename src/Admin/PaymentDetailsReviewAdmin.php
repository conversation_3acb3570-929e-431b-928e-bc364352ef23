<?php

namespace App\Admin;

use App\Entity\PaymentDetailsReview;
use App\Entity\PaymentDetails;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\FieldDescription\FieldDescriptionInterface;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;

class PaymentDetailsReviewAdmin extends AbstractAdmin
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'DESC';
        $sortValues[DatagridInterface::SORT_BY] = 'id';
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        // Add quality_control as a collection-level action (not tied to a specific object)
        $collection->add('quality_control');
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('id')
            ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('amount', FieldDescriptionInterface::TYPE_CURRENCY)
            ->add('litres', FieldDescriptionInterface::TYPE_CURRENCY)
            ->add('unitPrice', FieldDescriptionInterface::TYPE_CURRENCY)
            ->add('reviewStatus', 'choice', [
                'choices' => [
                    'Pending Review' => PaymentDetailsReview::REVIEW_STATUS_PENDING,
                    'Approved' => PaymentDetailsReview::REVIEW_STATUS_APPROVED,
                    'Rejected' => PaymentDetailsReview::REVIEW_STATUS_REJECTED,
                ],
            ])
            ->add('reviewer')
            ->add('reviewedAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('paymentDetails')
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'show' => [],
                ],
            ]);
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('id')
            ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('amount')
            ->add('litres')
            ->add('unitPrice')
            ->add('reviewStatus', 'choice', [
                'choices' => [
                    'Pending Review' => PaymentDetailsReview::REVIEW_STATUS_PENDING,
                    'Approved' => PaymentDetailsReview::REVIEW_STATUS_APPROVED,
                    'Rejected' => PaymentDetailsReview::REVIEW_STATUS_REJECTED,
                ],
            ])
            ->add('reviewer')
            ->add('reviewedAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('updatedAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('reviewNotes')
            ->add('originalValues', 'json', [
                'template' => 'admin/show_json.html.twig',
            ])
            ->add('modifiedValues', 'json', [
                'template' => 'admin/show_json.html.twig',
            ])
            ->add('reviewHistory', 'json', [
                'template' => 'admin/show_json.html.twig',
            ])
            ->add('paymentDetails')
            ->add('paymentDetails.image', null, [
                'template' => 'admin/show_image.html.twig',
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('amount', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('litres', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('unitPrice', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('rejected', CheckboxType::class, [
                'required' => false,
            ])
        ;
    }

    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    public function createNewInstance(): Object
    {
        $instance = parent::createNewInstance();

        // Fetch the most recently created PaymentDetails entity
        $paymentDetails = $this->entityManager
            ->getRepository(PaymentDetails::class)
            ->findOneBy(['qcState' => null], ['created_at' => 'DESC']);

        if ($paymentDetails) {
            $instance->setPaymentDetails($paymentDetails);
            $instance->setAmount($paymentDetails->getAmount());
            $instance->setLitres($paymentDetails->getLitres());
            $instance->setUnitPrice($paymentDetails->getUnitPrice());
        }

        return $instance;
    }

    public function prePersist($object): void
    {
        /** @var PaymentDetails */
        $paymentDetails = $object->getPaymentDetails();
        $paymentDetails->setQcStateFromReview($object);
        $this->entityManager->persist($paymentDetails);
    }

    protected function getAccessMapping(): array
    {
        return [
            'quality_control' => 'LIST',
        ];
    }
}
