<?php

namespace App\Admin;

use App\Entity\ImageValidationTrainingData;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\AdminBundle\FieldDescription\FieldDescriptionInterface;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;

class ImageValidationTrainingDataAdmin extends AbstractAdmin
{
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'DESC';
        $sortValues[DatagridInterface::SORT_BY] = 'createdAt';
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('isValid', null, [
                'label' => 'Validation Status'
            ])
            ->add('mathematicalValidation', null, [
                'label' => 'Mathematical Validation'
            ])
            ->add('includeInExport', null, [
                'label' => 'Include in Export'
            ])
            ->add('createdAt', null, [
                'label' => 'Created At'
            ]);
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('imagePath', null, [
                'template' => 'admin/image_validation_training_data/list_image.html.twig',
                'label' => 'Image'
            ])
            ->addIdentifier('id')
            ->add('payemtDetailsId', null, [
                'label' => 'Payment Details'
            ])
            ->add('isValid', null, [
                'label' => 'Validation Status',
                'editable' => true
            ])
            ->add('mathematicalValidation', null, [
                'label' => 'Mathematical Validation',
                'editable' => true
            ])
            ->add('includeInExport', null, [
                'label' => 'Include in Export',
                'editable' => true
            ])
            ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME, [
                'label' => 'Created At'
            ])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'show' => [],
                    'edit' => [],
                ],
            ]);
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('imagePath', null, [
                'template' => 'admin/image_validation_training_data/show_image.html.twig',
                'label' => 'Image'
            ])
            ->add('id')
            ->add('payemtDetailsId', null, [
                'label' => 'Payment Details'
            ])
            ->add('isValid', null, [
                'label' => 'Validation Status'
            ])
            ->add('mathematicalValidation', null, [
                'label' => 'Mathematical Validation'
            ])
            ->add('validationNote', null, [
                'label' => 'Validation Note'
            ])
            ->add('includeInExport', null, [
                'label' => 'Include in Export'
            ])
            ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME, [
                'label' => 'Created At'
            ])
            ->add('updatedAt', FieldDescriptionInterface::TYPE_DATETIME, [
                'label' => 'Updated At'
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with('Image', ['class' => 'col-md-6'])
            ->add('imagePath', null, [
                'attr' => ['readonly' => true],
                'required' => true,
                'label' => 'Image Path'
            ])
            ->end()
            ->with('Validation', ['class' => 'col-md-6'])
            ->add('isValid', CheckboxType::class, [
                'required' => false,
                'label' => 'Is Valid'
            ])
            ->add('mathematicalValidation', CheckboxType::class, [
                'required' => false,
                'label' => 'Mathematical Validation'
            ])
            ->add('validationNote', TextareaType::class, [
                'required' => false,
                'label' => 'Validation Note'
            ])
            ->add('includeInExport', CheckboxType::class, [
                'required' => false,
                'label' => 'Include in Export'
            ])
            ->end();
    }

    protected function configureBatchActions(array $actions): array
    {
        $actions['toggle_include_in_export'] = [
            'label' => 'Toggle Include in Export',
            'ask_confirmation' => true
        ];

        return $actions;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->remove('create');
    }
}
