<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\MediaBundle\Form\Type\MediaType;
use Sonata\Form\Type\CollectionType;

class ExclusiveOfferAdmin  extends AbstractAdmin
{
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'ASC';
        $sortValues[DatagridInterface::SORT_BY] = 'publishedAt';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('featuredImage', null, [
                'template' => 'admin/list_thumb.html.twig',
            ])
            ->addIdentifier('title')
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'show' => [],
                    'edit' => [],
                ],
            ]);;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('featuredImage', null, [
                'template' => 'admin/show_image.html.twig',
            ])
            ->add('publishedAt')
            ->add('title')
            ->add('description')
            ->add('url')
            ->add('active')
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with('General', ['class' => 'col-md-8'])
            ->add('publishedAt')
            ->add('title')
            ->add('description')
            ->add('url')
            ->end()
            ->with('Status', ['class' => 'col-md-4'])
            ->add('active', CheckboxType::class, [
                'required' => false,
            ])
            ->end()
            ->with('Featured Image', ['class' => 'col-md-8'])
            ->add('featuredImage', MediaType::class, [
                'provider' => 'sonata.media.provider.image',
                'context' => 'default',
            ])
            ->end()
        ;
    }
}
