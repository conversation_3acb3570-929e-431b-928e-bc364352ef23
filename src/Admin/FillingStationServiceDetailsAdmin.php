<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\MediaBundle\Form\Type\MediaType;

class FillingStationServiceDetailsAdmin extends AbstractAdmin
{
    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('service', null, [
                'required' => true,
            ])
            ->add('description', null, [
                'required' => false,
                'help' => 'Common description to all filling stations',
            ])
            ->add('featuredImage', MediaType::class, [
                'provider' => 'sonata.media.provider.image',
                'context' => 'default',
            ])
            ->end()
        ;
    }
}
