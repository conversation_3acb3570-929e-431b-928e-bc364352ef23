<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class LotteryTicketAdmin extends AbstractAdmin
{
    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('number', TextType::class)
            ->add('assignTo')
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $datagrid): void
    {
        $datagrid->add('number');
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list->addIdentifier('number')
            ->add('assignTo');
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show->add('number');
    }
}
