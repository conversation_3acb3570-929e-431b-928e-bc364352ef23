<?php

namespace App\Admin;

use App\Entity\PaymentDetails;
use App\Entity\SonataUserUser;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\FieldDescription\FieldDescriptionInterface;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\FieldDescription\FieldDescription;
use Sonata\MediaBundle\Form\Type\MediaType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Filter\FilterInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Doctrine\ORM\EntityManagerInterface;

class PaymentDetailsAdmin extends AbstractAdmin
{
    private EntityManagerInterface $entityManager;

    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'DESC';
        $sortValues[DatagridInterface::SORT_BY] = 'id';
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->add('review', $this->getRouterIdParameter() . '/review');
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('number')
            ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME)
            ->add('fillingStation')
            ->add('customerUser')
            ->add('amount', FieldDescriptionInterface::TYPE_CURRENCY)
            ->add('isManualEntry', null, [
                'label' => 'Manual Entry',
            ])
            ->add('qcState', null, [
                'label' => 'Review Status',
                'template' => 'admin/payment_details/qc_state_list_field.html.twig',
            ])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'show' => [],
                    'review' => [
                        'template' => 'admin/payment_details/list__action_review.html.twig',
                    ],
                ],
            ]);
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show->with('Payment Details', ['class' => 'col-md-12']);
        /** @var PaymentDetails */
        $payment = $this->getSubject();
        if (!empty($payment->getImage())) {
            $show->add('image', null, [
                'label' => 'Image',
                'template' => 'admin/image.html.twig',
            ]);
        }

        if (
            $payment->getLatitude() != 0.0 &&
            $payment->getLongitude() != 0.0
        ) {
            $show->add('positionImageUrl', null, [
                'label' => 'Geolocation',
                'template' => 'admin/image.html.twig',
            ]);
        }

        if ($this->getSubject())
            $show
                ->add('createdAt', FieldDescriptionInterface::TYPE_DATETIME)
                ->add('customerUser')
                ->add('fillingStation')
                ->add('amount')
                ->add('milage')
                ->add('points')
                ->add('litres')
                ->add('unitPrice')
                ->add('isManualEntry')
                ->add('ocrFailReason')
                ->add('createdBy')
            ;
        $show->end();

        // The OCR Results section has been removed as it doesn't display well in the standard show layout
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('fillingStation', ModelAutocompleteType::class, [
                'property' => 'name',
                'required' => true,
                'btn_add' => false,
            ])
            ->add('customer_user', ModelAutocompleteType::class, [
                'property' => 'username',
                'required' => true,
                'btn_add' => false,
            ])
            ->add('amount', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('points', NumberType::class, [
                'required' => true,
                'scale' => 0,
            ])
            ->add('milage', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('litres', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('unitPrice', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('litres', TextType::class)
            ->add('unitPrice', TextType::class)
            ->add('image', MediaType::class, [
                'provider' => 'sonata.media.provider.image',
                'context' => 'default',
            ])
            ->add('latitude', TextType::class, [
                'required' => false,
            ])
            ->add('longitude', TextType::class, [
                'required' => false,
            ])
            ->add('isManualEntry', null, [
                'required' => false,
                'label' => 'Manual Entry',
            ])
            ->add('ocrFailReason', TextType::class, [
                'required' => false,
                'label' => 'OCR Fail Reason',
            ])
            ->add('created_by', ModelAutocompleteType::class, [
                'property' => 'name',
                'required' => true,
                'btn_add' => false,
                'callback' => function ($admin, $property, $value) {
                    $datagrid = $admin->getDatagrid();
                    $queryBuilder = $datagrid->getQuery();
                    $rootAlias = $queryBuilder->getRootAliases()[0];
                    $queryBuilder
                        ->andWhere($rootAlias . '.roles LIKE :role')
                        ->setParameter('role', '%ROLE_ADMIN%');
                },
            ])
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('created_by', null, [
                'show_filter' => true,
                'label' => 'Created By',
                'field_type' => ModelAutocompleteType::class,
                'field_options' => [
                    'property' => 'email',
                    'minimum_input_length' => 3,
                    'class' => SonataUserUser::class,
                ]
            ])
            ->add('customer_user', null, [
                'show_filter' => true,
                'label' => 'Customer',
                'field_type' => ModelAutocompleteType::class,
                'field_options' => [
                    'property' => 'email',
                    'minimum_input_length' => 3,
                    'class' => SonataUserUser::class,
                ]
            ])
            ->add('qcState', ChoiceFilter::class, [
                'show_filter' => true,
                'label' => 'Review Status',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Passed' => PaymentDetails::QC_STATE_PASSED,
                        'Corrected' => PaymentDetails::QC_STATE_CORRECTED,
                        'Rejected' => PaymentDetails::QC_STATE_REJECTED,
                    ],
                ]
            ])
            ->add('isManualEntry', null, [
                'show_filter' => true,
                'label' => 'Manual Entry',
            ]);
    }
}
