<?php

namespace App\Admin;

use App\Entity\SonataUserUser;
use Sonata\UserBundle\Admin\Model\UserAdmin as BaseUserAdmin;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Validator\Constraints\NotBlank;

final class UserAdmin extends BaseUserAdmin
{
    protected function configureFormFields(FormMapper $form): void
    {
        parent::configureFormFields($form);

        $form->with('Account Details')
            ->add('mobilePhone', TextType::class, [
                'label' => 'Mobile Phone',
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->end();
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show->with('Account Details', ['label' => 'Account Information']);

        /** @var SonataUserUser */
        $user = $this->getSubject();
        if (!empty($user->getPicture())) {
            $show->add('picture', null, [
                'label' => 'Profile pricture',
                'template' => 'admin/show_image.html.twig',
            ]);
        }

        $show
            ->add('username')
            ->add('email')
            ->add('mobilePhoneCanonical', null, ['label' => 'phone'])
            ->end()

            ->with('Personal Information', ['label' => 'Personal Information'])
            ->add('firstName', null, ['label' => 'First Name'])
            ->add('lastName', null, ['label' => 'Last Name'])
            ->add('dob', null, ['label' => 'Date of Birth'])
            ->add('address', null, ['label' => 'Address'])
            ->add('district', null, ['label' => 'District'])
            ->add('points', null, ['label' => 'Points'])
            ->add('ID Card Number', null, ['label' => 'ID Card Number'])
            ->end()

            ->with('Vehicle Information', ['label' => 'Vehicle Information'])
            ->add('vehicles', null, [
                'template' => 'admin/user/vehicles_show.html.twig'
            ])
            ->end()
        ;
    }
}
