<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\MediaBundle\Form\Type\MediaType;
use Sonata\Form\Type\CollectionType;
use Sonata\AdminBundle\Datagrid\DatagridMapper;

class FillingStationAdmin extends AbstractAdmin
{
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::PAGE] = 1;
        $sortValues[DatagridInterface::SORT_ORDER] = 'ASC';
        $sortValues[DatagridInterface::SORT_BY] = 'name';
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->remove('show');
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter->add('name');
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('featuredImage', null, [
                'template' => 'admin/list_thumb.html.twig',
            ])
            ->addIdentifier('name')
            ->add('phone')
            ->add('address')
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'edit' => [],
                ],
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with('General', ['class' => 'col-md-8'])
            ->add('name')
            ->add('address')
            ->add('phone')
            ->add('geolocation', null, ['attr' => ['iomlMapPicker' => true]])
            ->end()
            ->with('Status', ['class' => 'col-md-4'])
            ->add('active', CheckboxType::class, [
                'required' => false,
            ])
            ->end()
            ->with('Featured Image', ['class' => 'col-md-8'])
            ->add('featuredImage', MediaType::class, [
                'provider' => 'sonata.media.provider.image',
                'context' => 'default',
            ])
            ->end()
            ->with('Services', ['class' => 'col-md-8'])
            ->add('services', ModelType::class, [
                'multiple' => true,
                'expanded' => true,
                'btn_add' => false,
            ])
            ->add('serviceDetails')
            ->add('serviceDetails', CollectionType::class, [
                'by_reference' => false,
                'help' => 'Service details will overwrite global service info',
            ], [
                'edit' => 'inline',
                'inline' => 'table',
            ])
            ->end()
        ;
    }
}
