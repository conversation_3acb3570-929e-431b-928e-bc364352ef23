<?php

namespace App\Command;

use App\Entity\OCRMethod;
use App\Repository\OCRMethodRepository;
use App\Repository\PaymentDetailsRepository;
use App\Service\OCRComparisonService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Psr\Log\LoggerInterface;

#[AsCommand(
    name: 'app:process-missing-improved-ocr',
    description: 'Process payment details that are missing improved OCR results',
)]
class ProcessMissingImprovedOCRCommand extends Command
{
    private $paymentDetailsRepository;
    private $ocrMethodRepository;
    private $ocrComparisonService;
    private $entityManager;
    private $logger;

    public function __construct(
        PaymentDetailsRepository $paymentDetailsRepository,
        OCRMethodRepository $ocrMethodRepository,
        OCRComparisonService $ocrComparisonService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        parent::__construct();
        
        $this->paymentDetailsRepository = $paymentDetailsRepository;
        $this->ocrMethodRepository = $ocrMethodRepository;
        $this->ocrComparisonService = $ocrComparisonService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limit the number of payment details to process', 500);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Processing Missing Improved OCR Results');

        $limit = (int) $input->getOption('limit');

        // Find payment details with Current System results but no Improved System results
        $paymentDetails = $this->findPaymentDetailsToProcess($limit);
        
        if (empty($paymentDetails)) {
            $io->warning('No payment details found for processing.');
            return Command::SUCCESS;
        }

        $io->note(sprintf('Found %d payment details to process', count($paymentDetails)));
        
        // Process payment details
        $io->section('Processing payment details');
        $successCount = 0;
        $failureCount = 0;
        
        $progressBar = new ProgressBar($output, count($paymentDetails));
        $progressBar->setFormat(
            "%current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s% \n%message%"
        );
        
        foreach ($paymentDetails as $paymentDetail) {
            $progressBar->setMessage(sprintf('Processing payment detail #%d', $paymentDetail->getId()));
            $progressBar->display();
            
            try {
                $this->ocrComparisonService->processAndCompare($paymentDetail);
                $successCount++;
                $this->entityManager->clear(); // Clear entity manager to avoid memory leaks
            } catch (\Exception $e) {
                $this->logger->error(sprintf(
                    'Error processing payment detail #%d: %s',
                    $paymentDetail->getId(),
                    $e->getMessage()
                ));
                $failureCount++;
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $io->newLine(2);
        
        // Display summary
        $io->section('Processing Summary');
        $io->success(sprintf(
            'Processed %d payment details (%d successful, %d failed)',
            count($paymentDetails),
            $successCount,
            $failureCount
        ));
        
        return Command::SUCCESS;
    }

    /**
     * Find payment details that have Current System results but no Improved System results
     */
    private function findPaymentDetailsToProcess(int $limit): array
    {
        $currentMethod = $this->ocrMethodRepository->find(OCRMethod::CURRENT_SYSTEM);
        $improvedMethod = $this->ocrMethodRepository->find(OCRMethod::IMPROVED_SYSTEM);
        
        if (!$currentMethod || !$improvedMethod) {
            $this->logger->error('OCR methods not found in the database');
            return [];
        }
        
        // Find payment details with current method results
        $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
            ->where('pd.qcState IS NOT NULL')
            ->join('pd.ocrResults', 'ocr_current', 'WITH', 'ocr_current.ocrMethod = :currentMethod')
            ->setParameter('currentMethod', $currentMethod);
        
        // Exclude payment details that already have improved method results
        $improvedSubquery = $this->entityManager->createQueryBuilder()
            ->select('IDENTITY(ocr_improved.paymentDetails)')
            ->from('App\Entity\PaymentDetailsOCR', 'ocr_improved')
            ->where('ocr_improved.ocrMethod = :improvedMethod');
        
        $qb->andWhere($qb->expr()->notIn('pd.id', $improvedSubquery->getDQL()))
           ->setParameter('improvedMethod', $improvedMethod)
           ->setMaxResults($limit);
        
        return $qb->getQuery()->getResult();
    }
} 