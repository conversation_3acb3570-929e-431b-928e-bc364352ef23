<?php

namespace App\Command;

use App\Entity\FillingStation;
use App\Entity\FillingStationService;
use App\Entity\SonataMediaMedia;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\MediaBundle\Model\MediaManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\File\File;

#[AsCommand(name: 'app:filling-station:seed', description: 'Seed filling station data')]
class SeedFillingStationCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private MediaManagerInterface $mediaManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $jsonFilePath = './public/uploads/assets/images/stations/filling_stations.json';
        if (!file_exists($jsonFilePath)) {
            $output->writeln('JSON file not found.');
            return Command::FAILURE;
        }

        $jsonData = file_get_contents($jsonFilePath);
        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $output->writeln('Error decoding JSON file: ' . json_last_error_msg());
            return Command::FAILURE;
        }

        $services = $this->entityManager
            ->getRepository(FillingStationService::class)->findAll();

        foreach ($data as $item) {
            $fillingStation = new FillingStation();
            $fillingStation->setName($item['station']);
            $fillingStation->setAddress($item['address']);
            $fillingStation->setPhone($item['phone']);
            $fillingStation->setGeolocation($item['geo']);
            foreach ($services as $service) {
                if (
                    isset($item[$service->getCode()]) &&
                    $item[$service->getCode()] === 'Y'
                ) {
                    $fillingStation->addService($service);
                }
            }

            if (isset($item['cover']) && !empty($item['cover'])) {
                $imagePath = 'public/uploads/' . $item['cover'];
                if (file_exists($imagePath)) {
                    $media = new SonataMediaMedia();
                    $media->setBinaryContent(new File($imagePath));
                    $media->setProviderName('sonata.media.provider.image');
                    $media->setContext('default');
                    $this->mediaManager->save($media);

                    $fillingStation->setFeaturedImage($media);
                } else {
                    $output->writeln('Image file not found: ' . $imagePath);
                }
            }
            $this->entityManager->persist($fillingStation);
        }

        $this->entityManager->flush();

        $output->writeln('JSON file decoded successfully.');
        return Command::SUCCESS;
    }
}
