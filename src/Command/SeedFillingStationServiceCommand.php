<?php

namespace App\Command;

use App\Entity\FillingStationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


#[AsCommand(name: 'app:filling-station-service:seed', description: 'Seed filling station services')]
class SeedFillingStationServiceCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $data = [
            "petrol" => "Petrol",
            "diesel" => "Diesel",
            "gaz" => "Gaz",
            "airPressure" => "Air Pressure",
            "carWash" => "Car Wash",
            "store" => "Store",
            "greasePump" => "GreasePump",
            "water" => "Water",
            "escale" => "Escale",
            "ice" => "Ice",
        ];
        foreach ($data as $code => $item) {
            $service = new FillingStationService();
            $service->setName($item);
            $service->setCode($code);
            $this->entityManager->persist($service);
        }

        $this->entityManager->flush();

        $output->writeln('JSON file decoded successfully.');
        return Command::SUCCESS;
    }
}
