<?php

namespace App\Command;

use App\FuelMeterScanner;
use App\ImagePreprocessor;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'app:benchmark:fuel-meter-scanner',
    description: 'Add a short description for your command',
)]
class BenchmarkFuelMeterScannerCommand extends Command
{
    /**
     * @var FuelMeterScanner
     */
    private $scanner;

    /**
     * @var ParameterBagInterface
     */
    private $parameterBag;

    public function __construct(FuelMeterScanner $scanner, ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->scanner = $scanner;
        $this->parameterBag = $parameterBag;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $path = './public/uploads/cmd/fuel_meter_scanner/data.json';
        $data = json_decode(file_get_contents($path), true);

        $io->title('Benchmarking Fuel Meter Scanner');
        $resultHeaders = [
            'image',
            'refAmount',
            'amount',
            'refLitres',
            'litres',
            'refUnitPrice',
            'unitPrice',
            'status'
        ];
        $results = [];

        $progressBar = $io->createProgressBar(count($data));
        $progressBar->start();

        /** @var BenchmarkInputData[] */
        foreach ($data as $item) {
            $benchmarkData = BenchmarkInputData::fromArray($item);
            $imagePath = $benchmarkData->getImagePath();

            // Convert image to base64
            $imageData = file_get_contents('./public/' . $imagePath);
            if ($imageData === false) {
                $io->error(sprintf('Failed to read image: %s', $imagePath));
                $progressBar->advance();
                continue;
            }

            $case = [
                [
                    'title' => 'chatGPT 4o',
                    'imagePreprocessorOptions' => [],
                    'scannerOptions' => ['model' => 'gpt-4o']
                ]
            ];
            $base64Image = base64_encode($imageData);
            $preprocessor = new ImagePreprocessor();
            $preprocessedImage = $preprocessor->preprocess($base64Image, ['mode' => 'local']);
            // $binaryImage = $preprocessor->preprocess(
            //     $base64Image,
            //     ['params' => ['binary' => 'true']]
            // );

            // Get the Gemini API key
            $geminiApiKey = $this->parameterBag->get('env(GEMINI_API_KEY)');

            // Try to use Gemini API first
            $tempFilePath = './public/uploads/temp_' . uniqid() . '.jpg';
            file_put_contents($tempFilePath, $imageData);

            try {
                // Use Gemini API if API key is available
                if (!empty($geminiApiKey)) {
                    $extractedData = $this->scanner->scanWithGemini($tempFilePath, $geminiApiKey);

                    // If result is null, fall back to OpenAI
                    if ($extractedData === null) {
                        $extractedData = $this->scanner->scan($base64Image);
                    }
                } else {
                    // Fall back to OpenAI if Gemini API key is not available
                    $extractedData = $this->scanner->scan($base64Image);
                }
            } catch (\Exception $e) {
                // Fall back to OpenAI on error
                $extractedData = $this->scanner->scan($base64Image);
            } finally {
                // Clean up temporary file
                if (file_exists($tempFilePath)) {
                    unlink($tempFilePath);
                }
            }
            // $extractedData2 = $this->scanner->scan(
            //     $binaryImage,
            //     FuelMeterScanner::PREPROCESS_DISABLED
            // );
            // $extractedData3 = $this->scanner->scanWithOllama($base64Image);
            $results[] = [
                [
                    'title' => 'Live',
                    'url' => $imagePath,
                    'amount' => $benchmarkData->getCurrentAmount(),
                    'litres' => $benchmarkData->getCurrentLitres(),
                    'unitPrice' => $benchmarkData->getCurrentUnitPrice(),
                    'status' => $benchmarkData->getAmount() === $benchmarkData->getCurrentAmount()
                        && $benchmarkData->getLitres() === $benchmarkData->getCurrentLitres()
                        && $benchmarkData->getUnitPrice() === $benchmarkData->getCurrentUnitPrice()
                        ? 'OK'
                        : 'FAIL'
                ],
                [
                    'title' => 'ChatGPT 4o (Custom Filters)',
                    'url' => 'data:image/png;base64,' . $preprocessedImage,
                    'amount' => $extractedData['amount'],
                    'litres' => $extractedData['litres'],
                    'unitPrice' => $extractedData['unit_price'],
                    'status' => $benchmarkData->getAmount() === $extractedData['amount']
                        && $benchmarkData->getLitres() === $extractedData['litres']
                        && $benchmarkData->getUnitPrice() === $extractedData['unit_price']
                        ? 'OK'
                        : 'FAIL'
                ],
                // [
                //     'title' => 'ChatGPT 4o (Binary)',
                //     'url' => 'data:image/png;base64,' . $binaryImage,
                //     'amount' => $extractedData2['amount'],
                //     'litres' => $extractedData2['litres'],
                //     'unitPrice' => $extractedData2['unit_price'],
                //     'status' => $benchmarkData->getAmount() === $extractedData2['amount']
                //         && $benchmarkData->getLitres() === $extractedData2['litres']
                //         && $benchmarkData->getUnitPrice() === $extractedData2['unit_price']
                //         ? 'OK'
                //         : 'FAIL'
                // ],
                // [
                //     'title' => 'Ollama (Basic Filters)',
                //     'url' => 'data:image/png;base64,' . $preprocessedImage,
                //     'amount' => $extractedData3['amount'],
                //     'litres' => $extractedData3['litres'],
                //     'unitPrice' => $extractedData3['unit_price'],
                //     'status' => $benchmarkData->getAmount() === $extractedData3['amount']
                //         && $benchmarkData->getLitres() === $extractedData3['litres']
                //         && $benchmarkData->getUnitPrice() === $extractedData3['unit_price']
                //         ? 'OK'
                //         : 'FAIL'
                // ],
            ];
            $progressBar->advance();
        }

        $progressBar->finish();
        $io->newLine(2);
        $io->success(sprintf('Parsed %d benchmark entries', count($data)));
        file_put_contents('./public/uploads/cmd/fuel_meter_scanner/results.json', json_encode($results));

        return Command::SUCCESS;
    }
}


class BenchmarkInputData
{
    /**
     * @var string
     */
    private $imagePath;

    /**
     * @var float
     */
    private $amount;

    /**
     * @var float
     */
    private $litres;

    /**
     * @var float
     */
    private $unitPrice;

    /**
     * @var float
     */
    private $currentAmount;

    /**
     * @var float
     */
    private $currentLitres;

    /**
     * @var float
     */
    private $currentUnitPrice;

    public function __construct(
        string $imagePath,
        float $amount,
        float $litres,
        float $unitPrice,
        float $currentAmount,
        float $currentLitres,
        float $currentUnitPrice
    ) {
        $this->imagePath = $imagePath;
        $this->amount = $amount;
        $this->litres = $litres;
        $this->unitPrice = $unitPrice;
        $this->currentAmount = $currentAmount;
        $this->currentLitres = $currentLitres;
        $this->currentUnitPrice = $currentUnitPrice;
    }

    static function fromArray(array $data): BenchmarkInputData
    {
        return new BenchmarkInputData(
            $data['image'],
            (float) $data['amount'],
            (float) $data['litres'],
            (float) $data['unitPrice'],
            (float) $data['currentAmount'],
            (float) $data['currentLitres'],
            (float) $data['currentUnitPrice']
        );
    }

    public function getImagePath(): string
    {
        return $this->imagePath;
    }

    public function setImagePath(string $imagePath): void
    {
        $this->imagePath = $imagePath;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    public function getLitres(): float
    {
        return $this->litres;
    }

    public function setLitres(float $litres): void
    {
        $this->litres = $litres;
    }

    public function getUnitPrice(): float
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(float $unitPrice): void
    {
        $this->unitPrice = $unitPrice;
    }

    public function getCurrentAmount(): float
    {
        return $this->currentAmount;
    }

    public function setCurrentAmount(float $currentAmount): void
    {
        $this->currentAmount = $currentAmount;
    }

    public function getCurrentLitres(): float
    {
        return $this->currentLitres;
    }

    public function setCurrentLitres(float $currentLitres): void
    {
        $this->currentLitres = $currentLitres;
    }

    public function getCurrentUnitPrice(): float
    {
        return $this->currentUnitPrice;
    }

    public function setCurrentUnitPrice(float $currentUnitPrice): void
    {
        $this->currentUnitPrice = $currentUnitPrice;
    }
}
