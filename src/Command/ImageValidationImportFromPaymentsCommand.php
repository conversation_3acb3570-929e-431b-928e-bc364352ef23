<?php

namespace App\Command;

use App\Entity\ImageValidationTrainingData;
use App\Entity\PaymentDetails;
use App\Repository\ImageValidationTrainingDataRepository;
use App\Repository\PaymentDetailsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:image-validation:import-from-payments',
    description: 'Import payment details into image validation training data',
)]
class ImageValidationImportFromPaymentsCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private PaymentDetailsRepository $paymentDetailsRepository;
    private ImageValidationTrainingDataRepository $imageValidationRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        PaymentDetailsRepository $paymentDetailsRepository,
        ImageValidationTrainingDataRepository $imageValidationRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->paymentDetailsRepository = $paymentDetailsRepository;
        $this->imageValidationRepository = $imageValidationRepository;
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'dry-run',
                null,
                InputOption::VALUE_NONE,
                'Run without making any changes to the database'
            )
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_REQUIRED,
                'Limit the number of payment details to process',
                null
            )
            ->addOption(
                'batch-size',
                null,
                InputOption::VALUE_REQUIRED,
                'Number of entities to process before flushing to the database',
                20
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $isDryRun = $input->getOption('dry-run');
        $limit = $input->getOption('limit');
        $batchSize = (int)$input->getOption('batch-size');

        $io->title('Importing Payment Details to Image Validation Training Data');

        if ($isDryRun) {
            $io->note('Running in dry-run mode. No changes will be made to the database.');
        }

        // Fetch all payment details
        $criteria = [];
        $orderBy = ['id' => 'ASC'];
        $paymentDetails = $limit
            ? $this->paymentDetailsRepository->findBy($criteria, $orderBy, (int)$limit)
            : $this->paymentDetailsRepository->findBy($criteria, $orderBy);

        if (empty($paymentDetails)) {
            $io->warning('No payment details found.');
            return Command::SUCCESS;
        }

        $io->note(sprintf('Found %d payment details to process.', count($paymentDetails)));

        // Set up progress bar
        $progressBar = new ProgressBar($output, count($paymentDetails));
        $progressBar->setFormat(
            ' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%'
        );
        $progressBar->start();

        // Statistics for the report
        $stats = [
            'total' => count($paymentDetails),
            'processed' => 0,
            'skipped' => 0,
            'created' => 0,
            'errors' => 0,
            'mathematicallyValid' => 0,
            'mathematicallyInvalid' => 0,
            'missingData' => 0,
        ];

        // Process payment details
        $batchCounter = 0;
        foreach ($paymentDetails as $paymentDetail) {
            try {
                $result = $this->processPaymentDetail($paymentDetail, $isDryRun);

                // Update statistics
                $stats['processed']++;
                if ($result === 'skipped') {
                    $stats['skipped']++;
                } elseif ($result === 'created') {
                    $stats['created']++;
                }

                // Update mathematical validation stats
                if ($result === 'created') {
                    if ($this->isMathematicallyValid($paymentDetail)) {
                        $stats['mathematicallyValid']++;
                    } else {
                        if ($this->hasMissingData($paymentDetail)) {
                            $stats['missingData']++;
                        } else {
                            $stats['mathematicallyInvalid']++;
                        }
                    }
                }

                // Flush every $batchSize entities
                if (!$isDryRun && ++$batchCounter % $batchSize === 0) {
                    $this->entityManager->flush();
                    $this->entityManager->clear(ImageValidationTrainingData::class);
                }
            } catch (\Exception $e) {
                $stats['errors']++;
                $io->error(sprintf(
                    'Error processing payment detail #%d: %s',
                    $paymentDetail->getId(),
                    $e->getMessage()
                ));
            }

            $progressBar->advance();
        }

        // Final flush if not in dry-run mode
        if (!$isDryRun) {
            $this->entityManager->flush();
        }

        $progressBar->finish();
        $io->newLine(2);

        // Display report
        $this->displayReport($io, $stats);

        return Command::SUCCESS;
    }

    /**
     * Process a single payment detail
     *
     * @param PaymentDetails $paymentDetail
     * @param bool $isDryRun
     * @return string Status: 'skipped', 'created'
     */
    private function processPaymentDetail(PaymentDetails $paymentDetail, bool $isDryRun): string
    {
        // Check if image path is valid
        if (empty($paymentDetail->getImage())) {
            return 'skipped';
        }

        // Check if an entry already exists for this payment detail
        $existingEntries = $this->imageValidationRepository->findByPaymentDetailsId($paymentDetail->getId());
        if (!empty($existingEntries)) {
            return 'skipped';
        }

        // If in dry-run mode, just return the status
        if ($isDryRun) {
            return 'created';
        }

        // Get a fresh instance of the payment detail from the database to avoid detached entity issues
        $paymentDetailEntity = $this->paymentDetailsRepository->find($paymentDetail->getId());
        if (!$paymentDetailEntity) {
            throw new \RuntimeException(sprintf('Payment detail with ID %d not found', $paymentDetail->getId()));
        }

        // Create new ImageValidationTrainingData entity
        $trainingData = new ImageValidationTrainingData();
        $trainingData->setPayemtDetailsId($paymentDetailEntity);
        $trainingData->setImagePath($paymentDetailEntity->getImage());

        // Set validation status
        $trainingData->setValid(false); // Default to false, requires manual validation

        // Calculate mathematical validation
        $isMathValid = $this->isMathematicallyValid($paymentDetailEntity);
        $trainingData->setMathematicalValidation($isMathValid);

        // Set default values
        $trainingData->setIncludeInExport(false);
        $trainingData->setCreatedAt(new \DateTimeImmutable());

        // Add validation note if mathematical validation fails
        if (!$isMathValid) {
            if ($this->hasMissingData($paymentDetailEntity)) {
                $trainingData->setValidationNote('Missing data for mathematical validation');
            } else {
                $calculatedAmount = (float)$paymentDetailEntity->getLitres() * (float)$paymentDetailEntity->getUnitPrice();
                $actualAmount = (float)$paymentDetailEntity->getAmount();
                $difference = abs($calculatedAmount - $actualAmount);

                $trainingData->setValidationNote(sprintf(
                    'Mathematical validation failed: %.2f (litres) * %.2f (unit price) = %.2f, but amount is %.2f (difference: %.2f)',
                    (float)$paymentDetailEntity->getLitres(),
                    (float)$paymentDetailEntity->getUnitPrice(),
                    $calculatedAmount,
                    $actualAmount,
                    $difference
                ));
            }
        }

        // Persist the entity
        $this->entityManager->persist($trainingData);

        return 'created';
    }

    /**
     * Check if payment detail is mathematically valid
     *
     * @param PaymentDetails $paymentDetail
     * @return bool
     */
    private function isMathematicallyValid(PaymentDetails $paymentDetail): bool
    {
        // If any value is missing, we can't validate
        if ($this->hasMissingData($paymentDetail)) {
            return false;
        }

        $amount = (float)$paymentDetail->getAmount();
        $litres = (float)$paymentDetail->getLitres();
        $unitPrice = (float)$paymentDetail->getUnitPrice();

        $calculatedAmount = $litres * $unitPrice;
        $tolerance = 1.00; // Same tolerance as in PaymentDetailsOCR

        return abs($calculatedAmount - $amount) < $tolerance;
    }

    /**
     * Check if payment detail has missing data for mathematical validation
     *
     * @param PaymentDetails $paymentDetail
     * @return bool
     */
    private function hasMissingData(PaymentDetails $paymentDetail): bool
    {
        return $paymentDetail->getAmount() === null
            || $paymentDetail->getAmount() === "0"
            || $paymentDetail->getLitres() === null
            || $paymentDetail->getLitres() === "0"
            || $paymentDetail->getUnitPrice() === null
            || $paymentDetail->getUnitPrice() === "0";
    }

    /**
     * Display the validation report
     *
     * @param SymfonyStyle $io
     * @param array $stats
     */
    private function displayReport(SymfonyStyle $io, array $stats): void
    {
        $io->section('Import Report');

        $io->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total payment details', $stats['total'], '100%'],
                ['Processed', $stats['processed'], $this->calculatePercentage($stats['processed'], $stats['total'])],
                ['Created', $stats['created'], $this->calculatePercentage($stats['created'], $stats['total'])],
                ['Skipped (already exists or no image)', $stats['skipped'], $this->calculatePercentage($stats['skipped'], $stats['total'])],
                ['Errors', $stats['errors'], $this->calculatePercentage($stats['errors'], $stats['total'])],
                ['Mathematically valid', $stats['mathematicallyValid'], $this->calculatePercentage($stats['mathematicallyValid'], $stats['created'])],
                ['Mathematically invalid', $stats['mathematicallyInvalid'], $this->calculatePercentage($stats['mathematicallyInvalid'], $stats['created'])],
                ['Missing data for validation', $stats['missingData'], $this->calculatePercentage($stats['missingData'], $stats['created'])],
            ]
        );

        if ($stats['created'] > 0) {
            $io->success(sprintf(
                'Successfully created %d new image validation training data entries.',
                $stats['created']
            ));
        } else {
            $io->warning('No new image validation training data entries were created.');
        }
    }

    /**
     * Calculate percentage
     *
     * @param int $value
     * @param int $total
     * @return string
     */
    private function calculatePercentage(int $value, int $total): string
    {
        if ($total === 0) {
            return '0%';
        }

        return sprintf('%.1f%%', ($value / $total) * 100);
    }
}
