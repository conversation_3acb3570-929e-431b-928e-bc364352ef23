<?php

namespace App\Command;

use App\Entity\RewardsItem;
use App\Entity\SonataMediaMedia;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\MediaBundle\Model\MediaManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\HttpFoundation\File\File;

#[AsCommand(name: 'app:rewards-item:seed', description: 'Seed Rewards items')]
class SeedRewardItemsCommand extends Command
{
    const CSV_SCHEMA = [
        'level',
        'spend',
        'points',
        'worth',
        'qty',
        'item1',
        'item2',
        'item3',
    ];

    public function __construct(
        private EntityManagerInterface $entityManager,
        private MediaManagerInterface $mediaManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $sf = new SymfonyStyle($input, $output);
        $csvFilePath = 'public/uploads/assets/images/rewards_items/rewards_items.csv';
        if (!file_exists($csvFilePath)) {
            $sf->error('CSV file not found.');
            return Command::FAILURE;
        }
        if (($handle = fopen($csvFilePath, 'r')) !== false) {
            $items = [];
            $isFirst = true;
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                if ($isFirst) {
                    $isFirst = false;
                    continue;
                }
                $newItems = $this->processRow($sf, $row);
                $items = array_merge($items, $newItems);
            }
            fclose($handle);

            $tableData = [];
            foreach ($items as $item) {
                $tableData[] = [
                    $item->getName(),
                    $item->getQuantity(),
                    $item->getPoints()
                ];
                $this->entityManager->persist($item);
            }
            $sf->table(['Name', 'Quantity', 'Points'], $tableData);
            $this->entityManager->flush();
        } else {
            $sf->error('Unable to open CSV file.');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function processRow(SymfonyStyle $sf, array $row): array
    {
        $keys = array_flip(self::CSV_SCHEMA);
        $max = 3;

        $items = [];
        for ($i = 0; $i < $max; $i++) {
            $nameQty = trim($row[$keys['item' . $i + 1]]);
            if (empty($nameQty)) {
                continue;
            }

            $parsedNameQty = $this->parseNameQty($nameQty);
            $name = $parsedNameQty['name'];
            $quantity = $parsedNameQty['quantity'];

            $item = new RewardsItem();
            $item->setName($name);
            $pointsValue = str_replace(',', '', $row[$keys['points']]);
            $item->setPoints((int)$pointsValue);
            $item->setQuantity($quantity);

            $imagePath = sprintf(
                'public/uploads/assets/images/rewards_items/%s_%d.jpg',
                trim($row[$keys['level']]),
                $i + 1
            );
            if (file_exists($imagePath)) {
                $media = new SonataMediaMedia();
                $media->setBinaryContent(new File($imagePath));
                $media->setProviderName('sonata.media.provider.image');
                $media->setContext('default');
                $this->mediaManager->save($media);

                $item->setFeaturedImage($media);
            } else {
                $sf->note('Image file not found: for ' . $name . '(' . $imagePath . ')');
            }

            $items[] = $item;
        }

        return $items;
    }

    /**
     * Parse a name containing an item name and quantity in the format:
     * "Item Name (Quantity)"
     *
     * @param string $name A single name from the input such as "Air freshner (2000)"
     * @return array Returns an associative array with 'name' and 'quantity', or the name and 0 if parsing fails.
     */
    private function parseNameQty(string $name): ?array
    {
        // Use a regular expression to match the pattern "Item Name (Quantity)"
        // The pattern breakdown:
        // ^           : start of the string
        // (.*?)       : capture any number of characters (lazy) as the item name
        // \s*\(       : optional whitespace followed by an opening parenthesis
        // (\d+)       : capture one or more digits as the quantity
        // \)\s*$      : closing parenthesis followed by optional whitespace until the end of the string
        if (preg_match('/^(.*?)\s*\((\d+)\)\s*$/', $name, $matches)) {
            // $matches[1] will be the item name
            // $matches[2] will be the quantity
            return [
                'name' => $matches[1],
                'quantity' => (int) $matches[2],
            ];
        }

        return ['name' => $name, 'quantity' => 0];
    }
}
