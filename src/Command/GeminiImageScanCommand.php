<?php

namespace App\Command;

use App\FuelMeterScanner;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'app:gemini:scan-image',
    description: 'Scan an image using Google Gemini 2.5 API to extract fuel meter information',
)]
class GeminiImageScanCommand extends Command
{
    private string $apiKey;
    private FuelMeterScanner $scanner;

    public function __construct(ParameterBagInterface $parameterBag, FuelMeterScanner $scanner)
    {
        parent::__construct();
        $this->apiKey = $parameterBag->get('env(GEMINI_API_KEY)');
        $this->scanner = $scanner;
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                'file_path',
                InputArgument::REQUIRED,
                'Path to the image file to scan'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Validate API key is not empty
        if (empty($this->apiKey)) {
            $io->error('Gemini API key is not configured. Please set the GEMINI_API_KEY environment variable.');
            return Command::FAILURE;
        }

        $filePath = $input->getArgument('file_path');

        $io->title('Scanning image with Google Gemini 2.5 API');
        $io->text("File: $filePath");

        try {
            // Use the FuelMeterScanner to scan the image
            $extractedData = $this->scanner->scanWithGemini($filePath, $this->apiKey);

            if ($extractedData === null) {
                $io->error('Failed to extract JSON data from the response');
                return Command::FAILURE;
            }

            // Display the results
            $io->success('Successfully extracted data from image');
            $io->table(
                ['Field', 'Value'],
                array_map(
                    fn($key, $value) => [$key, is_string($value) ? $value : json_encode($value)],
                    array_keys($extractedData),
                    array_values($extractedData)
                )
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error('Error processing image: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
