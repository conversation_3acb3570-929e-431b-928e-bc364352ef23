<?php

namespace App\Command;

use App\Entity\OCRMethod;
use App\Entity\PaymentDetails;
use App\Repository\OCRMethodRepository;
use App\Repository\PaymentDetailsRepository;
use App\Service\OCRComparisonService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Psr\Log\LoggerInterface;

#[AsCommand(
    name: 'app:generate-ocr-comparison',
    description: 'Generate OCR comparison data between current and improved systems',
)]
class GenerateOCRComparisonDataCommand extends Command
{
    private $paymentDetailsRepository;
    private $ocrMethodRepository;
    private $ocrComparisonService;
    private $entityManager;
    private $logger;

    public function __construct(
        PaymentDetailsRepository $paymentDetailsRepository,
        OCRMethodRepository $ocrMethodRepository,
        OCRComparisonService $ocrComparisonService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        parent::__construct();
        
        $this->paymentDetailsRepository = $paymentDetailsRepository;
        $this->ocrMethodRepository = $ocrMethodRepository;
        $this->ocrComparisonService = $ocrComparisonService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limit the number of payment details to process', 500)
            ->addOption('max-failures', null, InputOption::VALUE_OPTIONAL, 'Maximum number of failures before aborting', 50);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Generating OCR Comparison Data');

        $limit = (int) $input->getOption('limit');
        $maxFailures = (int) $input->getOption('max-failures');

        $io->section('Finding payment details to process');
        $paymentDetails = $this->findPaymentDetailsToProcess($limit);
        
        if (empty($paymentDetails)) {
            $io->warning('No payment details found for processing.');
            return Command::SUCCESS;
        }

        $io->note(sprintf('Found %d payment details to process', count($paymentDetails)));
        
        // Verify images exist before processing
        $io->section('Verifying image files');
        [$validPaymentDetails, $invalidPaymentDetails] = $this->verifyImages($paymentDetails);
        
        if (empty($validPaymentDetails)) {
            $io->error('No valid payment details with accessible images found.');
            return Command::FAILURE;
        }
        
        $io->note(sprintf(
            'Verified %d payment details (%d valid, %d invalid images)',
            count($paymentDetails),
            count($validPaymentDetails),
            count($invalidPaymentDetails)
        ));

        // Process valid payment details
        $io->section('Processing payment details');
        $successCount = 0;
        $failureCount = 0;
        
        $progressBar = new ProgressBar($output, count($validPaymentDetails));
        $progressBar->setFormat(
            "%current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s% \n%message%"
        );
        
        foreach ($validPaymentDetails as $index => $paymentDetail) {
            $progressBar->setMessage(sprintf('Processing payment detail #%d', $paymentDetail->getId()));
            $progressBar->display();
            
            try {
                $result = $this->processWithRetry($paymentDetail);
                if ($result) {
                    $successCount++;
                    $this->entityManager->clear(); // Clear entity manager to avoid memory leaks
                } else {
                    $failureCount++;
                }
            } catch (\Exception $e) {
                $this->logger->error(sprintf(
                    'Unrecoverable error processing payment detail #%d: %s',
                    $paymentDetail->getId(),
                    $e->getMessage()
                ));
                $failureCount++;
            }
            
            $progressBar->advance();
            
            // Check if we've reached the maximum failure count
            if ($failureCount >= $maxFailures) {
                $io->error(sprintf(
                    'Reached maximum failure count (%d). Aborting processing.',
                    $maxFailures
                ));
                break;
            }
        }
        
        $progressBar->finish();
        $io->newLine(2);
        
        // Display summary
        $io->section('Processing Summary');
        $io->success(sprintf(
            'Processed %d payment details (%d successful, %d failed, %d invalid images)',
            count($validPaymentDetails),
            $successCount,
            $failureCount,
            count($invalidPaymentDetails)
        ));
        
        if (!empty($invalidPaymentDetails)) {
            $io->warning('Payment details with invalid images:');
            foreach ($invalidPaymentDetails as $paymentDetail) {
                $io->writeln(sprintf('  - ID: %d, Image: %s', $paymentDetail->getId(), $paymentDetail->getImage()));
            }
        }

        return Command::SUCCESS;
    }

    /**
     * Find payment details that have QC state and haven't been processed by the improved system
     */
    private function findPaymentDetailsToProcess(int $limit): array
    {
        // Get the improved system OCR method
        $improvedMethod = $this->ocrMethodRepository->find(OCRMethod::IMPROVED_SYSTEM);
        
        if (!$improvedMethod) {
            $this->logger->error('Improved OCR method not found in the database');
            return [];
        }
        
        // Use a direct SQL query to ensure we're getting accurate results
        $conn = $this->entityManager->getConnection();
        
        // Find payment details with QC state that don't have results for the improved system
        $sql = '
            SELECT pd.id 
            FROM payment_details pd
            INNER JOIN payment_details_review pdr ON pdr.payment_details_id = pd.id
            WHERE pd.qc_state IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 
                FROM payment_details_ocr ocr 
                WHERE ocr.payment_details_id = pd.id 
                AND ocr.ocr_method_id = :methodId
            )
            ORDER BY pd.id DESC
            LIMIT :limit
        ';
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('methodId', $improvedMethod->getId(), \PDO::PARAM_INT);
        $stmt->bindValue('limit', $limit, \PDO::PARAM_INT);
        $result = $stmt->executeQuery();
        
        $paymentDetailIds = $result->fetchAllAssociative();
        
        if (empty($paymentDetailIds)) {
            return [];
        }
        
        // Extract the IDs
        $ids = array_map(function ($row) {
            return $row['id'];
        }, $paymentDetailIds);
        
        // Load the actual PaymentDetails entities
        return $this->paymentDetailsRepository->findBy(['id' => $ids], ['id' => 'DESC']);
    }

    /**
     * Verify that images exist and are readable
     * @param PaymentDetails[] $paymentDetails
     */
    private function verifyImages(array $paymentDetails): array
    {
        $validPaymentDetails = [];
        $invalidPaymentDetails = [];
        
        foreach ($paymentDetails as $paymentDetail) {
            $imagePath = realpath('public' . $paymentDetail->getImage());
            
            if ($imagePath && file_exists($imagePath) && is_readable($imagePath)) {
                $validPaymentDetails[] = $paymentDetail;
            } else {
                $invalidPaymentDetails[] = $paymentDetail;
                $this->logger->warning(sprintf(
                    'Image not found or not readable for payment detail #%d: %s',
                    $paymentDetail->getId(),
                    $paymentDetail->getImage()
                ));
            }
        }
        
        return [$validPaymentDetails, $invalidPaymentDetails];
    }

    /**
     * Process a payment detail with retry logic
     */
    private function processWithRetry(PaymentDetails $paymentDetail): bool
    {
        $retries = 0;
        $maxRetries = 5;
        
        // Save ID for logging since we'll clear the entity manager
        $paymentDetailId = $paymentDetail->getId();
        
        // Clear entity manager to ensure we have a fresh state
        $this->entityManager->clear();
        
        // Reload the payment detail to ensure we have the latest state
        $paymentDetail = $this->paymentDetailsRepository->find($paymentDetailId);
        if (!$paymentDetail) {
            $this->logger->error(sprintf(
                'Could not reload payment detail #%d - entity not found after clearing entity manager',
                $paymentDetailId
            ));
            return false;
        }
        
        while ($retries < $maxRetries) {
            try {
                $this->throttle();
                $this->ocrComparisonService->processAndCompare($paymentDetail);
                
                // Clear entity manager after successful processing to avoid memory leaks
                $this->entityManager->clear();
                return true;
            } catch (\Exception $e) {
                $retries++;
                $this->logger->error(sprintf(
                    'Error processing payment detail #%d (attempt %d/%d): %s',
                    $paymentDetailId,
                    $retries,
                    $maxRetries,
                    $e->getMessage()
                ));
                
                if ($retries < $maxRetries) {
                    sleep(60); // 1-minute sleep between retries
                    
                    // Clear entity manager and reload entity to ensure fresh state for retry
                    $this->entityManager->clear();
                    $paymentDetail = $this->paymentDetailsRepository->find($paymentDetailId);
                    
                    if (!$paymentDetail) {
                        $this->logger->error(sprintf(
                            'Could not reload payment detail #%d after retry - aborting',
                            $paymentDetailId
                        ));
                        return false;
                    }
                }
            }
        }
        
        return false;
    }

    /**
     * Throttle API requests to 3 per second
     */
    private function throttle(): void
    {
        static $lastRequestTime = 0;
        
        $currentTime = microtime(true);
        $timeToWait = max(0, ($lastRequestTime + 0.333) - $currentTime);
        
        if ($timeToWait > 0) {
            usleep((int)($timeToWait * 1000000));
        }
        
        $lastRequestTime = microtime(true);
    }
} 