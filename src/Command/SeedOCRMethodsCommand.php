<?php

namespace App\Command;

use App\Entity\OCRMethod;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:seed:ocr-methods',
    description: 'Seeds the OCR methods into the database',
)]
class SeedOCRMethodsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Seeding OCR Methods');

        $repository = $this->entityManager->getRepository(OCRMethod::class);

        // Check if any methods already exist
        $existingCount = count($repository->findAll());
        
        if ($existingCount > 0) {
            $io->info("Found {$existingCount} existing OCR method(s).");
        }

        // Create methods
        $methods = [
            [
                'name' => 'Current System',
                'description' => 'Original ChatGPT Vision implementation',
            ],
            [
                'name' => 'Improved System',
                'description' => 'Enhanced implementation with follow-up questioning',
            ],
        ];

        $addedCount = 0;

        foreach ($methods as $methodData) {
            // Check if this specific method exists by name
            $existing = $repository->findOneBy(['name' => $methodData['name']]);
            
            if (!$existing) {
                $method = new OCRMethod();
                $method->setName($methodData['name']);
                $method->setDescription($methodData['description']);

                
                $this->entityManager->persist($method);
                $addedCount++;
                
                $io->text("Added OCR method: {$methodData['name']}");
            } else {
                $io->text("OCR method already exists: {$methodData['name']}");
            }
        }

        $this->entityManager->flush();

        if ($addedCount > 0) {
            $io->success("Successfully added {$addedCount} OCR methods to the database.");
        } else {
            $io->info('No new OCR methods were added.');
        }

        return Command::SUCCESS;
    }
} 