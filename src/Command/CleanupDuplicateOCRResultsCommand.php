<?php

namespace App\Command;

use App\Repository\PaymentDetailsOCRRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:cleanup-duplicate-ocr-results',
    description: 'Clean up duplicate OCR results in the database',
)]
class CleanupDuplicateOCRResultsCommand extends Command
{
    private $paymentDetailsOCRRepository;

    public function __construct(
        PaymentDetailsOCRRepository $paymentDetailsOCRRepository
    ) {
        parent::__construct();
        
        $this->paymentDetailsOCRRepository = $paymentDetailsOCRRepository;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Cleaning up duplicate OCR results');

        // Count before cleanup
        $countBefore = $this->countOCRResults($io);
        
        // Remove duplicates
        $removed = $this->paymentDetailsOCRRepository->removeDuplicates();
        
        // Count after cleanup
        $countAfter = $this->countOCRResults($io);
        
        $io->success(sprintf('Removed %d duplicate OCR results', $removed));
        $io->table(
            ['State', 'Count'],
            [
                ['Before cleanup', $countBefore],
                ['After cleanup', $countAfter],
                ['Difference', $countBefore - $countAfter]
            ]
        );
        
        return Command::SUCCESS;
    }
    
    private function countOCRResults(SymfonyStyle $io): int
    {
        $qb = $this->paymentDetailsOCRRepository->createQueryBuilder('p')
            ->select('COUNT(p.id)');
            
        return (int) $qb->getQuery()->getSingleScalarResult();
    }
} 