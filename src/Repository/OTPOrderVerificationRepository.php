<?php

namespace App\Repository;

use App\Entity\OTPOrderVerification;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OTPOrderVerification>
 */
class OTPOrderVerificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OTPOrderVerification::class);
    }

    /**
     * Find OTP verification by order ID and customer ID
     *
     * @param RewardsOrder $rewardOrder The order
     * @param SonataUserUser $customer The customer
     * @return OTPOrderVerification|null
     */
    public function findByOrderAndCustomer(RewardsOrder $rewardOrder, SonataUserUser $customer): ?OTPOrderVerification
    {
        return $this->createQueryBuilder('o')
            ->andWhere('o.rewardOrder = :rewardOrder')
            ->andWhere('o.customer = :customer')
            ->setParameter('rewardOrder', $rewardOrder)
            ->setParameter('customer', $customer)
            ->orderBy('o.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * Find active (not verified and not expired) OTP verification by order ID and customer ID
     *
     * @param RewardsOrder $rewardOrder The order
     * @param SonataUserUser $customer The customer
     * @return OTPOrderVerification|null
     */
    public function findActiveByOrderAndCustomer(RewardsOrder $rewardOrder, SonataUserUser $customer): ?OTPOrderVerification
    {
        $now = new \DateTimeImmutable();

        return $this->createQueryBuilder('o')
            ->andWhere('o.rewardOrder = :rewardOrder')
            ->andWhere('o.customer = :customer')
            ->andWhere('o.verifiedAt IS NULL')
            ->andWhere('o.expiredAt > :now')
            ->setParameter('rewardOrder', $rewardOrder)
            ->setParameter('customer', $customer)
            ->setParameter('now', $now)
            ->orderBy('o.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * Find OTP verification by code
     *
     * @param string $code The OTP code
     * @return OTPOrderVerification|null
     */
    public function findByCode(string $code): ?OTPOrderVerification
    {
        return $this->createQueryBuilder('o')
            ->andWhere('o.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * Find active OTP verification by code, order, and customer
     *
     * @param string $code The OTP code
     * @param RewardsOrder $rewardOrder The order
     * @param SonataUserUser $customer The customer
     * @return OTPOrderVerification|null
     */
    public function findActiveByCodeOrderAndCustomer(string $code, RewardsOrder $rewardOrder, SonataUserUser $customer): ?OTPOrderVerification
    {
        $now = new \DateTimeImmutable();

        return $this->createQueryBuilder('o')
            ->andWhere('o.code = :code')
            ->andWhere('o.rewardOrder = :rewardOrder')
            ->andWhere('o.customer = :customer')
            ->andWhere('o.verifiedAt IS NULL')
            ->andWhere('o.expiredAt > :now')
            ->setParameter('code', $code)
            ->setParameter('rewardOrder', $rewardOrder)
            ->setParameter('customer', $customer)
            ->setParameter('now', $now)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * Invalidate all previous OTPs for a specific order and customer
     *
     * @param RewardsOrder $rewardOrder The order
     * @param SonataUserUser $customer The customer
     * @return int Number of invalidated OTPs
     */
    public function invalidatePreviousOTPs(RewardsOrder $rewardOrder, SonataUserUser $customer): int
    {
        $qb = $this->createQueryBuilder('o')
            ->update()
            ->set('o.expiredAt', ':now')
            ->andWhere('o.rewardOrder = :rewardOrder')
            ->andWhere('o.customer = :customer')
            ->andWhere('o.verifiedAt IS NULL')
            ->setParameter('now', new \DateTimeImmutable())
            ->setParameter('rewardOrder', $rewardOrder)
            ->setParameter('customer', $customer);

        return $qb->getQuery()->execute();
    }
}
