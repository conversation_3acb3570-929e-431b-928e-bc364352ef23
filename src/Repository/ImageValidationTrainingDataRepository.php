<?php

namespace App\Repository;

use App\Entity\ImageValidationTrainingData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ImageValidationTrainingData>
 */
class ImageValidationTrainingDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ImageValidationTrainingData::class);
    }

    /**
     * Find records by validation status
     *
     * @param bool $isValid
     * @return ImageValidationTrainingData[] Returns an array of ImageValidationTrainingData objects
     */
    public function findByValidationStatus(bool $isValid): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.isValid = :val')
            ->setParameter('val', $isValid)
            ->orderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Find records by mathematical validation status
     *
     * @param bool $mathematicalValidation
     * @return ImageValidationTrainingData[] Returns an array of ImageValidationTrainingData objects
     */
    public function findByMathematicalValidation(bool $mathematicalValidation): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.mathematicalValidation = :val')
            ->setParameter('val', $mathematicalValidation)
            ->orderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Find records by export status
     *
     * @param bool $includeInExport
     * @return ImageValidationTrainingData[] Returns an array of ImageValidationTrainingData objects
     */
    public function findByExportStatus(bool $includeInExport): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.includeInExport = :val')
            ->setParameter('val', $includeInExport)
            ->orderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Find records by payment details ID
     *
     * @param int $paymentDetailsId
     * @return ImageValidationTrainingData[] Returns an array of ImageValidationTrainingData objects
     */
    public function findByPaymentDetailsId(int $paymentDetailsId): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.payemtDetailsId = :val')
            ->setParameter('val', $paymentDetailsId)
            ->orderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Find records for export (valid and marked for export)
     *
     * @return ImageValidationTrainingData[] Returns an array of ImageValidationTrainingData objects
     */
    public function findForExport(): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.isValid = :valid')
            ->andWhere('i.includeInExport = :export')
            ->setParameter('valid', true)
            ->setParameter('export', true)
            ->orderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }
}
