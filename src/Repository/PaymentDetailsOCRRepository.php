<?php

namespace App\Repository;

use App\Entity\PaymentDetailsOCR;
use App\Entity\OCRMethod;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PaymentDetailsOCR>
 *
 * @method PaymentDetailsOCR|null find($id, $lockMode = null, $lockVersion = null)
 * @method PaymentDetailsOCR|null findOneBy(array $criteria, array $orderBy = null)
 * @method PaymentDetailsOCR[]    findAll()
 * @method PaymentDetailsOCR[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PaymentDetailsOCRRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PaymentDetailsOCR::class);
    }

    /**
     * Find OCR results by payment details and method
     */
    public function findByPaymentDetailsAndMethod(int $paymentDetailsId, int $methodId): ?PaymentDetailsOCR
    {
        return $this->findOneBy([
            'paymentDetails' => $paymentDetailsId,
            'ocrMethod' => $methodId
        ]);
    }

    /**
     * Find all OCR results for a specific method
     */
    public function findByMethod(int $methodId): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.ocrMethod = :methodId')
            ->setParameter('methodId', $methodId)
            ->getQuery()
            ->getResult();
    }

    /**
     * Remove duplicate OCR results for a payment detail
     */
    public function removeDuplicates(): int
    {
        $conn = $this->getEntityManager()->getConnection();
        
        // Find duplicate entries (same payment_details_id and ocr_method_id)
        $sql = '
            DELETE p1 FROM payment_details_ocr p1
            INNER JOIN (
                SELECT MIN(id) as min_id, payment_details_id, ocr_method_id
                FROM payment_details_ocr
                GROUP BY payment_details_id, ocr_method_id
            ) p2
            ON p1.payment_details_id = p2.payment_details_id 
            AND p1.ocr_method_id = p2.ocr_method_id 
            AND p1.id != p2.min_id
        ';
        
        return $conn->executeStatement($sql);
    }
} 