<?php

namespace App\Repository;

use App\Entity\RewardsOrder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RewardsOrder>
 */
class RewardsOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RewardsOrder::class);
    }

    /**
     * Find pending orders for a specific customer
     *
     * @param int $customerId The customer ID
     * @return RewardsOrder[] Returns an array of RewardsOrder objects
     */
    public function findPendingOrdersByCustomer(int $customerId): array
    {
        return $this->createQueryBuilder('o')
            ->innerJoin('o.user', 'u')
            ->innerJoin('o.item', 'i')
            ->andWhere('u.id = :customerId')
            ->andWhere('o.state = :state')
            ->setParameter('customerId', $customerId)
            ->setParameter('state', RewardsOrder::STATUS_PENDING)
            ->orderBy('o.created_at', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    //    /**
    //     * @return RewardsOrder[] Returns an array of RewardsOrder objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('r')
    //            ->andWhere('r.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('r.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?RewardsOrder
    //    {
    //        return $this->createQueryBuilder('r')
    //            ->andWhere('r.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
