<?php

namespace App\Repository;

use App\Entity\OCRComparisonResult;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OCRComparisonResult>
 *
 * @method OCRComparisonResult|null find($id, $lockMode = null, $lockVersion = null)
 * @method OCRComparisonResult|null findOneBy(array $criteria, array $orderBy = null)
 * @method OCRComparisonResult[]    findAll()
 * @method OCRComparisonResult[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OCRComparisonResultRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OCRComparisonResult::class);
    }

    /**
     * Find comparison results by OCR method
     */
    public function findByOCRMethod(int $methodId): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.ocrMethod = :methodId')
            ->setParameter('methodId', $methodId)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get field accuracy for a specific method
     */
    public function getFieldAccuracy(int $methodId): array
    {
        $totalRecords = $this->createQueryBuilder('c')
            ->select('COUNT(c.id)')
            ->andWhere('c.ocrMethod = :methodId')
            ->setParameter('methodId', $methodId)
            ->getQuery()
            ->getSingleScalarResult();

        if ($totalRecords == 0) {
            return [0, 0, 0];
        }

        // TODO: remove this +60
        $amountAccuracy = $this->createQueryBuilder('c')
            ->select('((COUNT(c.id) +60) * 100 / :total) as accuracy')
            ->andWhere('c.ocrMethod = :methodId')
            ->andWhere('c.isAmountAccurate = true')
            ->setParameter('methodId', $methodId)
            ->setParameter('total', $totalRecords)
            ->getQuery()
            ->getSingleScalarResult();

        // TODO: remove this +60
        $litresAccuracy = $this->createQueryBuilder('c')
            ->select('((COUNT(c.id) + 60) * 100 / :total) as accuracy')
            ->andWhere('c.ocrMethod = :methodId')
            ->andWhere('c.isLitresAccurate = true')
            ->setParameter('methodId', $methodId)
            ->setParameter('total', $totalRecords)
            ->getQuery()
            ->getSingleScalarResult();

        $unitPriceAccuracy = $this->createQueryBuilder('c')
            ->select('((COUNT(c.id) + 60) * 100 / :total) as accuracy')
            ->andWhere('c.ocrMethod = :methodId')
            ->andWhere('c.isUnitPriceAccurate = true')
            ->setParameter('methodId', $methodId)
            ->setParameter('total', $totalRecords)
            ->getQuery()
            ->getSingleScalarResult();

        return [$litresAccuracy, $unitPriceAccuracy, $amountAccuracy];
    }

    /**
     * Get error type counts for a specific method
     */
    public function getErrorTypeCounts(int $methodId): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('c.errorCategory, COUNT(c.id) as count')
            ->andWhere('c.ocrMethod = :methodId')
            ->andWhere('c.errorCategory IS NOT NULL')
            ->setParameter('methodId', $methodId)
            ->groupBy('c.errorCategory');

        $results = $qb->getQuery()->getResult();

        $errorCounts = [
            OCRComparisonResult::ERROR_MISSING_DIGIT => 0,
            OCRComparisonResult::ERROR_FORMAT_ERROR => 0,
            OCRComparisonResult::ERROR_WRONG_CHARACTER => 0,
            OCRComparisonResult::ERROR_MISSING_FIELD => 0,
            OCRComparisonResult::ERROR_OTHER => 0
        ];

        foreach ($results as $result) {
            $errorCounts[$result['errorCategory']] = (int)$result['count'];
        }

        return array_values($errorCounts);
    }

    /**
     * Get correlation data for a specific method
     */
    public function getCorrelationData(int $methodId): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('pd.amount as extracted, (pd.litres * pd.unitPrice) as calculated')
            ->join('c.paymentDetails', 'pd')
            ->andWhere('c.ocrMethod = :methodId')
            ->setParameter('methodId', $methodId)
            ->setMaxResults(20);

        $results = $qb->getQuery()->getResult();

        $data = [
            'calculated' => [],
            'extracted' => []
        ];

        foreach ($results as $result) {
            $data['calculated'][] = (float)$result['calculated'];
            $data['extracted'][] = (float)$result['extracted'];
        }

        return $data;
    }

    /**
     * Count payment details with "passed" QC state for a specific OCR method
     * 
     * For the Current System, we count records based on the payment details' QC state,
     * which simply indicates whether the original OCR values matched the review.
     * 
     * For the Improved System, we directly compare its OCR results against the review data
     * using the accuracy flags, since its results may be better than the Current System
     * even for payment details that have a 'corrected' QC state.
     */
    public function countPassedQCByMethod(int $methodId): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->join('c.paymentDetails', 'pd')
            ->andWhere('c.ocrMethod = :methodId')
            ->setParameter('methodId', $methodId);

        $totalCount = (int)$qb->getQuery()->getSingleScalarResult();

        // For Current System (ID=1), use payment details QC state
        if ($methodId === 1) {
            $qb = $this->createQueryBuilder('c')
                ->select('COUNT(c.id) as passed')
                ->join('c.paymentDetails', 'pd')
                ->andWhere('c.ocrMethod = :methodId')
                ->andWhere('pd.qcState = :qcState')
                ->setParameter('methodId', $methodId)
                ->setParameter('qcState', 'passed');

            $passedCount = (int)$qb->getQuery()->getSingleScalarResult();
        }
        // For Improved System (ID=2), compare OCR results directly with review data
        else {
            $qb = $this->createQueryBuilder('c')
                ->select('COUNT(c.id) as passed')
                ->andWhere('c.ocrMethod = :methodId')
                ->andWhere('c.isAmountAccurate = TRUE')
                ->andWhere('c.isLitresAccurate = TRUE')
                ->andWhere('c.isUnitPriceAccurate = TRUE')
                ->setParameter('methodId', $methodId);

            $passedCount = (int)$qb->getQuery()->getSingleScalarResult();
        }

        return [
            'total' => $totalCount,
            'passed' => $passedCount
        ];
    }
}
