<?php

namespace App\Repository;

use App\Entity\OCRMethod;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OCRMethod>
 *
 * @method OCRMethod|null find($id, $lockMode = null, $lockVersion = null)
 * @method OCRMethod|null findOneBy(array $criteria, array $orderBy = null)
 * @method OCRMethod[]    findAll()
 * @method OCRMethod[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OCRMethodRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OCRMethod::class);
    }

    /**
     * Find active OCR methods
     */
    public function findActive(): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.isActive = :active')
            ->setParameter('active', true)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get method by its constant identifier
     */
    public function getByConstant(int $constantId): ?OCRMethod
    {
        return $this->find($constantId);
    }
} 