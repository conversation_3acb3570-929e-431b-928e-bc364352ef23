<?php

namespace App\Repository;

use App\Entity\RegistrationOTP;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RegistrationOTP>
 */
class RegistrationOTPRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RegistrationOTP::class);
    }

    /**
     * Find registration OTP by registration ID
     *
     * @param string $registrationId The registration ID
     * @return RegistrationOTP|null
     */
    public function findByRegistrationId(string $registrationId): ?RegistrationOTP
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.registrationId = :registrationId')
            ->setParameter('registrationId', $registrationId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find active (not verified and not expired) registration OTP by registration ID
     *
     * @param string $registrationId The registration ID
     * @return RegistrationOTP|null
     */
    public function findActiveByRegistrationId(string $registrationId): ?RegistrationOTP
    {
        $now = new \DateTimeImmutable();

        return $this->createQueryBuilder('r')
            ->andWhere('r.registrationId = :registrationId')
            ->andWhere('r.verifiedAt IS NULL')
            ->andWhere('r.expiresAt > :now')
            ->setParameter('registrationId', $registrationId)
            ->setParameter('now', $now)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find active registration OTP by phone number
     *
     * @param string $mobilePhoneCanonical The canonical phone number
     * @return RegistrationOTP|null
     */
    public function findActiveByPhone(string $mobilePhoneCanonical): ?RegistrationOTP
    {
        $now = new \DateTimeImmutable();

        return $this->createQueryBuilder('r')
            ->andWhere('r.mobilePhoneCanonical = :mobilePhoneCanonical')
            ->andWhere('r.verifiedAt IS NULL')
            ->andWhere('r.expiresAt > :now')
            ->setParameter('mobilePhoneCanonical', $mobilePhoneCanonical)
            ->setParameter('now', $now)
            ->orderBy('r.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Invalidate all previous OTPs for a specific phone number
     *
     * @param string $mobilePhoneCanonical The canonical phone number
     * @return int Number of invalidated OTPs
     */
    public function invalidatePreviousOTPs(string $mobilePhoneCanonical): int
    {
        $qb = $this->createQueryBuilder('r')
            ->update()
            ->set('r.expiresAt', ':now')
            ->andWhere('r.mobilePhoneCanonical = :mobilePhoneCanonical')
            ->andWhere('r.verifiedAt IS NULL')
            ->setParameter('now', new \DateTimeImmutable())
            ->setParameter('mobilePhoneCanonical', $mobilePhoneCanonical);

        return $qb->getQuery()->execute();
    }

    /**
     * Clean up expired registration OTPs
     *
     * @param int $days Number of days to keep expired OTPs
     * @return int Number of deleted OTPs
     */
    public function cleanupExpiredOTPs(int $days = 7): int
    {
        $date = new \DateTimeImmutable("-{$days} days");

        $qb = $this->createQueryBuilder('r')
            ->delete()
            ->andWhere('r.expiresAt < :date')
            ->setParameter('date', $date);

        return $qb->getQuery()->execute();
    }
}
