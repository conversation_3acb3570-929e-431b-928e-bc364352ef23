<?php

namespace App;

use OpenAI;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class FuelMeterScanner
{
    const PREPROCESS_ENABLED = true;
    const PREPROCESS_DISABLED = false;

    private $apiKey;
    private $preprocessor;

    public function __construct($apiKey = null)
    {
        $this->apiKey = $apiKey;
        $this->preprocessor = new ImagePreprocessor();
    }

    public function scan($image_base64, $proprocess = self::PREPROCESS_ENABLED)
    {
        $client = OpenAI::client($this->apiKey);
        // if ($proprocess) {
        $image_base64 = $this->preprocessor->preprocess($image_base64, ['mode' => 'local']);
        // }

        $prompt = <<<EOD
You are given a photo of a fuel dispenser screen from Mauritius. Extract the values displayed on the screen and return them as a JSON object.

The screen shows:

The total amount in Rupees.

The volume in Litres.

The price per litre.

Instructions:

Read the numeric values from the screen.

Based on the price per litre:

If the price is 61.20, set "fuel_type": "gasoline".

If the price is 58.95, set "fuel_type": "diesel".

Return the result in this format:

{
  "amount": 200.00,
  "litres": 3.27,
  "unit_price": 61.20,
  "fuel_type": "gasoline"
}
EOD;

        $result = $client->chat()->create([
            'model' => 'gpt-4o', // TOOD: try to optimize prompt with mini
            'temperature' => 0.8,
            'messages' => [
                [
                    "role" => "user",
                    "content" => [
                        ["type" => "text", "text" => $prompt],
                        [
                            "type" => "image_url",
                            "image_url" => [
                                "url" => "data:image/png;base64,$image_base64",
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $content = $result->choices[0]->message->content;
        $data = $this->extractJson($content);

        // If data is null, return a default result
        if ($data === null) {
            return [
                'amount' => 0,
                'litres' => 0,
                'unit_price' => 0,
                'fuel_type' => 'unknown',
                'processing_time' => 0,
                'error' => 'Failed to extract JSON from response'
            ];
        }

        return $this->sanitize($data);
    }

    public function scanWithOllama($base64Image, $proprocess = self::PREPROCESS_ENABLED)
    {
        $client = new Client();

        if ($proprocess === self::PREPROCESS_ENABLED) {
            $base64Image = $this->preprocessor->preprocess($base64Image);
        }

        try {
            $response = $client->post('http://192.168.0.196:11434/api/generate', [
                'json' => [
                    'model' => 'llava-llama3',
                    'prompt' => 'Extract the following information in JSON format with keys: amount, litres, and unit_price. Provide only JSON output.',
                    'images' => [$base64Image],
                    'stream' => false
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            if (!isset($result['response'])) {
                throw new \Exception('Invalid response from Ollama API');
            }

            $data = $this->extractJson($result['response']);

            // If data is null, return a default result
            if ($data === null) {
                return [
                    'amount' => 0,
                    'litres' => 0,
                    'unit_price' => 0,
                    'fuel_type' => 'unknown',
                    'processing_time' => 0,
                    'error' => 'Failed to extract JSON from response'
                ];
            }

            return $this->sanitize($data);
        } catch (GuzzleException $e) {
            throw new \Exception('API request failed: ' . $e->getMessage());
        }
    }

    private function sanitize(array $data)
    {
        // Ensure we have an array to work with
        if (!is_array($data)) {
            return [
                'amount' => 0,
                'litres' => 0,
                'unit_price' => 0,
                'fuel_type' => 'unknown'
            ];
        }

        foreach ($data as $key => $value) {
            if ($key === 'fuel_type') {
                continue; // Skip sanitization for non-numeric fields
            }

            $str = "$value";
            $data[$key] = str_replace('.', '', $str);
            $data[$key] = (float) $data[$key] / 100;
        }

        return $data;
    }

    /**
     * Scan an image using Google Gemini 2.5 API
     *
     * @param string $filePath Path to the image file
     * @param string $apiKey Gemini API key
     * @return array Extracted data
     * @throws \Exception If API request fails or processing fails
     */
    public function scanWithGemini(string $filePath, string $apiKey): array
    {
        // Validate file exists
        if (!file_exists($filePath)) {
            throw new \Exception("File not found: $filePath", 1001);
        }

        // Get file MIME type
        $mimeType = mime_content_type($filePath);
        if (!str_starts_with($mimeType, 'image/')) {
            throw new \Exception("File is not an image: $filePath ($mimeType)", 1002);
        }

        // Read and encode the image
        $imageData = file_get_contents($filePath);
        if ($imageData === false) {
            throw new \Exception("Failed to read image data: $filePath", 1003);
        }

        $base64Image = base64_encode($imageData);

        // Get the prompt
        $prompt = $this->getFuelMeterPrompt();

        // Call Gemini API
        $result = $this->callGeminiApi($base64Image, $mimeType, $prompt, $apiKey);

        // Extract JSON from the response
        $extractedData = $this->extractJson($result);

        // If extraction failed, throw an exception
        if ($extractedData === null) {
            throw new \Exception("Failed to extract structured data from API response", 1004);
        }

        return $extractedData;
    }

    /**
     * Get the fuel meter prompt
     */
    public function getFuelMeterPrompt(): string
    {
        return <<<EOD
You are given a photo of a fuel dispenser screen from Mauritius. Extract the values displayed on the screen and return them as a JSON object.

The screen shows:

The total amount in Rupees.

The volume in Litres.

The price per litre.

Instructions:

Read the numeric values from the screen.

Based on the price per litre:

If the price is 61.20, set "fuel_type": "gasoline".

If the price is 58.95, set "fuel_type": "diesel".

Return the result in this format:

{
  "amount": 200.00,
  "litres": 3.27,
  "unit_price": 61.20,
  "fuel_type": "gasoline"
}
EOD;
    }

    /**
     * Call the Gemini API with the image and prompt
     *
     * @param string $base64Image Base64 encoded image data
     * @param string $mimeType MIME type of the image
     * @param string $prompt Prompt to send to the API
     * @param string $apiKey Gemini API key
     * @return string API response text
     * @throws \Exception If API request fails or response is invalid
     */
    private function callGeminiApi(string $base64Image, string $mimeType, string $prompt, string $apiKey): string
    {
        try {
            $client = new Client();
            $response = $client->post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-03-25:generateContent?key={$apiKey}",
                [
                    'json' => [
                        'contents' => [
                            [
                                'role' => 'user',
                                'parts' => [
                                    [
                                        'inlineData' => [
                                            'mimeType' => $mimeType,
                                            'data' => $base64Image
                                        ]
                                    ],
                                    [
                                        'text' => $prompt
                                    ]
                                ]
                            ]
                        ],
                        'generationConfig' => [
                            'responseMimeType' => 'application/json',
                            'responseSchema' => [
                                'type' => 'object',
                                'properties' => [
                                    'amount' => [
                                        'type' => 'number'
                                    ],
                                    'litres' => [
                                        'type' => 'number'
                                    ],
                                    'unit_price' => [
                                        'type' => 'number'
                                    ],
                                    'fuel_type' => [
                                        'type' => 'string'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'timeout' => 30, // Set a reasonable timeout
                ]
            );

            $responseData = json_decode($response->getBody()->getContents(), true);

            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                return $responseData['candidates'][0]['content']['parts'][0]['text'];
            } else {
                // Check for specific error conditions in the response
                if (isset($responseData['error'])) {
                    $errorCode = $responseData['error']['code'] ?? 0;
                    $errorMessage = $responseData['error']['message'] ?? 'Unknown API error';
                    throw new \Exception("Gemini API error ($errorCode): $errorMessage", 2001);
                }

                throw new \Exception(sprintf(
                    'Unexpected response format from Gemini API. Response: %s',
                    json_encode($responseData, JSON_PRETTY_PRINT)
                ), 2002);
            }
        } catch (GuzzleException $e) {
            // Handle network-related errors
            if ($e->getCode() === 28) { // CURLE_OPERATION_TIMEDOUT
                throw new \Exception('API request timed out', 2003);
            } elseif ($e->getCode() === 6) { // CURLE_COULDNT_RESOLVE_HOST
                throw new \Exception('Could not resolve API host - network issue', 2004);
            } else {
                throw new \Exception('API request failed: ' . $e->getMessage(), 2005);
            }
        }
    }

    /**
     * Extract JSON from the response text
     */
    private function extractJson($responseText)
    {
        // First try: Direct JSON parsing
        $jsonContent = json_decode(trim($responseText), true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $jsonContent;
        }

        // Second try: Use regex to find text between ``` or ```json and ```
        $pattern = '/```(?:json)?\s*(\{.*?\})\s*```/s';
        preg_match($pattern, $responseText, $matches);

        // If any matches are found, take the first one and parse it as JSON
        if (!empty($matches)) {
            try {
                // Parse the first JSON match
                $jsonContent = json_decode(trim($matches[1]), true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonContent;
                }
            } catch (\Exception $e) {
                // Failed to parse JSON inside code block
            }
        }

        // Third try: Look for any JSON-like structure in the text
        $pattern = '/\{[^\{\}]*"[^"]+"\s*:\s*[^\{\}]+\}/s';
        preg_match($pattern, $responseText, $matches);

        if (!empty($matches)) {
            try {
                $jsonContent = json_decode(trim($matches[0]), true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonContent;
                }
            } catch (\Exception $e) {
                // Failed to parse JSON
            }
        }

        return null;
    }
}
