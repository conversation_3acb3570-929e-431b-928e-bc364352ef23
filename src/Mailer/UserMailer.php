<?php

namespace App\Mailer;

use Sonata\UserBundle\Mailer\MailerInterface;
use Sonata\UserBundle\Model\UserInterface;
use Symfony\Component\Mailer\MailerInterface as SymfonyMailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;

final class UserMailer implements MailerInterface
{
    /**
     * @param array<string, string> $fromEmail
     */
    public function __construct(
        private UrlGeneratorInterface $urlGenerator,
        private Environment $twig,
        private SymfonyMailerInterface $mailer,
        private array $fromEmail,
        private string $emailTemplate,
    ) {}

    public function sendResettingEmailMessage(UserInterface $user): void
    {
        $url = $this->urlGenerator->generate('app_reset_password', [
            'token' => $user->getConfirmationToken(),
        ], UrlGeneratorInterface::ABSOLUTE_URL);

        $rendered = $this->twig->render($this->emailTemplate, [
            'user' => $user,
            'confirmationUrl' => $url,
        ]);

        // Render the email, use the first line as the subject, and the rest as the body
        $renderedLines = preg_split('/\R/', trim($rendered), 2, \PREG_SPLIT_NO_EMPTY);
        \assert(false !== $renderedLines && [] !== $renderedLines);

        $subject = array_shift($renderedLines);
        $body = implode('', $renderedLines);
        $fromName = current($this->fromEmail);
        $fromAddress = current(array_keys($this->fromEmail));

        $this->mailer->send(
            (new Email())
                ->from(\sprintf('%s <%s>', $fromName, $fromAddress))
                ->to((string) $user->getEmail())
                ->subject($subject)
                ->html($body)
        );
    }

    public function sendConfirmationEmailMessage(UserInterface $user): void
    {
        throw new \LogicException('This method is not implemented.');
    }
}
