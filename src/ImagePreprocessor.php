<?php

namespace App;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

class ImagePreprocessor
{
    private $client;

    public function __construct(?Client $client = null)
    {
        $this->client = $client ?? new Client();
    }

    /**
     * @var string $image_base64
     * @var array $options ["params' => ['param1' => 'value1', 'param2' => 'value2', 'mode' => 'local']]
     * 
     * params is an optional array that can be used to pass as query string in
     * the url
     */
    public function preprocess(string $image_base64, array $options = []): string
    {
        try {
            if (isset($options['mode']) && $options['mode'] === 'local') {
                // Local processing
                return $this->processImageLocally($image_base64);
            } else {
                // Use external service
                $url = "http://preprocessor:5000/fuel-meter";

                // Add query parameters if present in options
                if (!empty($options['params'])) {
                    $url .= '?' . http_build_query($options['params']);
                }

                $response = $this->client->post($url, [
                    'json' => [
                        "image" => "$image_base64"
                    ]
                ]);

                return $response->getBody()->getContents();
            }
        } catch (GuzzleException $e) {
            throw new Exception("Failed to preprocess image: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Failed to preprocess image: " . $e->getMessage());
        }
    }

    private function processImageLocally(string $image_base64): string
    {
        // Decode the base64 image
        $image_data = base64_decode($image_base64);
        $image = imagecreatefromstring($image_data);

        if (!$image) {
            throw new Exception("Failed to create image from base64 string.");
        }

        // Increase contrast
        imagefilter($image, IMG_FILTER_CONTRAST, -10);

        // Increase brightness
        imagefilter($image, IMG_FILTER_BRIGHTNESS, 10);

        // Convert to grayscale
        imagefilter($image, IMG_FILTER_GRAYSCALE);

        // Capture the output
        ob_start();
        imagejpeg($image);
        $processed_image_data = ob_get_contents();
        ob_end_clean();

        // Clean up
        imagedestroy($image);

        // Return the processed image as base64
        return base64_encode($processed_image_data);
    }
}
