<?php

namespace App\EventListener;

use App\Entity\SonataUserUser;
use Lexik\Bundle\JWTAuthenticationBundle\Event\JWTCreatedEvent;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;

class JWTCreatedListener
{
    /**
     * @var RequestStack
     */
    private $requestStack;

    /**
     * @var RoleHierarchyInterface
     */
    private $roleHierarchy;

    /**
     * @param RequestStack $requestStack
     */
    public function __construct(
        RequestStack $requestStack,
        RoleHierarchyInterface $roleHierarchy
    ) {
        $this->requestStack = $requestStack;
        $this->roleHierarchy = $roleHierarchy;
    }

    /**
     * @param JWTCreatedEvent $event
     *
     * @return void
     */
    public function onJWTCreated(JWTCreatedEvent $event)
    {
        /** @var SonataUserUser */
        $user = $event->getUser();
        $payload       = $event->getData();
        $payload['user_id'] = $user->getId();

        $reachableRoles = $this->roleHierarchy->getReachableRoleNames($user->getRoles());
        $payload['roles'] = $reachableRoles;

        $event->setData($payload);
    }
}
