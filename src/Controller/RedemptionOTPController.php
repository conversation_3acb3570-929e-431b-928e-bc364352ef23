<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class RedemptionOTPController extends AbstractController
{
    #[Route('/api/v1/otp/pending-orders', name: 'api_redemption_otp_generate', methods: [Request::METHOD_POST])]
    public function generateOTP(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        
        // Basic validation
        if (!isset($data['phone_number'])) {
            return $this->json(['error' => 'Phone number is required'], Response::HTTP_BAD_REQUEST);
        }

        // For now, just return 204 as per requirements
        return new Response(null, Response::HTTP_NO_CONTENT);
    }
}