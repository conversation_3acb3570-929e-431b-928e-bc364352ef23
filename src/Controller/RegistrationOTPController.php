<?php

namespace App\Controller;

use App\Entity\RegistrationOTP;
use App\Entity\SonataUserUser;
use App\Repository\RegistrationOTPRepository;
use App\Service\EmailGenerationService;
use App\Service\OTPService;
use App\Service\PhoneValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler;
use Psr\Log\LoggerInterface;
use Sonata\UserBundle\Entity\UserManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controller for handling OTP verification during registration
 *
 * This is a minimal implementation with static responses for client testing
 */
class RegistrationOTPController extends AbstractController
{
    /**
     * Send registration data and request OTP
     *
     * @param Request $request The request object
     * @param OTPService $otpService For OTP generation and sending
     * @param PhoneValidationService $phoneValidationService For phone validation
     * @param EntityManagerInterface $entityManager For database operations
     * @param LoggerInterface $logger For logging
     * @return JsonResponse Response with registration ID and expiration time
     */
    #[Route('/api/otp/register', name: 'api_otp_register', methods: [Request::METHOD_POST])]
    public function register(
        Request $request,
        OTPService $otpService,
        PhoneValidationService $phoneValidationService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);

        // Validate required fields
        if (!isset($data['mobilePhone']) || empty($data['mobilePhone'])) {
            return $this->json(['error' => 'Mobile phone is required'], Response::HTTP_BAD_REQUEST);
        }

        $mobilePhone = $data['mobilePhone'];
        $email = $data['email'] ?? null;
        $firstName = $data['firstName'] ?? null;
        $lastName = $data['lastName'] ?? null;

        // Validate phone number format
        if (!$phoneValidationService->validate($mobilePhone)) {
            return $this->json(['error' => $phoneValidationService->getErrorMessage()], Response::HTTP_BAD_REQUEST);
        }

        // Format the phone number to canonical form
        $canonicalPhone = $phoneValidationService->format($mobilePhone);

        // Check if phone number is already registered
        $tempUser = new SonataUserUser();
        $tempUser->setMobilePhone($canonicalPhone);
        $canonicalPhoneNumberToQuery = $tempUser->getMobilePhoneCanonical();

        $existingUserByPhone = $entityManager->getRepository(SonataUserUser::class)
            ->findOneBy(['mobilePhoneCanonical' => $canonicalPhoneNumberToQuery]);

        if ($existingUserByPhone) {
            return $this->json(['error' => 'Phone number already in use'], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Create registration OTP
            $registrationOTP = $otpService->createRegistrationOTP(
                $canonicalPhone,
                $email,
                $firstName,
                $lastName
            );

            // Send OTP via SMS
            $smsSent = $otpService->sendRegistrationOTP($registrationOTP);

            if (!$smsSent) {
                $logger->warning('Failed to send OTP SMS to {phoneNumber}', [
                    'phoneNumber' => $canonicalPhone,
                    'registrationId' => $registrationOTP->getRegistrationId(),
                ]);
                // Continue anyway, as the OTP is created in the database
            }

            return $this->json([
                'registrationId' => $registrationOTP->getRegistrationId(),
                'expiresIn' => $registrationOTP->getExpiresInSeconds()
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $logger->error('Error creating registration OTP: {error}', [
                'error' => $e->getMessage(),
                'phoneNumber' => $canonicalPhone,
            ]);

            return $this->json(['error' => 'Failed to create registration. Please try again.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Verify OTP and create account
     *
     * @param AuthenticationSuccessHandler $authenticationSuccessHandler JWT token generator
     * @param Request $request The request object
     * @param UserManager $userManager For creating and saving users
     * @param EmailGenerationService $emailGenerationService For generating emails from phone numbers
     * @param EntityManagerInterface $entityManager For database operations
     * @param OTPService $otpService For OTP verification
     * @param LoggerInterface $logger For logging
     * @return Response JWT token response or error
     */
    #[Route('/api/otp/register_check', name: 'api_otp_register_check', methods: [Request::METHOD_POST])]
    public function verifyOTP(
        AuthenticationSuccessHandler $authenticationSuccessHandler,
        Request $request,
        UserManager $userManager,
        EmailGenerationService $emailGenerationService,
        EntityManagerInterface $entityManager,
        OTPService $otpService,
        LoggerInterface $logger
    ): Response {
        $data = json_decode($request->getContent(), true);

        // Validate required fields
        if (!isset($data['registrationId']) || empty($data['registrationId'])) {
            return $this->json(['error' => 'Registration ID is required'], Response::HTTP_BAD_REQUEST);
        }

        if (!isset($data['code']) || empty($data['code'])) {
            return $this->json(['error' => 'OTP code is required'], Response::HTTP_BAD_REQUEST);
        }

        if (!isset($data['password']) || empty($data['password'])) {
            return $this->json(['error' => 'Password is required'], Response::HTTP_BAD_REQUEST);
        }

        $registrationId = $data['registrationId'];
        $otpCode = $data['code'];
        $password = $data['password'];

        try {
            // Verify OTP
            $result = $otpService->verifyRegistrationOTP($registrationId, $otpCode);

            if (!$result['success']) {
                return $this->json(['error' => $result['message']], Response::HTTP_BAD_REQUEST);
            }

            // Get the verified registration OTP
            $registrationOTP = $result['registrationOTP'];

            // Check if phone number is already registered (double-check)
            $canonicalPhone = $registrationOTP->getMobilePhoneCanonical();
            $existingUserByPhone = $entityManager->getRepository(SonataUserUser::class)
                ->findOneBy(['mobilePhoneCanonical' => $canonicalPhone]);

            if ($existingUserByPhone) {
                return $this->json(['error' => 'Phone number already in use'], Response::HTTP_BAD_REQUEST);
            }

            // Get registration data
            $finalPhoneNumber = $registrationOTP->getMobilePhone();

            // Email is optional, but must be unique if provided
            if ($registrationOTP->getEmail()) {
                $finalEmail = $registrationOTP->getEmail();
            } else {
                // Generate email from phone number using the email generation service
                $finalEmail = $emailGenerationService->generateFromPhone($finalPhoneNumber);
            }

            // Check if email already exists (whether provided by user or generated)
            if ($userManager->findUserByEmail($finalEmail)) {
                return $this->json(['error' => 'Email already exists'], Response::HTTP_BAD_REQUEST);
            }

            // Create user using the UserManager
            $user = $userManager->create();
            $user->setEmail($finalEmail);
            $user->setUsername($finalEmail); // Username is set to email

            // Use the password provided in the register_check request
            $user->setPlainPassword($password);

            $user->setEnabled(true);
            $user->setSuperAdmin(false);

            // Set optional fields if provided
            $user->setFirstName($registrationOTP->getFirstName() ?? '');
            $user->setLastName($registrationOTP->getLastName() ?? '');
            $user->setMobilePhone($finalPhoneNumber);

            $userManager->save($user);

            $logger->info('User created successfully from OTP registration', [
                'userId' => $user->getId(),
                'phone' => $finalPhoneNumber,
                'registrationId' => $registrationId,
            ]);

            // Generate JWT token
            $response = $authenticationSuccessHandler->handleAuthenticationSuccess($user);

            // Set the correct status code (201 Created) as per requirements
            $response->setStatusCode(Response::HTTP_CREATED);

            return $response;
        } catch (\Exception $e) {
            $logger->error('Error verifying registration OTP: {error}', [
                'error' => $e->getMessage(),
                'registrationId' => $registrationId,
            ]);

            return $this->json(['error' => 'Failed to verify OTP. Please try again.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Resend OTP for an existing registration
     *
     * @param Request $request The request object
     * @param OTPService $otpService For OTP generation and sending
     * @param LoggerInterface $logger For logging
     * @return Response No content response or error
     */
    #[Route('/api/otp/register/resend', name: 'api_otp_register_resend', methods: [Request::METHOD_POST])]
    public function resendOTP(
        Request $request,
        OTPService $otpService,
        LoggerInterface $logger
    ): Response {
        $data = json_decode($request->getContent(), true);

        // Validate required field
        if (!isset($data['registrationId']) || empty($data['registrationId'])) {
            return $this->json(['error' => 'Registration ID is required'], Response::HTTP_BAD_REQUEST);
        }

        $registrationId = $data['registrationId'];

        try {
            // Resend OTP
            $result = $otpService->resendRegistrationOTP($registrationId);

            if (!$result['success']) {
                return $this->json(['error' => $result['message']], Response::HTTP_BAD_REQUEST);
            }

            $logger->info('Registration OTP resent successfully', [
                'registrationId' => $registrationId,
            ]);

            // Return a no content response as per requirements
            return new Response(null, Response::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            $logger->error('Error resending registration OTP: {error}', [
                'error' => $e->getMessage(),
                'registrationId' => $registrationId,
            ]);

            return $this->json(['error' => 'Failed to resend OTP. Please try again.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
