<?php

namespace App\Controller;

use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Service\OrderService;
use App\Service\OTPService;
use App\Service\PhoneValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CustomerController extends AbstractController
{
    /**
     * Search for a customer by phone number
     *
     * @return JsonResponse Customer ID if found, error message if not
     */
    #[Route('/api/v1/customers/search', name: 'api_customer_search_by_phone', methods: [Request::METHOD_GET])]
    public function searchByPhone(
        Request $request,
        EntityManagerInterface $entityManager,
        PhoneValidationService $phoneValidationService
    ): JsonResponse {
        // Get phone parameter from query string
        $phone = $request->query->get('phone');

        // Basic validation
        if (!$phone) {
            return $this->json(['error' => 'Phone number is required'], Response::HTTP_BAD_REQUEST);
        }

        // Validate and format the phone number
        if (!$phoneValidationService->validate($phone)) {
            return $this->json(['error' => $phoneValidationService->getErrorMessage()], Response::HTTP_BAD_REQUEST);
        }

        // Format the phone number to canonical form
        $canonicalPhone = $phoneValidationService->format($phone);

        // Find user by phone
        $user = $entityManager->getRepository(SonataUserUser::class)
            ->findOneBy(['mobilePhoneCanonical' => $canonicalPhone]);

        if (!$user) {
            return $this->json(['error' => 'Customer not found'], Response::HTTP_NOT_FOUND);
        }

        return $this->json([
            'id' => $user->getId()
        ]);
    }

    /**
     * Get available points for a customer
     *
     * @param int $customerId The customer ID
     * @param EntityManagerInterface $entityManager The entity manager
     * @return JsonResponse Available points or error message
     */
    #[Route('/api/v1/customers/{customerId}/points', name: 'api_customer_points', methods: [Request::METHOD_GET])]
    public function getCustomerPoints(int $customerId, EntityManagerInterface $entityManager): JsonResponse
    {
        // Basic validation for customer ID
        if ($customerId <= 0) {
            return $this->json(['error' => 'Customer not found'], Response::HTTP_NOT_FOUND);
        }

        // Find customer
        $customer = $entityManager->getRepository(SonataUserUser::class)->find($customerId);
        if (!$customer) {
            return $this->json(['error' => 'Customer not found'], Response::HTTP_NOT_FOUND);
        }

        // Return the customer's available points
        return $this->json([
            'availablePoints' => $customer->getPoints()
        ]);
    }

    /**
     * Generate OTP for order verification
     *
     * @param Request $request The request object
     * @param int $customerId The customer ID
     * @param EntityManagerInterface $entityManager The entity manager
     * @param OTPService $otpService The OTP service
     * @param LoggerInterface $logger The logger
     * @return Response 204 No Content on success, error message otherwise
     */
    #[Route('/api/v1/customers/{customerId}/generate-otp', name: 'api_customer_generate_otp', methods: [Request::METHOD_POST])]
    public function generateOtp(
        Request $request,
        int $customerId,
        EntityManagerInterface $entityManager,
        OTPService $otpService,
        LoggerInterface $logger
    ): Response {
        // Get request body
        $data = json_decode($request->getContent(), true);

        // Basic validation
        if (!isset($data['phoneNumber']) || !isset($data['orderId'])) {
            return $this->json(['error' => 'Phone number and order ID are required'], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Find customer
            $customer = $entityManager->getRepository(SonataUserUser::class)->find($customerId);
            if (!$customer) {
                return $this->json(['error' => 'Customer not found'], Response::HTTP_NOT_FOUND);
            }

            // Find order
            $order = $entityManager->getRepository(RewardsOrder::class)->findOneBy(['orderNumber' => $data['orderId']]);
            if (!$order) {
                return $this->json(['error' => 'Order not found'], Response::HTTP_NOT_FOUND);
            }

            // Create OTP verification
            $otpVerification = $otpService->createOTPVerification($order, $customer);

            // Send OTP via SMS
            $phoneNumber = $data['phoneNumber'];
            $smsSent = $otpService->sendOTP($otpVerification, $phoneNumber);

            if (!$smsSent) {
                $logger->warning('Failed to send OTP SMS to {phoneNumber} for order {orderId}', [
                    'phoneNumber' => $phoneNumber,
                    'orderId' => $order->getOrderNumber(),
                    'customerId' => $customer->getId(),
                ]);
                // Continue anyway, as the OTP is created in the database
            }

            // Return a 204 No Content response
            return new Response(null, Response::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            $logger->error('Error generating OTP: {error}', [
                'error' => $e->getMessage(),
                'customerId' => $customerId,
                'orderId' => $data['orderId'] ?? 'unknown',
            ]);

            return $this->json(['error' => 'Failed to generate OTP'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Verify OTP for order completion
     *
     * @param Request $request The request object
     * @param int $customerId The customer ID
     * @param EntityManagerInterface $entityManager The entity manager
     * @param OTPService $otpService The OTP service
     * @param OrderService $orderService The order service
     * @param LoggerInterface $logger The logger
     * @return Response 204 No Content on success, error message otherwise
     */
    #[Route('/api/v1/customers/{customerId}/verify-otp', name: 'api_customer_verify_otp', methods: [Request::METHOD_POST])]
    public function verifyOtp(
        Request $request,
        int $customerId,
        EntityManagerInterface $entityManager,
        OTPService $otpService,
        OrderService $orderService,
        LoggerInterface $logger
    ): Response {
        // Get request body
        $data = json_decode($request->getContent(), true);

        // Basic validation
        if (!isset($data['orderId']) || !isset($data['otp'])) {
            return $this->json(['error' => 'Order ID and OTP are required'], Response::HTTP_BAD_REQUEST);
        }

        // Validate OTP format (assuming it's a 6-digit code)
        if (!preg_match('/^\d{6}$/', $data['otp'])) {
            return $this->json(['error' => 'Invalid OTP format'], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Find customer
            $customer = $entityManager->getRepository(SonataUserUser::class)->find($customerId);
            if (!$customer) {
                return $this->json(['error' => 'Customer not found'], Response::HTTP_NOT_FOUND);
            }

            // Find order
            $order = $entityManager->getRepository(RewardsOrder::class)->findOneBy(['orderNumber' => $data['orderId']]);
            if (!$order) {
                return $this->json(['error' => 'Order not found'], Response::HTTP_NOT_FOUND);
            }

            // Verify OTP
            $result = $otpService->verifyOTP($order, $customer, $data['otp']);

            if (!$result['success']) {
                return $this->json(['error' => $result['message']], Response::HTTP_BAD_REQUEST);
            }

            // Check if a points transaction exists for this order
            if (!$orderService->hasPointsTransaction($order)) {
                // Create a points transaction for the order
                $orderService->createPointsTransaction($order, $customer);
            }

            // Return a 204 No Content response on success
            return new Response(null, Response::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            $logger->error('Error verifying OTP: {error}', [
                'error' => $e->getMessage(),
                'customerId' => $customerId,
                'orderId' => $data['orderId'] ?? 'unknown',
            ]);

            return $this->json(['error' => 'Failed to verify OTP'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
