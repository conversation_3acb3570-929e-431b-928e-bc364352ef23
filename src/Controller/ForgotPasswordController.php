<?php

namespace App\Controller;

use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Sonata\UserBundle\Mailer\MailerInterface;
use Sonata\UserBundle\Model\UserManagerInterface;
use Sonata\UserBundle\Util\TokenGeneratorInterface;

#[Route('/api/v1/forgot-password', name: 'api_forgot_password', methods: [Request::METHOD_POST])]
class ForgotPasswordController
{
    public function __construct(
        private UserManagerInterface $userManager,
        private MailerInterface $mailer,
        private TokenGeneratorInterface $tokenGenerator,
        private int $retryTtl,
    ) {}

    public function __invoke(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $username = $data['email'];
        $user = $this->userManager->findUserByUsernameOrEmail($username);

        if (
            null !== $user && $user->isEnabled() &&
            !$user->isPasswordRequestNonExpired($this->retryTtl) &&
            $user->isAccountNonLocked()
        ) {
            if (null === $user->getConfirmationToken()) {
                $user->setConfirmationToken($this->tokenGenerator->generateToken());
            }

            $this->mailer->sendResettingEmailMessage($user);
            $user->setPasswordRequestedAt(new \DateTime());
            $this->userManager->save($user);
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }
}
