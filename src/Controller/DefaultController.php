<?php

namespace App\Controller;

use App\Entity\ExclusiveOffer;
use App\Entity\FillingStation;
use App\Entity\FillingStationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\LotteryTicket;
use App\Entity\PaymentDetails;
use App\Entity\RewardsItem;
use App\Entity\RewardsItemInventoryMovement;
use App\Entity\RewardsOrder;
use App\Entity\SonataMediaMedia;
use App\Entity\SonataUserUser;
use App\Entity\UserPointsTransaction;
use App\Entity\Vehicle;
use App\FuelMeterScanner;
use App\Service\EmailGenerationService;
use League\OAuth2\Client\Provider\Google;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler;
use Sonata\MediaBundle\Provider\ImageProvider;
use Sonata\UserBundle\Entity\UserManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

use function PHPUnit\Framework\isEmpty;

class DefaultController extends AbstractController
{
    #[Route('/reset-password-success', name: 'app_reset_password_success', methods: [Request::METHOD_GET])]
    public function resetPasswordSuccess(): Response
    {
        return $this->render('reset_password_success.html.twig');
    }

    #[Route('/privacy_policy', name: 'app_privacy_policy', methods: [Request::METHOD_GET])]
    public function privacyPolicy(): Response
    {
        return $this->render('privacy_policy.html.twig');
    }

    #[Route('/pro/privacy_policy', name: 'app_pro_privacy_policy', methods: [Request::METHOD_GET])]
    public function proPrivacyPolicy(): Response
    {
        return $this->render('pro_privacy_policy.html.twig');
    }

    #[Route('/account/delete', name: 'app_account_delete', methods: [Request::METHOD_GET])]
    public function deleteAccount(): Response
    {
        return $this->render('delete_account.html.twig');
    }

    #[Route('/api/register', name: 'api_register', methods: ['POST'])]
    public function register(
        UserManager $userManager, // Keep UserManager for saving the user
        Request $request,
        EmailGenerationService $emailGenerationService,
        EntityManagerInterface $entityManager // Inject EntityManagerInterface
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);

        // Validate required field: mobilePhone
        if (!isset($data['mobilePhone']) || empty($data['mobilePhone'])) {
            return $this->json(['error' => 'Mobile phone is required'], Response::HTTP_BAD_REQUEST);
        }
        $finalPhoneNumber = $data['mobilePhone'];

        // Validate phone number format and uniqueness
        $tempUserForCanonical = new SonataUserUser();
        $tempUserForCanonical->setMobilePhone($finalPhoneNumber);
        $canonicalPhoneNumberToQuery = $tempUserForCanonical->getMobilePhoneCanonical();
        if (!$canonicalPhoneNumberToQuery) {
            return $this->json(['error' => 'Invalid phone number format.'], Response::HTTP_BAD_REQUEST);
        }
        $existingUserByPhone = $entityManager->getRepository(SonataUserUser::class)->findOneBy(['mobilePhoneCanonical' => $canonicalPhoneNumberToQuery]);
        if ($existingUserByPhone) {
            return $this->json(['error' => 'Phone number already in use.'], Response::HTTP_BAD_REQUEST);
        }

        // Email is optional, but must be unique if provided
        if (isset($data['email']) && !empty($data['email'])) {
            $finalEmail = $data['email'];
        } else {
            // Generate email from phone number using the email generation service
            $finalEmail = $emailGenerationService->generateFromPhone($finalPhoneNumber);
        }

        // Check if email already exists (whether provided by user or generated)
        if ($userManager->findUserByEmail($finalEmail)) {
            return $this->json(['error' => 'Email already exists.'], Response::HTTP_BAD_REQUEST);
        }

        // Validate required field: password
        if (!isset($data['password']) || empty($data['password'])) {
            return $this->json(['error' => 'Password is required'], Response::HTTP_BAD_REQUEST);
        }

        // Create user using the UserManager
        $user = $userManager->create();
        $user->setEmail($finalEmail);
        $user->setUsername($finalEmail ?? $finalPhoneNumber); // Username is set to email if present, else phone
        $user->setPlainPassword($data['password']);
        $user->setEnabled(true);
        $user->setSuperAdmin(false);

        // Set optional fields if provided
        $user->setFirstName($data['firstName'] ?? '');
        $user->setLastName($data['lastName'] ?? '');
        $user->setMobilePhone($finalPhoneNumber);

        $userManager->save($user);

        return $this->json(['id' => $user->getId()], Response::HTTP_CREATED);
    }

    #[Route('/api/v1/users/{userId}', name: 'api_user_update_profile', methods: [Request::METHOD_PUT])]
    public function updateProfile(Request $request, UserManager $userManager, string $userId, EntityManagerInterface $entityManager): JsonResponse
    {
        /** @var SonataUserUser */
        $user = $this->getUser();
        // Ensure the current user is the one being updated or is an admin
        if ($user->getId() != $userId && !$this->isGranted('ROLE_ADMIN')) {
            return $this->json(['error' => 'Unauthorized'], Response::HTTP_FORBIDDEN);
        }

        // If an admin is editing, load the user by $userId
        if ($user->getId() != $userId && $this->isGranted('ROLE_ADMIN')) {
            $userToEdit = $entityManager->getRepository(SonataUserUser::class)->find($userId);
            if (!$userToEdit) {
                return $this->json(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
            }
            $user = $userToEdit; // The user being edited
        }

        $json = json_decode($request->getContent(), true);

        // Handle phone number update and uniqueness check
        if (isset($json['mobilePhone'])) {
            $newPhoneNumber = $json['mobilePhone'];
            if (!empty($newPhoneNumber)) {
                // To get the canonical form for checking, we can use a temporary entity instance
                $tempUserForCanonical = new SonataUserUser();
                $tempUserForCanonical->setMobilePhone($newPhoneNumber); // This sets the canonical version internally
                $canonicalPhoneNumberToQuery = $tempUserForCanonical->getMobilePhoneCanonical();

                if ($canonicalPhoneNumberToQuery) {
                    $existingUserByPhone = $entityManager->getRepository(SonataUserUser::class)->createQueryBuilder('u')
                        ->where('u.mobilePhoneCanonical = :canonicalPhone')
                        ->andWhere('u.id != :currentUserId')
                        ->setParameter('canonicalPhone', $canonicalPhoneNumberToQuery)
                        ->setParameter('currentUserId', $user->getId())
                        ->getQuery()
                        ->getOneOrNullResult();

                    if ($existingUserByPhone) {
                        return $this->json(['error' => 'Phone number already in use by another account.'], Response::HTTP_BAD_REQUEST);
                    }
                }
                $user->setMobilePhone($newPhoneNumber); // This will also set the canonical version on the actual user
            } else {
                // If an empty phone number is provided, set it to null
                $user->setMobilePhone(null);
                // The entity setter for mobilePhone should handle setting mobilePhoneCanonical to null as well if mobilePhone is null.
                // If not, explicitly set: $user->setMobilePhoneCanonical(null);
            }
        }

        $user->setFirstname($json['first_name'] ?? $user->getFirstname());
        $user->setLastname($json['last_name'] ?? $user->getLastname());

        // Handle email update - ensure it's not conflicting if changed
        if (isset($json['email']) && $json['email'] !== $user->getEmail()) {
            $newEmail = $json['email'];
            if (!empty($newEmail)) {
                $existingUserByEmail = $userManager->findUserByEmail($newEmail);
                if ($existingUserByEmail && $existingUserByEmail->getId() !== $user->getId()) {
                    return $this->json(['error' => 'Email already in use by another account.'], Response::HTTP_BAD_REQUEST);
                }
                $user->setEmail($newEmail);
                $user->setUsername($newEmail); // Typically username is email
            } else if (empty($newEmail) && isset($json['mobilePhone'])) {
                // If email is emptied, and phone is available, consider generating one if that's a desired feature
                // For now, let's assume an empty email in update means to clear it if allowed by entity constraints
                // However, SonataUserUser typically requires an email. This logic might need adjustment
                // based on whether auto-generation should also apply on updates.
            }
        }

        $user->setDistrict($json['district'] ?? $user->getDistrict());
        $user->setIdCardNumber($json['idCardNumber'] ?? $user->getIdCardNumber());
        $user->setAddress($json['address'] ?? $user->getAddress());
        $dob = $json['dob'] ?? null;
        try {
            if ($dob) {
                $parsedDOB = \DateTime::createFromFormat('Y-m-d', $json['dob']);
                $user->setDob($parsedDOB);
            }
        } catch (\Exception $e) {
            // ignore error
        }

        $password = $json['password'] ?? '';
        if (!empty($password)) {
            $user->setPlainPassword($password);
        }

        if (
            isset($json['vehicleNumber']) ||
            isset($json['model']) ||
            isset($json['fuel']) ||
            isset($json['vehicleType'])
        ) {
            $vehicles = $user->getVehicles();
            if (count($vehicles) === 0) {
                $vehicle = new Vehicle();
                $vehicle->setDriver($user);
                $vehicles->add($vehicle);
            } else {
                $vehicle = $vehicles[0];
            }
            $vehicle->setLicensePlate($json['vehicleNumber']);
            $vehicle->setModel($json['model']);
            $vehicle->setFuel($json['fuel']);
            $vehicle->setVehicleType($json['vehicleType']);
        }
        $userManager->save($user);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    #[Route('/api/v1/users/{userId}', name: 'api_user_profile', methods: [Request::METHOD_GET])]
    public function getProfile(
        EntityManagerInterface $entityManager,
        ImageProvider $provider,
        EmailGenerationService $emailGenerationService,
        string $userId // Required for route but not used directly
    ): JsonResponse {
        /** @var SonataUserUser */
        $user = $this->getUser();
        $vehicles = $user->getVehicles() ?? [];
        $vehicle = count($vehicles) > 0 ? $vehicles[0] : new Vehicle();
        $picture =
            $user->getPicture() === null ? null :
            $provider->generatePublicUrl(
                $user->getPicture(),
                'reference'
            );

        // Check if email is auto-generated
        $email = $user->getEmail();
        $includeEmail = !$emailGenerationService->isAutoGenerated($email);

        $userData = [
            'id' => (int) $user->getId(),
            'firstName' => $user->getFirstname(),
            'lastName' => $user->getLastname(),
            'points' => $user->getPoints() ?? 0,
            'mobilePhone' => $user->getMobilePhone(),
            'first_name' => $user->getFirstname(),
            'last_name' => $user->getLastname(),
            'dob' => $user->getDob() ? $user->getDob()->format('Y-m-d') : null,
            'address' => $user->getAddress(),
            'district' => $user->getDistrict(),
            'idCardNumber' => $user->getIdCardNumber(),
            'vehicleNumber' => $vehicle->getLicensePlate(),
            'model' => $vehicle->getModel(),
            'fuel' => $vehicle->getFuel(),
            'vehicleType' => $vehicle->getVehicleType(),
            'picture' => $picture,
        ];

        // Always include email field, but set to empty string if auto-generated
        $userData['email'] = $includeEmail ? $email : '';

        return new JsonResponse($userData);
    }

    #[Route('/api/v1/users/{userId}/delete', name: 'api_user_delete', methods: [Request::METHOD_POST])]
    public function apiDeleteAccount(
        EntityManagerInterface $entityManager,
        string $userId // Required for route but not used directly
    ): Response {
        /** @var SonataUserUser */
        $user = $this->getUser();
        $user->setEmail(uniqid() . '@deleted.iol.ids.ac');
        $user->setUsername($user->getEmail());
        $user->setFirstname('Deleted');
        $user->setLastname('Deleted');
        $entityManager->persist($user);
        $entityManager->flush();

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    #[Route('/api/v1/users/{userId}/tickets', name: 'api_tickets', methods: ['GET'])]
    public function getTickets(EntityManagerInterface $entityManager, string $userId): JsonResponse
    {
        $repository = $entityManager->getRepository(LotteryTicket::class);
        $tickets = $repository->findBy(['assignTo' => $userId]);

        // Convert the list of LotteryTicket entities to an array of arrays for JSON response
        $ticketData = array_map(function (LotteryTicket $ticket) {
            return $ticket->getNumber();
        }, $tickets);

        // Return the list as JSON
        return $this->json($ticketData);
    }

    #[Route('/api/v1/payments-details', name: 'api_payment_details_create', methods: ['POST'])]
    public function savePaymentDetails(EntityManagerInterface $entityManager, Request $request): Response
    {
        try {
            $userRepo = $entityManager->getRepository(SonataUserUser::class);
            $stationRepo = $entityManager->getRepository(FillingStation::class);

            // Save the payment details
            $requestContent = json_decode($request->getContent(), true);
            /** @var SonataUserUser */
            $requestUser = $this->getUser();
            $customerUid = !empty($requestContent['customerUser'])
                ? $requestContent['customerUser']
                : $requestUser->getId();
            $fillingStationId = $requestContent['fillingStationId'];
            $entityManager->beginTransaction();
            /** @var SonataUserUser */
            $customerUser = $userRepo->find($customerUid);
            if (!$customerUser) {
                $entityManager->rollback();
                return new Response("User not found, ID: $customerUid", Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            $fillingStation = $stationRepo->find($fillingStationId);
            if (!$fillingStation) {
                $entityManager->rollback();
                return new Response('Filling station not found', Response::HTTP_BAD_REQUEST);
            }

            /** @var SonataUserUser */
            $createdBy = $this->getUser();
            $paymentDetails = PaymentDetails::fromArray($requestContent, $customerUser, $createdBy, $fillingStation);
            $pointsEarned = $paymentDetails->getPoints();
            $customerUser->addPoints($pointsEarned);

            // Create points transaction record for earned points
            $pointsTransaction = UserPointsTransaction::createEarnTransaction(
                $customerUser,
                $pointsEarned,
                'Earned from purchase at ' . $fillingStation->getName(),
                $paymentDetails
            );

            $entityManager->persist($paymentDetails);
            $entityManager->persist($customerUser);
            $entityManager->persist($pointsTransaction);
            $entityManager->flush();
            $entityManager->commit();

            return new Response('', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            $entityManager->rollback();
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/api/v1/fuel-meter/scan', name: 'api_fuel_meter_scan', methods: ['POST'])]
    public function scanFuelMeter(FuelMeterScanner $scanner, Request $request, \Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface $parameterBag): Response
    {
        $file = $request->files->get('file');

        if (!$file) {
            return new JsonResponse(['error' => 'No file uploaded'], Response::HTTP_BAD_REQUEST);
        }

        $uploadDir = 'uploads/fuel_meter';
        $filename = uniqid() . '.' . $file->guessExtension();
        $path = '';

        try {
            // Move the uploaded file
            $file->move($uploadDir, $filename);
            $path = $uploadDir . '/' . $filename;

            // Get the Gemini API key
            $apiKey = $parameterBag->get('env(GEMINI_API_KEY)');

            // Validate API key is not empty
            if (empty($apiKey)) {
                throw new \Exception('Gemini API key is not configured', 3001);
            }

            // Use scanWithGemini with the file path and API key
            $scannedData = $scanner->scanWithGemini($path, $apiKey);

            // Add the image path to the response
            $scannedData['image'] = '/' . $path;
            $scannedData['success'] = true;

            return new JsonResponse($scannedData, Response::HTTP_OK);
        } catch (\Exception $e) {
            // Log the error
            error_log('Error in scanFuelMeter: ' . $e->getMessage() . ' [Code: ' . $e->getCode() . ']');

            // Determine error type based on exception code
            $errorType = $this->getErrorTypeFromCode($e->getCode());

            // Create standardized zero-value response
            $response = [
                'amount' => 0,
                'litres' => 0,
                'unit_price' => 0,
                'fuel_type' => 'unknown',
                'success' => false,
                'error_code' => $errorType,
                'error_details' => [
                    'message' => $e->getMessage(),
                    'type' => $this->getErrorTypeDescription($errorType)
                ]
            ];

            // Add image path if available
            if (!empty($path)) {
                $response['image'] = '/' . $path;
            }

            // Always return HTTP 200 with zero values for all caught exceptions
            return new JsonResponse($response, Response::HTTP_OK);
        }
    }

    /**
     * Map exception codes to error types
     */
    private function getErrorTypeFromCode(int $code): string
    {
        return match (true) {
            $code >= 1001 && $code <= 1003 => 'INVALID_IMAGE',
            $code === 1004 => 'SCAN_FAILED',
            $code >= 2001 && $code <= 2005 => 'API_ERROR',
            $code === 3001 => 'SYSTEM_ERROR',
            default => 'SCAN_FAILED',
        };
    }

    /**
     * Get human-readable description for error types
     */
    private function getErrorTypeDescription(string $errorType): string
    {
        return match ($errorType) {
            'INVALID_IMAGE' => 'image',
            'CORRUPT_IMAGE' => 'image',
            'SCAN_FAILED' => 'processing',
            'API_ERROR' => 'api',
            'SYSTEM_ERROR' => 'system',
            default => 'unknown',
        };
    }

    #[Route('/api/v1/rewards-items', name: 'api_rewards_items_list', methods: [Request::METHOD_GET])]
    public function  cgetRewardsItems(
        EntityManagerInterface $entityManager,
        ImageProvider $provider
    ): JsonResponse {
        $items = $entityManager->getRepository(RewardsItem::class)->findBy([], ['points' => 'ASC']);
        $data = array_map(function (RewardsItem $item) use ($provider) {
            $url =
                $item->getFeaturedImage() === null ? null :
                $provider->generatePublicUrl(
                    $item->getFeaturedImage(),
                    'reference'
                );

            return [
                'id' => $item->getId(),
                'name' => $item->getName(),
                'points' => $item->getPoints(),
                'featuredImage' => $url,
                'quantity' => $item->getQuantity(),
                'description' => $item->getDescription(),
            ];
        }, $items);
        return new JsonResponse($data);
    }

    #[Route('/api/v1/rewards-orders', name: 'api_rewards_order_create', methods: [Request::METHOD_POST])]
    public function createRewardsOrder(
        EntityManagerInterface $entityManager,
        Request $request
    ): Response {
        $entityManager->beginTransaction();
        try {
            $data = json_decode($request->getContent(), true);
            if (!isset($data['itemId'])) {
                $entityManager->rollback();
                return new JsonResponse(
                    ['error' => 'Item ID is required'],
                    Response::HTTP_BAD_REQUEST
                );
            }

            /** @var RewardsItem */
            $item = $entityManager->getRepository(RewardsItem::class)
                ->find($data['itemId']);
            if (!$item) {
                $entityManager->rollback();
                return new JsonResponse(
                    ['error' => 'Item not found'],
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Check if item is in stock
            if ($item->getQuantity() <= 0) {
                $entityManager->rollback();
                return new JsonResponse(
                    ['error' => 'Item is out of stock'],
                    Response::HTTP_BAD_REQUEST
                );
            }

            /** @var SonataUserUser */
            $user = $this->getUser();
            if (!$user) {
                $entityManager->rollback();
                return new JsonResponse(
                    ['error' => 'User not authenticated'],
                    Response::HTTP_UNAUTHORIZED
                );
            }

            // Check if user has enough points
            if ($user->getPoints() < $item->getPoints()) {
                $entityManager->rollback();
                return new JsonResponse(
                    ['error' => 'Insufficient points balance'],
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Deduct points from user's balance
            $pointsToDeduct = $item->getPoints();
            $user->setPoints($user->getPoints() - $pointsToDeduct);
            $entityManager->persist($user);

            // Decrease item quantity
            $item->setQuantity($item->getQuantity() - 1);
            $entityManager->persist($item);

            // Create order
            $order = new RewardsOrder();
            $order->setState(RewardsOrder::STATUS_PENDING);
            $order->setCreatedAt(new \DateTimeImmutable());
            $order->setUser($user);
            $order->setItem($item);

            // Manually set a unique order number to avoid lifecycle callback issues
            $maxOrderNumber = $entityManager->getRepository(RewardsOrder::class)
                ->createQueryBuilder('o')
                ->select('MAX(o.number)')
                ->getQuery()
                ->getSingleScalarResult();

            // If there are no orders yet, $maxOrderNumber will be null, so we start at 1
            $currentMax = $maxOrderNumber ? (int) $maxOrderNumber : 0;
            $orderNumber = (string) ($currentMax + 1);
            $order->setNumber($orderNumber);
            $order->setOrderNumber('ORD-' . str_pad($orderNumber, 5, '0', STR_PAD_LEFT));

            $entityManager->persist($order);

            // First flush to get the order ID and ensure order number is set
            $entityManager->flush();

            // Create inventory movement record
            $inventoryMovement = new RewardsItemInventoryMovement();
            $inventoryMovement->setItem($item);
            $inventoryMovement->setQuantity(-1);
            $inventoryMovement->setMovementType(RewardsItemInventoryMovement::MOVEMENT_TYPE_DECREASE);
            $inventoryMovement->setReason('Order #' . $order->getOrderNumber());
            $entityManager->persist($inventoryMovement);

            // Create points transaction record
            $pointsTransaction = UserPointsTransaction::createSpendTransaction(
                $user,
                $pointsToDeduct,
                'Redeemed for ' . $item->getName() . ' (Order #' . $order->getOrderNumber() . ')',
                $order
            );
            $entityManager->persist($pointsTransaction);

            $entityManager->flush();
            $entityManager->commit();

            return new JsonResponse(
                ['id' => $order->getId()],
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            // Make sure to rollback the transaction on any exception
            if ($entityManager->getConnection()->isTransactionActive()) {
                $entityManager->rollback();
            }

            // Log the detailed error for debugging
            error_log('Error in createRewardsOrder: ' . $e->getMessage() . '\n' . $e->getTraceAsString());

            return new JsonResponse(
                ['error' => 'An error occurred while processing your order: ' . $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    #[Route('/api/v1/filling-stations', name: 'api_filling_station_list', methods: [Request::METHOD_GET])]
    public function cgetFillingStation(
        EntityManagerInterface $em,
        ImageProvider $provider
    ) {
        $stations = $em->getRepository(FillingStation::class)
            ->findBy([], ['name' => 'ASC']);
        $content = array_map(function (FillingStation $station) use ($provider) {
            $url =
                $station->getFeaturedImage() === null ? null :
                $provider->generatePublicUrl(
                    $station->getFeaturedImage(),
                    'reference'
                );
            /** @var array */
            $services = array_map(function ($service) {
                return $service->getCode();
            }, $station->getServices()->toArray());
            return [
                "id" => $station->getId(),
                "station" => $station->getName(),
                "address" => $station->getAddress(),
                "phone" => $station->getPhone(),
                "open" => 6,
                "closed" => 9,
                "petrol" => $this->getServiceState($services, 'petrol'),
                "diesel" => $this->getServiceState($services, "diesel"),
                "gaz" => $this->getServiceState($services, "gaz"),
                "airPressure" => $this->getServiceState($services, "airPressure"),
                "carWash" => $this->getServiceState($services, "carWash"),
                "store" => $this->getServiceState($services, "store"),
                "greasePump" => $this->getServiceState($services, "greasePump"),
                "water" => $this->getServiceState($services, "water"),
                "escale" => $this->getServiceState($services, "escale"),
                "ice" => $this->getServiceState($services, "ice"),
                "geo" => $station->getGeolocation(),
                "cover" => $url,
            ];
        }, $stations);
        return new JsonResponse($content);
    }

    #[Route('/api/v1/filling-stations/{id}/services/{code}', name: 'api_filling_station_service', methods: [Request::METHOD_GET])]
    public function getFillingStationService(
        EntityManagerInterface $em,
        ImageProvider $provider,
        int $id,
        String $code,
    ) {
        /** @var FillingStation */
        $station = $em->getRepository(FillingStation::class)->find($id);
        if (!$station) {
            return new JsonResponse(['error' => 'Station not found'], Response::HTTP_NOT_FOUND);
        }

        foreach ($station->getServiceDetails() as $details) {
            if ($details->getService()->getCode() === $code) {
                $service = $details->getService();
                $imgUrl =
                    $details->getFeaturedImage() === null ? null :
                    $provider->generatePublicUrl(
                        $details->getFeaturedImage(),
                        'reference'
                    );

                $content = [
                    'id' => $service->getId(),
                    'title' => $service->getName(),
                    'description' => $details->getDescription(),
                    'imgUrl' => $imgUrl,
                ];

                return new JsonResponse($content);
            }
        }


        /** @var FillingStationService */
        $service = $em->getRepository(FillingStationService::class)->findOneBy([
            'code' => $code,
        ]);
        if (!$service) {
            return new JsonResponse(['error' => 'Service not found'], Response::HTTP_NOT_FOUND);
        }

        $imgUrl =
            $service->getFeaturedImage() === null ? null :
            $provider->generatePublicUrl(
                $station->getFeaturedImage(),
                'reference'
            );

        $content = [
            'id' => $service->getId(),
            'title' => $service->getName(),
            'description' => $service->getDescription(),
            'imgUrl' => $imgUrl,
        ];

        return new JsonResponse($content);
    }

    #[Route('/api/otp/login', name: 'api_otp_send_code', methods: [Request::METHOD_POST])]
    public function sendVerificationCode(
        UserManager $userManager,
        Request $request
    ): Response {
        $data = json_decode($request->getContent(), true);
        $phone = SonataUserUser::sanitizePhoneNumber($data['phone']);
        $user = $userManager->findOneBy(['mobilePhoneCanonical' => $phone]);
        if (!$user) {
            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        $code = (string) random_int(100000, 999999);
        $user->setOtp($code);
        $user->setOtpValidUntil(new \DateTime('+5 minutes'));
        $userManager->save($user);
        sendSingleMessage($phone, 'Your verification code is ' . $code);

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/api/otp/login_check', name: 'api_otp_verify_code', methods: [Request::METHOD_POST])]
    public function verifyCode(
        AuthenticationSuccessHandler $authenticationSuccessHandler,
        UserManager $userManager,
        Request $request
    ): Response {
        $data = json_decode($request->getContent(), true);
        $phone = SonataUserUser::sanitizePhoneNumber($data['phone']);
        $user = $userManager->findOneBy(['mobilePhoneCanonical' => $phone]);
        if (!$user) {
            return new JsonResponse(
                ['error' => 'OTP is invalid'],
                Response::HTTP_NOT_FOUND,
            );
        }

        if ($user->getOtp() !== $data['code']) {
            return new JsonResponse(
                ['error' => 'OTP is invalid'],
                Response::HTTP_BAD_REQUEST
            );
        }

        return $authenticationSuccessHandler->handleAuthenticationSuccess($user);
    }

    #[Route('/api/google/login_check', name: 'api_google_login_check', methods: [Request::METHOD_POST])]
    public function googleLoginCheck(
        AuthenticationSuccessHandler $authenticationSuccessHandler,
        UserManager $userManager,
        Request $request
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        $code = $data['code'];
        $provider = new Google([
            'clientId'     => '647169373585-982jm6b06ot97rc7m73su7gs8r1kb31f.apps.googleusercontent.com',
            'clientSecret' => '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',
        ]);
        $accessToken = $provider->getAccessToken('authorization_code', [
            'code' => $code,
        ]);
        $googleUser = $provider->getResourceOwner($accessToken);
        $userDetails = $googleUser->toArray();
        $email = $userDetails['email'] ?? '';

        /** @var SonataUserUser */
        $user = $userManager->findOneBy(['email' => $email]);
        if (!$user) {
            return new JsonResponse(
                ['error' => 'User not found'],
                Response::HTTP_BAD_REQUEST
            );
        }

        return $authenticationSuccessHandler->handleAuthenticationSuccess($user);
    }

    #[Route('/benchmark', name: 'benchmark', methods: [Request::METHOD_GET])]
    public function getBenchmark()
    {
        $dataset = json_decode(file_get_contents('./uploads/cmd/fuel_meter_scanner/results.json'), true);

        return $this->render('benchmark.html.twig', ['dataset' => $dataset]);
    }

    #[Route('/api/v1/exclusive-offers', name: 'api_exclusive_offers', methods: [Request::METHOD_GET])]
    public function getExclusiveOffers(
        EntityManagerInterface $entityManager,
        ImageProvider $provider
    ): JsonResponse {
        $offers = $entityManager->getRepository(ExclusiveOffer::class)
            ->findBy(['active' => true], ['publishedAt' => 'DESC']);

        $data = array_map(function (ExclusiveOffer $offer) use ($provider) {
            $image =
                $offer->getFeaturedImage() === null ? null :
                $provider->generatePublicUrl(
                    $offer->getFeaturedImage(),
                    'reference'
                );

            return [
                'id' => $offer->getId(),
                'title' => $offer->getTitle(),
                'description' => $offer->getDescription(),
                'featuredImage' => $image,
                'url' => $offer->getUrl(),
            ];
        }, $offers);
        return new JsonResponse($data);
    }

    #[Route('/api/v1/users/{userId}/transactions', name: 'api_user_transactions', methods: [Request::METHOD_GET])]
    public function getUserTransactions(EntityManagerInterface $entityManager)
    {
        $user = $this->getUser();
        $paymentDetails = $entityManager->getRepository(PaymentDetails::class)
            ->findBy(['customer_user' => $user], ['created_at' => 'DESC']);

        $data = array_map(function (PaymentDetails $payment) {
            $litres = (float)$payment->getLitres();
            $unitPrice = $litres > 0 ?
                (float)($payment->getAmount() / $litres) : 0;

            // Determine fuel type based on unit price, using same logic as FuelMeterScanner
            $fuelType = match (number_format($unitPrice, 2)) {
                '61.20' => PaymentDetails::FUEL_TYPE_GASOLINE,
                '58.95' => PaymentDetails::FUEL_TYPE_DIESEL,
                default => PaymentDetails::FUEL_TYPE_GASOLINE,  // default to gasoline
            };

            return [
                'id' => $payment->getId(),
                'stationName' => $payment->getFillingStation()?->getName() ?? '',
                'createdAt' => (int) $payment->getCreatedAt()->getTimestamp() * 1000,
                'amount' => (float) $payment->getAmount(),
                'points' => (int) $payment->getPoints(),
                'fuelType' => $fuelType,
                'litres' => $litres,
                'unitPrice' => $unitPrice,
            ];
        }, $paymentDetails);

        return new JsonResponse($data);
    }

    #[Route('/api/v1/users/{userId}/points-transactions', name: 'api_user_points_transactions', methods: [Request::METHOD_GET])]
    public function getUserPointsTransactions(EntityManagerInterface $entityManager): JsonResponse
    {
        /** @var SonataUserUser */
        $user = $this->getUser();
        $transactions = $entityManager->getRepository(UserPointsTransaction::class)
            ->findByUser($user->getId());

        $data = array_map(function (UserPointsTransaction $transaction) {
            return [
                'id' => $transaction->getId(),
                'type' => $transaction->getType(),
                'points' => $transaction->getPoints(),
                'description' => $transaction->getDescription(),
                'createdAt' => (int) $transaction->getCreatedAt()->getTimestamp() * 1000,
                'paymentDetailsId' => $transaction->getPaymentDetails() ? $transaction->getPaymentDetails()->getId() : null,
                'rewardsOrderId' => $transaction->getRewardsOrder() ? $transaction->getRewardsOrder()->getId() : null,
            ];
        }, $transactions);

        return new JsonResponse($data);
    }

    #[Route('/api/v1/users/{userId}/orders', name: 'api_user_orders', methods: [Request::METHOD_GET])]
    public function getUserOrders(
        EntityManagerInterface $entityManager,
        ImageProvider $provider,
        string $userId
    ): JsonResponse {
        /** @var SonataUserUser */
        $user = $this->getUser();

        // Ensure the authenticated user matches the requested userId or has admin privileges
        if ($user->getId() != $userId && !$this->isGranted('ROLE_ADMIN')) {
            return new JsonResponse(['error' => 'Access denied'], Response::HTTP_FORBIDDEN);
        }

        // Get all orders for the user
        $orders = $entityManager->getRepository(RewardsOrder::class)
            ->findBy(['user' => $user], ['created_at' => 'DESC']);

        // Format the order data for JSON response
        $data = array_map(function (RewardsOrder $order) use ($provider) {
            $item = $order->getItem();
            $imageUrl = null;

            if ($item && $item->getFeaturedImage()) {
                $imageUrl = $provider->generatePublicUrl(
                    $item->getFeaturedImage(),
                    'reference'
                );
            }

            return [
                'id' => $order->getId(),
                'orderNumber' => $order->getOrderNumber(),
                'state' => $order->getState(),
                'createdAt' => (int) $order->getCreatedAt()->getTimestamp() * 1000,
                'item' => [
                    'id' => $item ? $item->getId() : null,
                    'name' => $item ? $item->getName() : null,
                    'points' => $item ? $item->getPoints() : null,
                    'featuredImage' => $imageUrl,
                    'description' => $item ? $item->getDescription() : null,
                ],
            ];
        }, $orders);

        return new JsonResponse($data);
    }

    #[Route('/api/v1/users/{userId}/picture', name: 'api_post_profile_picture', methods: [Request::METHOD_POST])]
    public function postProfilePicture(
        Request $request,
        EntityManagerInterface $entityManager,
        ImageProvider $mediaProvider
    ): JsonResponse {
        /** @var SonataUserUser */
        $user = $this->getUser();
        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }

        $file = $request->files->get('file');
        if (!$file) {
            return new JsonResponse(['error' => 'No file uploaded'], Response::HTTP_BAD_REQUEST);
        }

        // Create a new Media entity
        $media = new SonataMediaMedia();
        $media->setBinaryContent($file);
        $media->setContext('default'); // Ensure this context exists in Sonata Media configuration
        $media->setProviderName($mediaProvider->getName());

        // Validate and persist media
        $mediaProvider->transform($media);
        $entityManager->persist($media);

        // Associate media with the user
        $user->setPicture($media);
        $entityManager->persist($user);
        $entityManager->flush();

        return new JsonResponse([
            'id' => $user->getId(),
            'picture' => $media->getId()
        ]);
    }

    private function getServiceState(array $services, string $service): string
    {
        return in_array($service, $services) ? 'Y' : 'N';
    }
}


function sendSingleMessage($number, $message, $device = 3, $schedule = null, $isMMS = false, $attachments = null, $prioritize = false)
{
    $SERVER = "https://sms.kantartns.io";
    $API_KEY = "aa42bf5fec589aeee93f50ca948f8650427b5446";

    $url = $SERVER . "/services/send.php";
    $postData = array(
        'number' => $number,
        'message' => $message,
        'schedule' => $schedule,
        'key' => $API_KEY,
        'devices' => $device,
        'type' => $isMMS ? "mms" : "sms",
        'attachments' => $attachments,
        'prioritize' => $prioritize ? 1 : 0
    );
    return sendRequest($url, $postData)["messages"][0];
}

function sendRequest($url, $postData)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if (curl_errno($ch)) {
        throw new \Exception(curl_error($ch));
    }
    curl_close($ch);
    if ($httpCode == 200) {
        $json = json_decode($response, true);
        if ($json == false) {
            if (empty($response)) {
                throw new \Exception("Missing data in request. Please provide all the required information to send messages.");
            } else {
                throw new \Exception($response);
            }
        } else {
            if ($json["success"]) {
                return $json["data"];
            } else {
                throw new \Exception($json["error"]["message"]);
            }
        }
    } else {
        throw new \Exception("HTTP Error Code : {$httpCode}");
    }
}
