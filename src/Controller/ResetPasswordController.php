<?php

namespace App\Controller;

use Sonata\UserBundle\Form\Type\ResettingFormType;
use Sonata\UserBundle\Model\UserManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Twig\Environment;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

#[Route('/reset-password/{token}', name: 'app_reset_password', methods: [Request::METHOD_GET, Request::METHOD_POST])]
class ResetPasswordController
{
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
        private AuthorizationCheckerInterface $authorizationChecker,
        private UserManagerInterface $userManager,
        private FormFactoryInterface $formFactory,
        private int $tokenTtl,
    ) {}

    public function __invoke(Request $request, string $token): Response
    {
        if ($this->authorizationChecker->isGranted('IS_AUTHENTICATED_FULLY')) {
            return new RedirectResponse($this->urlGenerator->generate('app_reset_password_success'));
        }

        $user = $this->userManager->findUserByConfirmationToken($token);

        if (null === $user) {
            throw new NotFoundHttpException(\sprintf('The user with "confirmation token" does not exist for value "%s"', $token));
        }

        if (!$user->isPasswordRequestNonExpired($this->tokenTtl)) {
            return new RedirectResponse($this->urlGenerator->generate('sonata_user_admin_resetting_request'));
        }

        $form = $this->formFactory->create(ResettingFormType::class);
        $form->setData($user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $user->setConfirmationToken(null);
            $user->setPasswordRequestedAt(null);
            $user->setEnabled(true);

            /** @var Session */
            $session = $request->getSession();
            $session->getFlashBag()->add(
                'success',
                'Your password has been reset successfully.'
            );

            $response = new RedirectResponse($this->urlGenerator->generate('app_reset_password_success'));

            $this->userManager->save($user);

            return $response;
        }

        return new Response($this->twig->render('reset_password.html.twig', [
            'token' => $token,
            'form' => $form->createView(),
        ]));
    }
}
