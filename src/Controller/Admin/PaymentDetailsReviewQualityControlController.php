<?php

namespace App\Controller\Admin;

use App\Entity\OCRMethod;
use App\Entity\PaymentDetailsReview;
use App\Entity\PaymentDetails;
use App\Repository\PaymentDetailsRepository;
use App\Repository\PaymentDetailsReviewRepository;
use App\Service\OCRComparisonService;
use App\Service\GeolocationService;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Controller for handling Quality Control operations for PaymentDetailsReview.
 */
class PaymentDetailsReviewQualityControlController extends CRUDController
{
    private EntityManagerInterface $entityManager;
    private PaymentDetailsRepository $paymentDetailsRepository;
    private PaymentDetailsReviewRepository $paymentDetailsReviewRepository;
    private OCRComparisonService $ocrComparisonService;
    private GeolocationService $geolocationService;

    public function __construct(
        EntityManagerInterface $entityManager,
        PaymentDetailsRepository $paymentDetailsRepository,
        PaymentDetailsReviewRepository $paymentDetailsReviewRepository,
        OCRComparisonService $ocrComparisonService,
        GeolocationService $geolocationService
    ) {
        $this->entityManager = $entityManager;
        $this->paymentDetailsRepository = $paymentDetailsRepository;
        $this->paymentDetailsReviewRepository = $paymentDetailsReviewRepository;
        $this->ocrComparisonService = $ocrComparisonService;
        $this->geolocationService = $geolocationService;
    }

    /**
     * Handles quality control actions for payment details at the collection level.
     *
     * @param Request $request The current request
     *
     * @return Response
     */
    public function qualityControlAction(Request $request): Response
    {
        // Ensure the user has access to the quality control page
        $this->admin->checkAccess('quality_control');

        // Get admin configuration and prepare any data needed for the template
        $admin = $this->admin;

        // Calculate metrics from real data
        // 1. Get total processed payment details (where qcState is not null)
        $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
            ->select('COUNT(pd.id)')
            ->where('pd.qcState IS NOT NULL');
        $totalProcessed = (int)$qb->getQuery()->getSingleScalarResult();

        // 2. Get metrics from OCR comparison service
        $currentSystemMetrics = $this->ocrComparisonService->calculateAccuracyMetrics(OCRMethod::CURRENT_SYSTEM);
        $improvedSystemMetrics = $this->ocrComparisonService->calculateAccuracyMetrics(OCRMethod::IMPROVED_SYSTEM);

        // If no comparison results yet, use legacy calculation method for backward compatibility
        if ($currentSystemMetrics['totalProcessed'] === 0) {
            // Legacy calculation method - get all payment details with reviews
            $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
                ->leftJoin('pd.review', 'r')
                ->where('pd.qcState IS NOT NULL')
                ->andWhere('r.id IS NOT NULL');
            $paymentDetailsWithReviews = $qb->getQuery()->getResult();

            // Count the number of payment details with "passed" QC state
            $matchingCount = 0;
            $passedCount = 0;
            $amountValues = [
                'calculated' => [], // PaymentDetails values
                'extracted' => []   // PaymentDetailsReview values
            ];

            $tolerance = 0.01; // Define a tolerance for comparing decimal values

            foreach ($paymentDetailsWithReviews as $paymentDetails) {
                $review = $paymentDetails->getReview();

                // Add amount values for correlation calculation
                $amountValues['calculated'][] = (float)$paymentDetails->getAmount();
                $amountValues['extracted'][] = (float)$review->getAmount();

                // Count payments with "passed" QC state directly
                if ($paymentDetails->getQcState() === PaymentDetails::QC_STATE_PASSED) {
                    $passedCount++;
                }

                // Check if all fields match within tolerance (keeping this for backwards compatibility)
                if (
                    abs((float)$paymentDetails->getAmount() - (float)$review->getAmount()) < $tolerance &&
                    abs((float)$paymentDetails->getLitres() - (float)$review->getLitres()) < $tolerance &&
                    abs((float)$paymentDetails->getUnitPrice() - (float)$review->getUnitPrice()) < $tolerance
                ) {
                    $matchingCount++;
                }
            }

            // Calculate overall accuracy as percentage based on "passed" QC state
            $overallAccuracy = count($paymentDetailsWithReviews) > 0
                ? ($passedCount / count($paymentDetailsWithReviews)) * 100
                : 0;

            // Calculate correlation for amount values
            // For simplicity, we'll use a basic correlation metric - what percentage of amount values match within tolerance
            $matchingAmounts = 0;
            for ($i = 0; $i < count($amountValues['calculated']); $i++) {
                if (abs($amountValues['calculated'][$i] - $amountValues['extracted'][$i]) < $tolerance) {
                    $matchingAmounts++;
                }
            }
            $correlationValidation = count($amountValues['calculated']) > 0
                ? ($matchingAmounts / count($amountValues['calculated'])) * 100
                : 0;

            // Use legacy calculated values
            $currentSystemMetrics = [
                'overallAccuracy' => round($overallAccuracy, 1),
                'correlationValidation' => round($correlationValidation, 1),
                'totalProcessed' => $totalProcessed,
                'accuracyRawCounts' => [
                    'passedCount' => $passedCount,
                    'totalCount' => count($paymentDetailsWithReviews)
                ],
                'correlationRawCounts' => [
                    'passedCount' => $matchingAmounts,
                    'totalCount' => count($amountValues['calculated'])
                ]
            ];

            // Calculate improved system metrics from actual data instead of using sample values
            // Get payment details with QC state "passed" for improved system
            $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
                ->leftJoin('pd.review', 'r')
                ->leftJoin('pd.ocrResults', 'ocr')
                ->leftJoin('ocr.ocrMethod', 'm')
                ->where('pd.qcState = :qcState')
                ->andWhere('m.id = :methodId')
                ->setParameter('qcState', PaymentDetails::QC_STATE_PASSED)
                ->setParameter('methodId', OCRMethod::IMPROVED_SYSTEM);

            $passedImprovedCount = count($qb->getQuery()->getResult());

            // Get total count of payment details processed with improved system
            $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
                ->leftJoin('pd.ocrResults', 'ocr')
                ->leftJoin('ocr.ocrMethod', 'm')
                ->where('m.id = :methodId')
                ->setParameter('methodId', OCRMethod::IMPROVED_SYSTEM);

            $totalImprovedCount = count($qb->getQuery()->getResult());

            // Calculate improved system accuracy
            $improvedOverallAccuracy = $totalImprovedCount > 0
                ? ($passedImprovedCount / $totalImprovedCount) * 100
                : 0;

            $improvedSystemMetrics = [
                'overallAccuracy' => round($improvedOverallAccuracy, 1),
                'correlationValidation' => round($correlationValidation, 1), // Using same correlation for now
                'totalProcessed' => $totalImprovedCount,
                'accuracyRawCounts' => [
                    'passedCount' => $passedImprovedCount,
                    'totalCount' => $totalImprovedCount
                ],
                'correlationRawCounts' => [
                    'passedCount' => $matchingAmounts,
                    'totalCount' => count($amountValues['calculated'])
                ]
            ];

            // Default correlation data
            $correlationData = [
                'currentSystem' => [
                    'calculated' => $amountValues['calculated'],
                    'extracted' => $amountValues['extracted']
                ],
                'improvedSystem' => [
                    'calculated' => [12.50, 25.00, 37.50, 50.00, 62.50, 75.00, 87.50, 100.00],
                    'extracted' => [12.55, 24.95, 37.55, 49.90, 62.60, 74.90, 87.60, 99.90]
                ]
            ];
        } else {
            // Use real data from comparison service
            $correlationData = [
                'currentSystem' => $this->ocrComparisonService->getCorrelationData('current'),
                'improvedSystem' => $this->ocrComparisonService->getCorrelationData('improved')
            ];
        }

        // Calculate geolocation accuracy metrics
        $geolocationMetrics = $this->calculateGeolocationMetrics();

        // Prepare quality control metrics data
        $summaryMetrics = [
            'manualQC' => [
                'overallAccuracy' => 100, // Always 100 as per requirement
                'correlationValidation' => 100,
                'totalProcessed' => $totalProcessed,
                'accuracyRawCounts' => [
                    'passedCount' => $totalProcessed,
                    'totalCount' => $totalProcessed
                ],
                'correlationRawCounts' => [
                    'passedCount' => $totalProcessed,
                    'totalCount' => $totalProcessed
                ],
                'geolocationAccuracy' => 100 // Always 100 for manual QC
            ],
            'currentSystem' => array_merge($currentSystemMetrics, [
                'geolocationAccuracy' => $geolocationMetrics['accuracy']
            ]),
            'improvedSystem' => array_merge($improvedSystemMetrics, [
                'geolocationAccuracy' => $geolocationMetrics['accuracy']
            ])
        ];

        // Get field accuracy data
        $fieldAccuracy = [
            'fields' => ['Volume', 'Unit Price', 'Amount'],
            'manualQC' => [100, 100, 100], // Baseline ground truth
            'currentSystem' => $this->ocrComparisonService->getFieldAccuracy('current'),
            'improvedSystem' => $this->ocrComparisonService->getFieldAccuracy('improved')
        ];

        // Get error types data
        $errorTypes = [
            'labels' => ['Missing Digit', 'Format Error', 'Wrong Character', 'Missing Field', 'Other'],
            'currentSystem' => $this->ocrComparisonService->getErrorTypeCounts('current'),
            'improvedSystem' => $this->ocrComparisonService->getErrorTypeCounts('improved')
        ];

        return $this->renderWithExtraParams('admin/payment_details_review/quality_control.html.twig', [
            'action' => 'quality_control',
            'admin' => $admin,
            'summaryMetrics' => $summaryMetrics,
            'fieldAccuracy' => $fieldAccuracy,
            'errorTypes' => $errorTypes,
            'correlationData' => $correlationData,
            'geolocationMetrics' => $geolocationMetrics
            // No specific object is needed since this is a collection-level action
        ]);
    }

    /**
     * Calculate geolocation accuracy metrics
     *
     * @return array Geolocation metrics
     */
    private function calculateGeolocationMetrics(): array
    {
        // Get all payment details with both coordinates and filling station
        $qb = $this->paymentDetailsRepository->createQueryBuilder('pd')
            ->where('pd.fillingStation IS NOT NULL')
            ->andWhere('pd.latitude != 0')
            ->andWhere('pd.longitude != 0')
            ->orderBy('pd.created_at', 'ASC');

        $paymentDetails = $qb->getQuery()->getResult();

        if (empty($paymentDetails)) {
            return [
                'accuracy' => 0,
                'totalCount' => 0,
                'correctCount' => 0,
                'averageDistance' => 0,
                'distanceDistribution' => [
                    'labels' => ['0-100m', '100-500m', '500m-1km', '1-5km', '>5km'],
                    'counts' => [0, 0, 0, 0, 0]
                ],
                'accuracyByTime' => [
                    'labels' => [],
                    'accuracy' => [],
                    'counts' => []
                ],
                'accuracyByStation' => [
                    'labels' => [],
                    'accuracy' => [],
                    'counts' => []
                ]
            ];
        }

        $correctCount = 0;
        $totalDistance = 0;
        $distanceCounts = [0, 0, 0, 0, 0]; // [0-100m, 100-500m, 500m-1km, 1-5km, >5km]

        foreach ($paymentDetails as $payment) {
            // Check if payment was made at the closest station
            if ($this->geolocationService->isPaymentAtClosestStation($payment)) {
                $correctCount++;
            }

            // Calculate distance to the associated filling station
            $distance = $this->geolocationService->calculatePaymentToStationDistance($payment);

            if ($distance !== null) {
                $totalDistance += $distance;

                // Categorize distance
                if ($distance <= 0.1) { // 0-100m
                    $distanceCounts[0]++;
                } elseif ($distance <= 0.5) { // 100-500m
                    $distanceCounts[1]++;
                } elseif ($distance <= 1) { // 500m-1km
                    $distanceCounts[2]++;
                } elseif ($distance <= 5) { // 1-5km
                    $distanceCounts[3]++;
                } else { // >5km
                    $distanceCounts[4]++;
                }
            }
        }

        $totalCount = count($paymentDetails);
        $accuracy = ($correctCount / $totalCount) * 100;
        $averageDistance = $totalDistance / $totalCount;

        // Calculate accuracy by time period (monthly)
        $accuracyByTime = $this->geolocationService->calculateAccuracyByTimePeriod($paymentDetails, 'month');

        // Calculate accuracy by filling station
        $accuracyByStation = $this->geolocationService->calculateAccuracyByFillingStation($paymentDetails);

        return [
            'accuracy' => round($accuracy, 1),
            'totalCount' => $totalCount,
            'correctCount' => $correctCount,
            'averageDistance' => round($averageDistance, 2),
            'distanceDistribution' => [
                'labels' => ['0-100m', '100-500m', '500m-1km', '1-5km', '>5km'],
                'counts' => $distanceCounts
            ],
            'accuracyByTime' => $accuracyByTime,
            'accuracyByStation' => $accuracyByStation
        ];
    }
}
