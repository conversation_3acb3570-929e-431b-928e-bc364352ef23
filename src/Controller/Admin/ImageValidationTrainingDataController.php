<?php

namespace App\Controller\Admin;

use App\Entity\ImageValidationTrainingData;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for ImageValidationTrainingData Admin
 */
class ImageValidationTrainingDataController extends CRUDController
{
    /**
     * Batch action for toggling includeInExport flag
     *
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @return Response
     */
    public function batchActionToggleIncludeInExport(Request $request, EntityManagerInterface $entityManager): Response
    {
        $this->admin->checkAccess('edit');
        
        $selectedIds = $request->get('idx');
        if (!$selectedIds) {
            $this->addFlash('sonata_flash_info', 'No items selected.');
            return $this->redirectToList();
        }
        
        $repository = $entityManager->getRepository(ImageValidationTrainingData::class);
        $selectedItems = $repository->findBy(['id' => $selectedIds]);
        
        foreach ($selectedItems as $item) {
            // Toggle the includeInExport flag
            $item->setIncludeInExport(!$item->isIncludeInExport());
            $entityManager->persist($item);
        }
        
        $entityManager->flush();
        
        $this->addFlash('sonata_flash_success', count($selectedItems) . ' items have been updated.');
        
        return $this->redirectToList();
    }
}
