<?php

namespace App\Controller\Admin;

use App\Entity\PaymentDetails;
use App\Entity\PaymentDetailsReview;
use App\Entity\SonataUserUser;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

/**
 * Controller for handling review actions for manually entered payment details.
 */
class PaymentDetailsReviewController extends CRUDController
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Review action for manually entered payment details.
     *
     * @param Request $request The current request
     * @param int $id The payment details ID
     *
     * @return Response
     */
    public function reviewAction(Request $request, int $id): Response
    {
        // Get the payment details object
        $paymentDetails = $this->admin->getObject($id);

        // Check if the payment details exist and are manually entered
        if (!$paymentDetails) {
            throw $this->createNotFoundException('Payment details not found');
        }

        if (!$paymentDetails->isManualEntry()) {
            $this->addFlash('error', 'Only manually entered payment details can be reviewed');
            return $this->redirectToList();
        }

        // Check if a review already exists for this payment
        $review = $paymentDetails->getReview();
        if (!$review) {
            // Create a new review if one doesn't exist
            $review = new PaymentDetailsReview();
            $review->setPaymentDetails($paymentDetails);
            $review->setAmount($paymentDetails->getAmount());
            $review->setLitres($paymentDetails->getLitres());
            $review->setUnitPrice($paymentDetails->getUnitPrice());
            $review->setReviewStatus(PaymentDetailsReview::REVIEW_STATUS_PENDING);
        } elseif ($review->getReviewStatus() !== PaymentDetailsReview::REVIEW_STATUS_PENDING) {
            // If the review is already completed, show a message and redirect
            $this->addFlash('warning', 'This payment has already been reviewed');
            return $this->redirectToRoute('admin_app_paymentdetails_show', ['id' => $id]);
        }

        // Calculate points based on the amount (Rs100 = 1 point)
        $pointsCalculation = (float)$review->getAmount() / 100;
        $review->setPointsCalculation((string)$pointsCalculation);

        // Get customer's current points
        $customer = $paymentDetails->getCustomerUser();
        $currentPoints = $customer ? $customer->getPoints() : 0;
        $projectedPoints = $currentPoints + (int)$pointsCalculation;

        // Create the review form
        $form = $this->createFormBuilder($review)
            ->add('amount', NumberType::class, [
                'required' => true,
                'scale' => 2,
                'attr' => [
                    'class' => 'amount-field',
                    'data-points-ratio' => 100, // Rs100 = 1 point
                ],
            ])
            ->add('litres', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('unitPrice', NumberType::class, [
                'required' => true,
                'scale' => 2,
            ])
            ->add('reviewNotes', TextareaType::class, [
                'required' => false,
                'label' => 'Review Notes',
            ])
            ->add('approve', SubmitType::class, [
                'label' => 'Approve',
                'attr' => ['class' => 'btn btn-success'],
            ])
            ->add('saveChanges', SubmitType::class, [
                'label' => 'Save Changes',
                'attr' => ['class' => 'btn btn-primary'],
            ])
            ->add('reject', SubmitType::class, [
                'label' => 'Reject',
                'attr' => ['class' => 'btn btn-danger'],
            ])
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Get the current user as the reviewer
            $reviewer = $this->getUser();

            // Check which button was clicked by examining the request
            $requestData = $request->request->all();
            $formData = reset($requestData);

            if (isset($formData['approve'])) {
                // Approve without modifications
                $review->approve($reviewer);
                $this->addFlash('success', 'Payment details approved successfully');
            } elseif (isset($formData['saveChanges'])) {
                // Approve with modifications
                $review->approve($reviewer);
                $this->addFlash('success', 'Payment details modified and approved successfully');
            } elseif (isset($formData['reject'])) {
                // Reject the payment details
                $review->reject($reviewer, $review->getReviewNotes());
                $this->addFlash('warning', 'Payment details rejected');
            }

            // Update the payment details QC state based on the review
            $paymentDetails->setQcStateFromReview($review);

            // Save changes to the database
            $this->entityManager->persist($review);
            $this->entityManager->persist($paymentDetails);
            $this->entityManager->flush();

            // Redirect to the payment details show page
            return $this->redirectToRoute('admin_app_paymentdetails_show', ['id' => $id]);
        }

        // Render the review form
        return $this->render('admin/payment_details/review.html.twig', [
            'action' => 'review',
            'admin' => $this->admin,
            'form' => $form->createView(),
            'object' => $paymentDetails,
            'review' => $review,
            'currentPoints' => $currentPoints,
            'projectedPoints' => $projectedPoints,
            'pointsCalculation' => $pointsCalculation,
        ]);
    }
}
