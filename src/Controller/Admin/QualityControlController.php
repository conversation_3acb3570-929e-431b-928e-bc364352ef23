<?php

namespace App\Controller\Admin;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sonata\AdminBundle\Admin\Pool;

/**
 * Controller for Quality Control Dashboard
 */
class QualityControlController extends AbstractController
{
    private $adminPool;

    public function __construct(Pool $adminPool)
    {
        $this->adminPool = $adminPool;
    }

    /**
     * Quality Control Dashboard
     *
     * @Route("/admin/quality-control", name="admin_quality_control_dashboard")
     */
    public function dashboardAction(): Response
    {
        // You could fetch real data here instead of using the dummy data in the template
        // For example:
        // $manualQcData = $this->getDoctrine()->getRepository('App:OcrResult')->findBySource('manual_qc');
        // $currentSystemData = $this->getDoctrine()->getRepository('App:OcrResult')->findBySource('current_system');

        return $this->render('admin/payment_details_review/quality_control.html.twig', [
            'admin_pool' => $this->adminPool,
            'action' => 'dashboard'
        ]);
    }
}
