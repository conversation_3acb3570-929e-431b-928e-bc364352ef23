<?php

namespace App\Controller;

use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CustomerOrderController extends AbstractController
{
    #[Route('/api/v1/customers/{customerId}/pending-orders', name: 'api_customer_pending_orders', methods: ['GET'])]
    public function getPendingOrders(int $customerId, EntityManagerInterface $entityManager): JsonResponse
    {
        // Check if customer exists
        $customer = $entityManager->getRepository(SonataUserUser::class)->find($customerId);
        if (!$customer) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }

        // Get pending orders for the customer
        $pendingOrders = $entityManager->getRepository(RewardsOrder::class)
            ->findPendingOrdersByCustomer($customerId);

        // Format the response
        $formattedOrders = array_map(function (RewardsOrder $order) {
            return [
                'orderNumber' => $order->getOrderNumber(),
                'itemName' => $order->getItem()->getName(),
                'pointCost' => $order->getItem()->getPoints()
            ];
        }, $pendingOrders);

        return $this->json($formattedOrders);
    }
}
