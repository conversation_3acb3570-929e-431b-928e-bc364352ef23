{"name": "idsdevanalysis/indian_oil_admin", "version": "0.8.2", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3.0", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.3", "gesdinet/jwt-refresh-token-bundle": "^1.3", "guzzlehttp/guzzle": "^7.9", "league/oauth2-google": "^4.0", "lexik/jwt-authentication-bundle": "^3.1", "openai-php/client": "^0.10.3", "sonata-project/admin-bundle": "^4.31", "sonata-project/doctrine-orm-admin-bundle": "^4.17", "sonata-project/media-bundle": "^4.15", "sonata-project/user-bundle": "^5.14", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/google-mailer": "6.4.*", "symfony/mailer": "6.4.*", "symfony/runtime": "6.4.*", "symfony/yaml": "6.4.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.6", "liip/test-fixtures-bundle": "^3.1", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/maker-bundle": "^1.61", "symfony/phpunit-bridge": "^7.1", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}}