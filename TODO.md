# TODO

## API Error Handling Enhancement for Fuel Meter Scan

### Background
- Current endpoint: `/api/v1/fuel-meter/scan`
- Need: Return zero values instead of error responses for failed scans
- Only using Gemini API, no fallback needed

### Implementation Plan

#### Phase 1: Update FuelMeterScanner Service
- [x] Clean up `FuelMeterScanner` class:
  - Keep `scanWithGemini` method throwing exceptions for all error cases
  - Remove any default/zero value returns from service
  - Ensure proper exception messages for different failure cases:
    - Invalid image format/size
    - API errors
    - Processing errors

#### Phase 2: Controller Updates
- [x] Update `DefaultController`:
  - Implement standardized zero-value response structure:
    ```php
    [
        'amount' => 0,
        'litres' => 0,
        'unit_price' => 0,
        'fuel_type' => 'unknown',
        'success' => false,
        'error_code' => 'SCAN_FAILED',
        'error_details' => [
            'message' => $e->getMessage(),
            'type' => 'processing'
        ]
    ]
    ```
  - Return HTTP 200 with zero values for all caught exceptions
  - Log all errors appropriately
  - Keep HTTP 5xx only for critical system failures

#### Phase 3: Testing
- [x] Add test cases:
  - Invalid image formats
  - Corrupt images
  - API timeout scenarios
  - Network failure scenarios
  - Invalid scan results

### Error Codes Reference
- `INVALID_IMAGE`: Image format or size invalid
- `CORRUPT_IMAGE`: Image data corrupted
- `SCAN_FAILED`: OCR processing failed
- `API_ERROR`: External API error
- `SYSTEM_ERROR`: Internal system error

### Acceptance Criteria
- Service throws exceptions for all error cases
- Controller catches all exceptions and returns zero values
- Error responses maintain HTTP 200 status
- All errors are properly logged
- Test coverage for all error scenarios

## Iteration 12: Complete Implementation
- [ ] Add validation and error handling:
  - Rate limiting (max 3 attempts)
  - OTP expiration check (10min)
  - Invalid OTP handling
  - Order status validation
- [ ] Add points transaction handling
- [ ] Update tests to cover all scenarios

### Error Scenarios to Test
- [ ] Invalid OTP format
- [ ] Expired OTP
- [ ] Maximum attempts exceeded
- [ ] Order not found
- [ ] Order already processed

## Iteration 13: Final Touches
- [ ] add count down for otp expiration
- [ ] Verify customer phone number
- [ ] Final code review and cleanup

## Other Update that need planning

- [ ] Cancel Order
- [ ] Short list item
- [ ] Add person name in QRCode & Display on scan
- [ ] Scan QRCode to identify user in redeem process
- [ ] Verify phone number with OTP
- [ ] Sign up with google
- [ ] Sign up with phone number
- [ ] Form to manually increase user points
- [ ] Publish app to apple store
- [ ] User Google New Vertex AI
- [ ] Take photo of ID Card
- [ ] Correct / reject a payment detail — update user points and transaction history

## AI Training Data Management Implementation

### Preparation
- [x] Develop import script for production data
  - Create a bash script `bin/import-production-data.sh` with the following features:
    - SSH tunnel creation and management
    - Database export from production
    - Local database reset and import
    - Uploads directory sync
    - Verification steps
    - Error handling and logging
    - Progress reporting
    - Command-line options (--skip-db, --skip-uploads, --dry-run)
  - Add documentation for the script in README.md
  - Test the script in development environment
  - Add safeguards to prevent accidental production data modification

### Phase 1: Database and Entity Setup
- [x] Create ImageValidationTrainingData entity with fields:
  - id (int)
  - paymentDetailsId (relation to PaymentDetails)
  - imagePath (string)
  - isValid (boolean)
  - mathematicalValidation (boolean)
  - validationNote (text, nullable)
  - createdAt, updatedAt (DateTimeImmutable)
- [x] Add includeInExport (boolean) field to entity
- [x] Run doctrine schema update
- [x] Add entity tests
- [x] Create repository class with basic queries

### Phase 2: Admin Interface
- [x] Create Sonata Admin class for ImageValidationTrainingData
  - List view with filters and batch actions
  - Edit form with image preview
  - Batch toggle for includeInExport
- [x] Add custom actions for validation status
- [x] Write admin controller tests

### Phase 3: Data Import Command
- [x] Create command: app:image-validation:import-from-payments
  - Fetch all PaymentDetails
  - Calculate mathematicalValidation
  - Create ImageValidationTrainingData entries
- [x] Add progress bar for large datasets
- [x] Add --dry-run option
- [x] Write command tests with sample data
- [x] Add validation report after import

### Phase 4: Export Implementation
- [ ] Create command: app:image-validation:export-dataset
  - Create timestamped export directory
  - Export only items where includeInExport is true
  - Generate metadata file with validation status
  - Create TFLite compatible format
- [ ] Add export format tests
- [ ] Add directory structure tests
- [ ] Test with large datasets

### Phase 5: Integration Tests
- [ ] Test complete workflow:
  - Import data
  - Admin management
  - Export process
- [ ] Test edge cases:
  - Missing images
  - Invalid mathematical calculations
  - Export with no valid items
  - Duplicate entries

### Phase 6: Documentation
- [ ] Add technical documentation
- [ ] Create user guide for admin interface
- [ ] Document export format specification
- [ ] Add example usage in README.md

### Phase 7: Mobile Integration
- [ ] Create API endpoint for validation rules
- [ ] Document integration points for Flutter app
- [ ] Add tests for API endpoints

### Acceptance Criteria
- Admin can manage and review validation items
- Import command successfully processes existing data
- Export command generates valid TFLite format
- All processes handle errors gracefully
- Documentation covers all features
- Test coverage > 90%


## Finish ImageValidationTraining Admin Setup

### Disable admin create route `/admin/app/imagevalidationtrainingdata/create`

The ImageValidationTrainingData entity should only be created through the import command, not manually through the admin interface. To disable the create route:

1. Open `src/Admin/ImageValidationTrainingDataAdmin.php`
2. Add the `configureRoutes` method to remove the create route:
   ```php
   protected function configureRoutes(RouteCollectionInterface $collection): void
   {
       $collection->remove('create');
   }
   ```
3. Make sure to import the RouteCollectionInterface at the top of the file:
   ```php
   use Sonata\AdminBundle\Route\RouteCollectionInterface;
   ```
4. Update the test file `tests/Admin/ImageValidationTrainingDataAdminTest.php`:
   - Remove or modify the `testCreateImageValidationTrainingData` test since this functionality will no longer be available
   - Add a new test to verify that the create route is properly disabled:
   ```php
   public function testCreateRouteIsDisabled(): void
   {
       $client = $this->getClient();
       $client->request('GET', '/admin/app/imagevalidationtrainingdata/create');
       $this->assertResponseStatusCodeSame(404);
   }
   ```
5. Verify the change by:
   - Checking that the "Create" button no longer appears in the list view
   - Confirming that accessing `/admin/app/imagevalidationtrainingdata/create` directly returns a 404 error
   - Running the updated admin controller tests to ensure they pass

This change ensures that all training data is created consistently through the import process, maintaining data integrity and validation rules.