#!/bin/bash
#
# Production Data Import Script
# This script automates the import of production database and uploads to a local development environment.
#
# Usage: bin/import-production-data.sh [OPTIONS]
#
# Options:
#   --skip-db        Skip database import
#   --skip-uploads   <PERSON><PERSON> uploads sync
#   --dry-run        Show what would be done without actually doing it
#   --help           Show this help message
#   --force          <PERSON>p confirmation prompts
#

# ⚠️  WARNING ⚠️
# There is a known issue where the dump file is downloaded but cannot be imported.
# Please contact the development team if you encounter this problem.
echo -e "\033[41m\033[1;37m ⚠️  WARNING: There is a known issue where the dump file is downloaded but cannot be imported. ⚠️ \033[0m"
echo ""

# Exit on error
set -e

# Script constants
SCRIPT_NAME=$(basename "$0")
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")
LOG_DIR="$PROJECT_ROOT/var/log"
LOG_FILE="$LOG_DIR/import-$(date +%Y%m%d-%H%M%S).log"

# Production server details
PROD_SERVER="rewards.ioml.mu"
PROD_USER="debian"
PROD_DB_NAME="indianoil_prod"
LOCAL_DB_NAME="indianoil_dev"
PROD_UPLOADS_PATH="/srv/indian_oil_admin/uploads/"
LOCAL_UPLOADS_PATH="$PROJECT_ROOT/public/uploads/"

# Default options
SKIP_DB=false
SKIP_UPLOADS=false
DRY_RUN=false
FORCE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display help message
function show_help() {
    echo "Usage: $SCRIPT_NAME [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --skip-db        Skip database import"
    echo "  --skip-uploads   Skip uploads sync"
    echo "  --dry-run        Show what would be done without actually doing it"
    echo "  --help           Show this help message"
    echo "  --force          Skip confirmation prompts"
    echo ""
    exit 0
}

# Function for logging
function log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")

    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"

    # Log to file
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"

    # Log to console with color
    case "$level" in
        "INFO")
            echo -e "${BLUE}[$level]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[$level]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[$level]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[$level]${NC} $message"
            ;;
        *)
            echo "[$level] $message"
            ;;
    esac
}

# Function to parse command line arguments
function parse_arguments() {
    for arg in "$@"; do
        case $arg in
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --skip-uploads)
                SKIP_UPLOADS=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --help)
                show_help
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

# Function to check prerequisites
function check_prerequisites() {
    log "INFO" "Checking prerequisites..."

    # Check if running in Docker environment
    if [ ! -f /.dockerenv ] && [ ! "$DRY_RUN" = true ]; then
        log "WARNING" "This script is designed to run inside the Docker container."
        log "WARNING" "If you're not running in Docker, database connection parameters may need adjustment."

        if [ "$FORCE" = false ]; then
            read -p "Continue anyway? (y/n) " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log "INFO" "Operation cancelled by user."
                exit 0
            fi
        fi
    fi

    # Check for required commands
    local required_commands=("ssh" "mysql" "rsync" "scp")
    for cmd in "${required_commands[@]}"; do
        if ! command -v $cmd &> /dev/null && [ ! "$DRY_RUN" = true ]; then
            log "ERROR" "Required command not found: $cmd"
            exit 1
        fi
    done

    # Check for sufficient disk space
    if [ ! "$DRY_RUN" = true ]; then
        local available_space=$(df -k "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
        local min_required_space=$((5 * 1024 * 1024)) # 5GB in KB

        if [ "$available_space" -lt "$min_required_space" ]; then
            log "WARNING" "Low disk space: $(($available_space / 1024 / 1024))GB available, 5GB recommended"

            if [ "$FORCE" = false ]; then
                read -p "Continue anyway? (y/n) " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log "INFO" "Operation cancelled by user."
                    exit 0
                fi
            fi
        fi
    fi

    log "SUCCESS" "Prerequisites check completed."
}

# Function to check SSH connection
function check_ssh_connection() {
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would check SSH connection to $PROD_SERVER"
        return 0
    fi

    log "INFO" "Checking SSH connection to $PROD_SERVER..."

    # Test SSH connection
    if ssh -q "$PROD_USER@$PROD_SERVER" exit; then
        log "SUCCESS" "SSH connection to $PROD_SERVER is working."
        return 0
    else
        log "ERROR" "Failed to connect to $PROD_SERVER via SSH."
        log "ERROR" "Please check your SSH configuration and ensure you have access to the server."
        exit 1
    fi
}

# Function to export database
function export_database() {
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would export database $PROD_DB_NAME from production"
        return 0
    fi

    log "INFO" "Exporting database $PROD_DB_NAME from production..."

    # Create temporary directory if it doesn't exist
    local tmp_dir="/tmp"
    mkdir -p "$tmp_dir"

    # Export database using Docker on the remote server
    local dump_file="$tmp_dir/${PROD_DB_NAME}_backup.sql"
    local remote_tmp_file="/tmp/${PROD_DB_NAME}_backup.sql"

    log "INFO" "Running database export on production server via Docker..."

    # Execute mysqldump inside the db container on the production server
    if ssh "$PROD_USER@$PROD_SERVER" "sudo docker-compose -f /srv/indian_oil_admin/docker-compose.prod.yml exec -T db mysqldump -u root -pmariadb $PROD_DB_NAME > $remote_tmp_file"; then
        log "SUCCESS" "Database exported on production server to $remote_tmp_file"

        # Copy the dump file from the production server to local
        log "INFO" "Copying database dump from production server..."
        if scp "$PROD_USER@$PROD_SERVER:$remote_tmp_file" "$dump_file"; then
            log "SUCCESS" "Database dump copied to $dump_file"

            echo "$dump_file"
        else
            log "ERROR" "Failed to copy database dump from production server."
            exit 1
        fi
    else
        log "ERROR" "Failed to export database on production server."
        exit 1
    fi
}

# Function to import database
function import_database() {
    local dump_file="$1"

    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would import database $PROD_DB_NAME to local $LOCAL_DB_NAME"
        return 0
    fi

    log "INFO" "Importing database to local $LOCAL_DB_NAME..."

    # Drop existing database if it exists
    log "INFO" "Dropping existing database $LOCAL_DB_NAME if it exists..."
    mysql -h db -u root -pmariadb -e "DROP DATABASE IF EXISTS $LOCAL_DB_NAME"

    # Create fresh database
    log "INFO" "Creating fresh database $LOCAL_DB_NAME..."
    mysql -h db -u root -pmariadb -e "CREATE DATABASE $LOCAL_DB_NAME"

    # Import production data
    log "INFO" "Importing data from $dump_file..."
    if mysql -h db -u root -pmariadb "$LOCAL_DB_NAME" < "$dump_file"; then
        log "SUCCESS" "Database imported successfully."
    else
        log "ERROR" "Failed to import database."
        exit 1
    fi
}

# Function to sync uploads
function sync_uploads() {
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would sync uploads from $PROD_SERVER:$PROD_UPLOADS_PATH to $LOCAL_UPLOADS_PATH"
        return 0
    fi

    log "INFO" "Syncing uploads from production..."

    # Create local uploads directory if it doesn't exist
    mkdir -p "$LOCAL_UPLOADS_PATH"

    # Use rsync to download production uploads
    if rsync -avz --progress "$PROD_USER@$PROD_SERVER:$PROD_UPLOADS_PATH" "$LOCAL_UPLOADS_PATH"; then
        log "SUCCESS" "Uploads synced successfully."
    else
        log "ERROR" "Failed to sync uploads."
        exit 1
    fi

    # Fix permissions
    chmod -R 755 "$LOCAL_UPLOADS_PATH"
}

# Function to verify import
function verify_import() {
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would verify import"
        return 0
    fi

    log "INFO" "Verifying import..."

    # Verify database import if not skipped
    if [ "$SKIP_DB" = false ]; then
        log "INFO" "Verifying database import..."

        # Check if tables exist
        local table_count=$(mysql -h db -u root -pmariadb -e "SHOW TABLES FROM $LOCAL_DB_NAME" | wc -l)

        if [ "$table_count" -gt 0 ]; then
            log "SUCCESS" "Database verification passed: $table_count tables found."
        else
            log "ERROR" "Database verification failed: No tables found."
            exit 1
        fi
    fi

    # Verify uploads sync if not skipped
    if [ "$SKIP_UPLOADS" = false ]; then
        log "INFO" "Verifying uploads sync..."

        # Check if uploads directory exists and has files
        if [ -d "$LOCAL_UPLOADS_PATH" ]; then
            local file_count=$(find "$LOCAL_UPLOADS_PATH" -type f | wc -l)

            if [ "$file_count" -gt 0 ]; then
                log "SUCCESS" "Uploads verification passed: $file_count files found."
            else
                log "WARNING" "Uploads verification warning: No files found in uploads directory."
            fi
        else
            log "ERROR" "Uploads verification failed: Uploads directory not found."
            exit 1
        fi
    fi
}

# Function to clean up temporary files
function cleanup() {
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Would clean up temporary files"
        return 0
    fi

    log "INFO" "Cleaning up temporary files..."

    # Remove database dump file if it exists
    local tmp_dir="$PROJECT_ROOT/var/tmp"
    local dump_file="$tmp_dir/${PROD_DB_NAME}_backup.sql"

    if [ -f "$dump_file" ]; then
        rm "$dump_file"
        log "INFO" "Removed temporary dump file: $dump_file"
    fi
}

# Function to show summary of actions
function show_summary() {
    log "INFO" "Import Production Data Script - Summary"
    log "INFO" "----------------------------------------"
    log "INFO" "Production Server: $PROD_SERVER"
    log "INFO" "Production Database: $PROD_DB_NAME"
    log "INFO" "Local Database: $LOCAL_DB_NAME"
    log "INFO" "Production Uploads Path: $PROD_UPLOADS_PATH"
    log "INFO" "Local Uploads Path: $LOCAL_UPLOADS_PATH"
    log "INFO" "----------------------------------------"
    log "INFO" "Skip Database Import: $SKIP_DB"
    log "INFO" "Skip Uploads Sync: $SKIP_UPLOADS"
    log "INFO" "Dry Run: $DRY_RUN"
    log "INFO" "Force Mode: $FORCE"
    log "INFO" "----------------------------------------"

    if [ "$FORCE" = false ] && [ "$DRY_RUN" = false ]; then
        read -p "Continue with these settings? (y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "Operation cancelled by user."
            exit 0
        fi
    fi
}

# Function to show completion message
function show_completion_message() {
    log "SUCCESS" "Import completed successfully!"
    log "INFO" "Log file: $LOG_FILE"

    if [ "$DRY_RUN" = true ]; then
        log "INFO" "This was a dry run. No changes were made."
    else
        log "INFO" "You can now use the imported data in your local environment."
    fi
}

# Main function
function main() {
    log "INFO" "Starting import process..."

    # Parse command line arguments
    parse_arguments "$@"

    # Check prerequisites
    check_prerequisites

    # Show summary of actions
    show_summary

    # Check SSH connection if needed for any operation
    if [ "$SKIP_DB" = false ] || [ "$SKIP_UPLOADS" = false ]; then
        check_ssh_connection
    fi

    # Database import
    if [ "$SKIP_DB" = false ]; then
        dump_file=$(export_database)
        import_database "$dump_file"
    else
        log "INFO" "Skipping database import as requested."
    fi

    # Uploads sync
    if [ "$SKIP_UPLOADS" = false ]; then
        sync_uploads
    else
        log "INFO" "Skipping uploads sync as requested."
    fi

    # Verify import
    verify_import

    # Clean up
    cleanup

    # Show completion message
    show_completion_message
}

# Run main function
main "$@"
