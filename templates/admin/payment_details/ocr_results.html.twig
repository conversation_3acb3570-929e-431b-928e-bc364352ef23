{# Display OCR results for each method #}
{% set object = object|default(admin.subject) %}
{% set ocr_results = object.ocrResults %}
{% set review = object.review %}

{# Display Ground Truth section if review exists #}
{% if review is not null %}
<div class="row">
    <div class="col-md-12">
        <div class="box box-success">
            <div class="box-header with-border">
                <h4 class="box-title">Ground Truth Data (Quality Control Review)</h4>
                <div class="box-tools pull-right">
                    <span class="label {% if review.rejected %}label-danger{% else %}label-success{% endif %}">
                        {% if review.rejected %}Rejected{% else %}Verified{% endif %}
                    </span>
                </div>
            </div>
            <div class="box-body">
                {% if review.rejected %}
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i> This payment was marked as rejected during quality control review.
                    </div>
                {% else %}
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-green"><i class="fa fa-money"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Amount</span>
                                    <span class="info-box-number">{{ review.amount }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-green"><i class="fa fa-tint"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Litres</span>
                                    <span class="info-box-number">{{ review.litres }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-green"><i class="fa fa-tag"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Unit Price</span>
                                    <span class="info-box-number">{{ review.unitPrice }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <p class="text-muted">
                                <em>Review created on {{ review.createdAt|date('Y-m-d H:i:s') }}</em>
                            </p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

{# Calculate best performing OCR method #}
{% if review is not null and not review.rejected and ocr_results|length > 0 %}
    {% set best_method = null %}
    {% set best_accuracy = -1 %}
    {% set tolerance = 0.01 %}
    
    {% for ocr_result in ocr_results %}
        {% set accuracy_count = 0 %}
        {% set total_fields = 0 %}
        
        {% if review.amount is not null and ocr_result.amount is not null %}
            {% set total_fields = total_fields + 1 %}
            {% if (review.amount - ocr_result.amount)|abs < tolerance %}
                {% set accuracy_count = accuracy_count + 1 %}
            {% endif %}
        {% endif %}
        
        {% if review.litres is not null and ocr_result.litres is not null %}
            {% set total_fields = total_fields + 1 %}
            {% if (review.litres - ocr_result.litres)|abs < tolerance %}
                {% set accuracy_count = accuracy_count + 1 %}
            {% endif %}
        {% endif %}
        
        {% if review.unitPrice is not null and ocr_result.unitPrice is not null %}
            {% set total_fields = total_fields + 1 %}
            {% if (review.unitPrice - ocr_result.unitPrice)|abs < tolerance %}
                {% set accuracy_count = accuracy_count + 1 %}
            {% endif %}
        {% endif %}
        
        {% set accuracy_percentage = total_fields > 0 ? (accuracy_count / total_fields * 100) : 0 %}
        
        {% if accuracy_percentage > best_accuracy %}
            {% set best_accuracy = accuracy_percentage %}
            {% set best_method = ocr_result.ocrMethod %}
        {% endif %}
    {% endfor %}
    
    {% if best_method is not null and best_accuracy > 0 %}
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h4><i class="icon fa fa-trophy"></i> Best Performing OCR Method</h4>
                <p>
                    <strong>{{ best_method.name }}</strong> had the highest accuracy ({{ best_accuracy|round }}%) when compared to the ground truth data.
                    {% if best_method.description %}
                    <br>
                    <small><em>{{ best_method.description }}</em></small>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    {% endif %}
{% endif %}

{# Main OCR Results Display #}
<div class="row">
    {% for ocr_result in ocr_results %}
        <div class="col-md-6">
            <div class="box box-primary {% if review is not null and not review.rejected and best_method is defined and best_method is not null and ocr_result.ocrMethod.id == best_method.id %}box-success{% endif %}">
                <div class="box-header with-border">
                    <h4 class="box-title">
                        {{ ocr_result.ocrMethod.name }}
                        {% if review is not null and not review.rejected and best_method is defined and best_method is not null and ocr_result.ocrMethod.id == best_method.id %}
                            <i class="fa fa-trophy" title="Best performing method"></i>
                        {% endif %}
                    </h4>
                    <div class="box-tools pull-right">
                        <span class="label label-info">Method ID: {{ ocr_result.ocrMethod.id }}</span>
                        {% if review is not null and not review.rejected %}
                            {% set accuracy_count = 0 %}
                            {% set total_fields = 0 %}
                            {% set tolerance = 0.01 %}
                            
                            {% if review.amount is not null and ocr_result.amount is not null %}
                                {% set total_fields = total_fields + 1 %}
                                {% if (review.amount - ocr_result.amount)|abs < tolerance %}
                                    {% set accuracy_count = accuracy_count + 1 %}
                                {% endif %}
                            {% endif %}
                            
                            {% if review.litres is not null and ocr_result.litres is not null %}
                                {% set total_fields = total_fields + 1 %}
                                {% if (review.litres - ocr_result.litres)|abs < tolerance %}
                                    {% set accuracy_count = accuracy_count + 1 %}
                                {% endif %}
                            {% endif %}
                            
                            {% if review.unitPrice is not null and ocr_result.unitPrice is not null %}
                                {% set total_fields = total_fields + 1 %}
                                {% if (review.unitPrice - ocr_result.unitPrice)|abs < tolerance %}
                                    {% set accuracy_count = accuracy_count + 1 %}
                                {% endif %}
                            {% endif %}
                            
                            {% set accuracy_percentage = total_fields > 0 ? (accuracy_count / total_fields * 100)|round : 0 %}
                            <span class="label 
                                {% if accuracy_percentage >= 90 %}label-success
                                {% elseif accuracy_percentage >= 60 %}label-warning
                                {% else %}label-danger{% endif %}">
                                Accuracy: {{ accuracy_percentage }}%
                            </span>
                        {% endif %}
                    </div>
                </div>

                <div class="box-body">
                    {% if ocr_result.ocrMethod.description %}
                        <p class="text-muted"><em>{{ ocr_result.ocrMethod.description }}</em></p>
                    {% endif %}

                            <table class="table table-bordered table-striped">
                                <tr>
                                    <th>Amount</th>
                                    <td>
                                        {{ ocr_result.amount }}
                                        {% if review is not null and not review.rejected and review.amount is not null %}
                                            {% set diff = (ocr_result.amount - review.amount)|abs %}
                                            {% set tolerance = 0.01 %}
                                            {% if diff < tolerance %}
                                                <span class="label label-success">Matches Ground Truth</span>
                                            {% else %}
                                                <span class="label label-danger">Differs from Ground Truth ({{ review.amount }})</span>
                                                <br>
                                                {% if review.amount != 0 %}
                                                    <small class="text-danger">Error: {{ (diff / review.amount * 100)|round(2) }}%</small>
                                                {% else %}
                                                    <small class="text-danger">Error: cannot calculate percentage (ground truth is zero)</small>
                                                {% endif %}
                                            {% endif %}
                                        {% elseif ocr_result.amount != object.amount %}
                                            <span class="label label-warning">Differs from payment ({{ object.amount }})</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Litres</th>
                                    <td>
                                        {{ ocr_result.litres }}
                                        {% if review is not null and not review.rejected and review.litres is not null %}
                                            {% set diff = (ocr_result.litres - review.litres)|abs %}
                                            {% set tolerance = 0.01 %}
                                            {% if diff < tolerance %}
                                                <span class="label label-success">Matches Ground Truth</span>
                                            {% else %}
                                                <span class="label label-danger">Differs from Ground Truth ({{ review.litres }})</span>
                                                <br>
                                                {% if review.litres != 0 %}
                                                    <small class="text-danger">Error: {{ (diff / review.litres * 100)|round(2) }}%</small>
                                                {% else %}
                                                    <small class="text-danger">Error: cannot calculate percentage (ground truth is zero)</small>
                                                {% endif %}
                                            {% endif %}
                                        {% elseif ocr_result.litres != object.litres %}
                                            <span class="label label-warning">Differs from payment ({{ object.litres }})</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Unit Price</th>
                                    <td>
                                        {{ ocr_result.unitPrice }}
                                        {% if review is not null and not review.rejected and review.unitPrice is not null %}
                                            {% set diff = (ocr_result.unitPrice - review.unitPrice)|abs %}
                                            {% set tolerance = 0.01 %}
                                            {% if diff < tolerance %}
                                                <span class="label label-success">Matches Ground Truth</span>
                                            {% else %}
                                                <span class="label label-danger">Differs from Ground Truth ({{ review.unitPrice }})</span>
                                                <br>
                                                {% if review.unitPrice != 0 %}
                                                    <small class="text-danger">Error: {{ (diff / review.unitPrice * 100)|round(2) }}%</small>
                                                {% else %}
                                                    <small class="text-danger">Error: cannot calculate percentage (ground truth is zero)</small>
                                                {% endif %}
                                            {% endif %}
                                        {% elseif ocr_result.unitPrice != object.unitPrice %}
                                            <span class="label label-warning">Differs from payment ({{ object.unitPrice }})</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if ocr_result.fuelType %}
                                <tr>
                                    <th>Fuel Type</th>
                                    <td>{{ ocr_result.fuelType }}</td>
                                </tr>
                                {% endif %}
                                {% if ocr_result.transactionDate %}
                                <tr>
                                    <th>Transaction Date</th>
                                    <td>{{ ocr_result.transactionDate|date('Y-m-d H:i:s') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                            <table class="table table-bordered table-striped">
                                <tr>
                                    <th>Processing Time</th>
                                    <td>
                                        {% if ocr_result.processingTime %}
                                            {{ ocr_result.processingTime }} ms
                                        {% else %}
                                            <span class="text-muted">Not recorded</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Mathematical Validation</th>
                                    <td>
                                        {% if ocr_result.isMathematicallyValid() %}
                                            <span class="label label-success">Valid</span>
                                        {% else %}
                                            <span class="label label-danger">Invalid</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Follow-up Required</th>
                                    <td>
                                        {% if ocr_result.followUpNeeded %}
                                            <span class="label label-warning">Yes</span>
                                        {% else %}
                                            <span class="label label-success">No</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ ocr_result.createdAt|date('Y-m-d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ ocr_result.updatedAt|date('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>

                    {% if ocr_result.followUpNeeded and ocr_result.followUpResponse %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box box-info">
                                    <div class="box-header with-border">
                                        <h4 class="box-title">Follow-up Response</h4>
                                    </div>
                                    <div class="box-body">
                                        <pre>{{ ocr_result.followUpResponse|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if ocr_result.rawResponse %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box box-default collapsed-box">
                                    <div class="box-header with-border">
                                        <h4 class="box-title">Raw OCR Response</h4>
                                        <div class="box-tools pull-right">
                                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                                <i class="fa fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="box-body">
                                        <pre>{{ ocr_result.rawResponse|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% if loop.index is even and not loop.last %}
            </div><div class="row">
        {% endif %}
    {% endfor %}
</div> 