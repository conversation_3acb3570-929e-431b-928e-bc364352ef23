{% extends '@SonataAdmin/CRUD/base_show.html.twig' %}

{% block title %}
    Review Payment Details - {{ parent() }}
{% endblock %}

{% block navbar_title %}
    {{ block('title') }}
{% endblock %}

{% block actions %}
    {% include '@SonataAdmin/Button/list_button.html.twig' %}
    {% include '@SonataAdmin/Button/show_button.html.twig' %}
{% endblock %}

{% block tab_menu %}{{ knp_menu_render(admin.sidemenu(action), {'currentClass' : 'active', 'template': get_global_template('tab_menu_template')}, 'twig') }}{% endblock %}

{% block show %}
    <div class="row">
        <div class="col-md-6">
            <div class="box box-primary">
                <div class="box-header">
                    <h4 class="box-title">Payment Details Information</h4>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-striped">
                        <tr>
                            <th>ID</th>
                            <td>{{ object.id }}</td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td>{{ object.customerUser }}</td>
                        </tr>
                        <tr>
                            <th>Filling Station</th>
                            <td>{{ object.fillingStation }}</td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ object.createdAt|date('Y-m-d H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Created By</th>
                            <td>{{ object.createdBy }}</td>
                        </tr>
                        <tr>
                            <th>Manual Entry</th>
                            <td>
                                <span class="label label-warning">Yes</span>
                            </td>
                        </tr>
                        {% if object.ocrFailReason %}
                        <tr>
                            <th>OCR Fail Reason</th>
                            <td>{{ object.ocrFailReason }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="box box-primary">
                <div class="box-header">
                    <h4 class="box-title">Payment Details Image</h4>
                </div>
                <div class="box-body text-center">
                    {% if object.image %}
                        <a href="{{ object.image }}" target="_blank">
                            <img src="{{ object.image }}" alt="Payment Details Image" class="img-responsive" style="max-height: 300px; margin: 0 auto;">
                        </a>
                    {% else %}
                        <div class="alert alert-warning">No image available</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h4 class="box-title">Review Payment Details</h4>
                </div>
                <div class="box-body">
                    {{ form_start(form) }}
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form_label(form.amount) }}
                                {{ form_widget(form.amount) }}
                                {{ form_errors(form.amount) }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form_label(form.litres) }}
                                {{ form_widget(form.litres) }}
                                {{ form_errors(form.litres) }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form_label(form.unitPrice) }}
                                {{ form_widget(form.unitPrice) }}
                                {{ form_errors(form.unitPrice) }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                {{ form_label(form.reviewNotes) }}
                                {{ form_widget(form.reviewNotes) }}
                                {{ form_errors(form.reviewNotes) }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-success">
                                <div class="box-header">
                                    <h4 class="box-title">Loyalty Points Calculation</h4>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-aqua"><i class="fa fa-calculator"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Points Calculation</span>
                                                    <span class="info-box-number" id="points-calculation">{{ pointsCalculation }}</span>
                                                    <span class="info-box-text text-muted">Rs100 = 1 point</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-green"><i class="fa fa-star"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Current Points</span>
                                                    <span class="info-box-number">{{ currentPoints }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-yellow"><i class="fa fa-star-half-o"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Projected Points</span>
                                                    <span class="info-box-number" id="projected-points">{{ projectedPoints }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="btn-group">
                                    {{ form_widget(form.approve) }}
                                    {{ form_widget(form.saveChanges) }}
                                    {{ form_widget(form.reject) }}
                                    <a href="{{ admin.generateObjectUrl('show', object) }}" class="btn btn-default">Cancel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            // Function to update points calculation
            function updatePointsCalculation() {
                var amount = parseFloat($('.amount-field').val());
                var pointsRatio = parseFloat($('.amount-field').data('points-ratio'));
                
                if (!isNaN(amount) && !isNaN(pointsRatio)) {
                    var points = amount / pointsRatio;
                    var currentPoints = {{ currentPoints }};
                    var projectedPoints = currentPoints + Math.floor(points);
                    
                    $('#points-calculation').text(points.toFixed(2));
                    $('#projected-points').text(projectedPoints);
                }
            }
            
            // Update points calculation when amount changes
            $('.amount-field').on('input', updatePointsCalculation);
            
            // Initial calculation
            updatePointsCalculation();
        });
    </script>
{% endblock %}
