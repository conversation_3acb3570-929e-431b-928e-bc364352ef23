<td>
    {% if object.review is not empty %}
    <a href="{{ path('admin_app_paymentdetailsreview_edit', {'id': object.review.id }) }}">
{% if object.qcState == constant('App\\Entity\\PaymentDetails::QC_STATE_PASSED') %}
        <span class="label label-success">{{ object.qcState }}</span>
{% elseif object.qcState == constant('App\\Entity\\PaymentDetails::QC_STATE_CORRECTED') %}
    <span class="label label-warning">{{ object.qcState }}</span>
{% elseif object.qcState == constant('App\\Entity\\PaymentDetails::QC_STATE_REJECTED') %}
    <span class="label label-danger">{{ object.qcState }}</span>
{% else %}
    <span class="label label-default">{{ object.qcState }}</span>
{% endif %}
    </a>
{% endif %}
</td>