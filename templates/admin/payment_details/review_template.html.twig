{# Display Ground Truth section if review exists #}
{% set object = object|default(admin.subject) %}
{% set review = object.review %}

{% if review is not null %}
{% if review.rejected %}
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i> This payment was marked as rejected during quality control review.
    </div>
{% else %}
    <div class="row">
        <div class="col-md-4">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-money"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Amount</span>
                    <span class="info-box-number">{{ review.amount }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-tint"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Litres</span>
                    <span class="info-box-number">{{ review.litres }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-tag"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Unit Price</span>
                    <span class="info-box-number">{{ review.unitPrice }}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <p class="text-muted">
                <em>Review created on {{ review.createdAt|date('Y-m-d H:i:s') }}</em>
            </p>
        </div>
    </div>
{% endif %}
{% endif %} 