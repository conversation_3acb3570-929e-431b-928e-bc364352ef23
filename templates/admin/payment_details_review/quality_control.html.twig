{% extends '@SonataAdmin/CRUD/base_list.html.twig' %}

{% block title %}
    Quality Control - {{ parent() }}
{% endblock %}

{% block navbar_title %}
    {{ block('title') }}
{% endblock %}

{% block actions %}
    {% include '@SonataAdmin/Button/list_button.html.twig' %}
{% endblock %}

{% block tab_menu %}{{ knp_menu_render(admin.sidemenu(action), {'currentClass' : 'active', 'template': get_global_template('tab_menu_template')}, 'twig') }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .metric-card {
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
        }
        .metric-label {
            font-size: 14px;
            color: #777;
        }
        .chart-container {
            position: relative;
            margin: 20px 0;
            height: 300px;
        }
        .data-source-toggle {
            margin-bottom: 15px;
        }
        .data-source-toggle .btn {
            margin-right: 5px;
        }
        .improvement-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 5px;
        }
        .legend-item {
            display: inline-block;
            margin-right: 15px;
        }
        .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 5px;
            border-radius: 2px;
        }

        /* Progress bar styling */
        .info-box .progress {
            height: 5px;
            margin: 5px 0 0 0;
            background-color: rgba(0,0,0,0.1);
        }

        .info-box .progress-bar {
            transition: width 0.5s ease;
        }

        .info-box .progress-bar.bg-blue {
            background-color: #0073b7;
        }

        .info-box .progress-bar.bg-green {
            background-color: #00a65a;
        }

        .info-box .progress-bar.bg-purple {
            background-color: #605ca8;
        }

        .info-box .progress-bar.bg-yellow {
            background-color: #f39c12;
        }

        .info-box .progress-bar.bg-aqua {
            background-color: #00c0ef;
        }

        /* Make sure chart containers have proper height */
        @media (max-width: 767px) {
            .chart-container {
                height: 250px;
            }
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <!-- Load the JavaScript file first -->
    <script src="{{ asset('js/quality_control.js') }}"></script>

    <script>
        // Register the Chart.js plugins
        Chart.register(ChartDataLabels);
    </script>

    <!-- Call the initialization function with data from controller -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            init_quality_control(
                {{ summaryMetrics|json_encode|raw }},
                {{ fieldAccuracy|json_encode|raw }},
                {{ errorTypes|json_encode|raw }},
                {{ correlationData|json_encode|raw }},
                {{ geolocationMetrics|json_encode|raw }}
            );
        });
    </script>
{% endblock %}

{% block list_table %}
    <section class="content">
        <!-- Summary Metrics Panel -->
        <div class="row">
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-blue"><i class="fa fa-check-circle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Current System Accuracy</span>
                        <span class="info-box-number" id="current-accuracy">-</span>
                        <div class="progress">
                            <div class="progress-bar bg-blue" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-green"><i class="fa fa-arrow-up"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Improved System Accuracy</span>
                        <span class="info-box-number" id="improvement1-accuracy">-</span>
                        <span class="label label-success pull-right" id="accuracy-improvement">-</span>
                        <div class="progress">
                            <div class="progress-bar bg-green" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-purple"><i class="fa fa-chart-line"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Correlation Validation</span>
                        <span class="info-box-number">
                            <span id="current-correlation">-</span> → <span id="improvement1-correlation">-</span>
                            <span class="label label-success pull-right" id="correlation-improvement">-</span>
                        </span>
                        <div class="progress">
                            <div class="progress-bar bg-purple" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-yellow"><i class="fa fa-file-alt"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Processed</span>
                        <span class="info-box-number" id="total-processed">-</span>
                        <div class="progress">
                            <div class="progress-bar bg-yellow" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Geolocation Summary -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-aqua"><i class="fa fa-map-marker-alt"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Geolocation Accuracy</span>
                        <span class="info-box-number" id="geolocation-accuracy">-</span>
                        <span class="info-box-desc">Percentage of payments made at the closest filling station</span>
                        <div class="progress">
                            <div class="progress-bar bg-aqua" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-aqua"><i class="fa fa-road"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Average Distance to Station</span>
                        <span class="info-box-number" id="average-distance">-</span>
                        <span class="info-box-desc">Average distance between payment location and filling station</span>
                        <div class="progress">
                            <div class="progress-bar bg-aqua" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Field Accuracy Comparison Box -->
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">Field Accuracy Comparison</h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <div class="btn-group data-source-toggle">
                    <button type="button" class="btn btn-default active toggle-datasource" data-source="Manual" data-chart="field-accuracy-chart">
                        <i class="fa fa-user"></i> Manual QC
                    </button>
                    <button type="button" class="btn btn-default active toggle-datasource" data-source="Current" data-chart="field-accuracy-chart">
                        <i class="fa fa-robot"></i> Current System
                    </button>
                    <button type="button" class="btn btn-default active toggle-datasource" data-source="Improved" data-chart="field-accuracy-chart">
                        <i class="fa fa-arrow-up"></i> Improved System
                    </button>
                </div>
                <div class="chart-container">
                    <canvas id="field-accuracy-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Correlation and Error Analysis Row -->
        <div class="row">
            <!-- Correlation Validation Box -->
            <div class="col-md-6">
                <div class="box box-default">
                    <div class="box-header">
                        <h4 class="box-title">Correlation Validation</h4>
                    </div>
                    <div class="box-body">
                        <div class="chart-container">
                            <canvas id="correlation-chart"></canvas>
                        </div>
                        <div class="text-muted">
                            <small>Closer to the diagonal line indicates better correlation between calculated and extracted values.</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Type Distribution Box -->
            <div class="col-md-6">
                <div class="box box-default">
                    <div class="box-header">
                        <h4 class="box-title">Error Type Distribution</h4>
                    </div>
                    <div class="box-body">
                        <div class="chart-container">
                            <canvas id="error-types-chart"></canvas>
                        </div>
                        <div class="form-group">
                            <label for="error-type-selector">Select System:</label>
                            <select id="error-type-selector" class="form-control">
                                <option value="currentSystem">Current System</option>
                                <option value="improvedSystem">Improved System</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Geolocation Analysis Box -->
        <div class="box box-default">
            <div class="box-header">
                <h4 class="box-title">Geolocation Accuracy Analysis</h4>
            </div>
            <div class="box-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">Geolocation Accuracy Metrics</div>
                            <div class="panel-body">
                                <table class="table table-striped">
                                    <tbody>
                                        <tr>
                                            <td>Total payments with location data:</td>
                                            <td id="geo-total-count">-</td>
                                        </tr>
                                        <tr>
                                            <td>Payments at closest station:</td>
                                            <td id="geo-correct-count">-</td>
                                        </tr>
                                        <tr>
                                            <td>Geolocation accuracy:</td>
                                            <td id="geo-accuracy">-</td>
                                        </tr>
                                        <tr>
                                            <td>Average distance to station:</td>
                                            <td id="geo-avg-distance">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="geo-chart-selector">View Accuracy By:</label>
                            <select id="geo-chart-selector" class="form-control">
                                <option value="station">Filling Station</option>
                                <option value="time">Time Period</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container" style="height: 600px;"> <!-- Increased height for more stations -->
                            <canvas id="geo-accuracy-chart"></canvas>
                        </div>
                        <div class="text-muted">
                            <small>Geolocation accuracy: percentage of payments where the closest filling station matches the assigned station</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
