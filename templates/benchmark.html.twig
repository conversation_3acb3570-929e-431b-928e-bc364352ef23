<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Data Table</title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>

<body>
	<div class="container mt-5">
		<h2 class="mb-4">AI Benchmark</h2>
		{% for data in dataset %}
		<div class="table-responsive mb-5">
			<h3 class="mb-2">Photo #{{loop.index}}</h3>
			<table class="table table-striped table-bordered table-hover">
				<thead class="table-dark">
					<tr>
						<th></th>
						{% for item in data %}
						<th class="text-center">{{ item['title'] }}</th>
						{% endfor %}
					</tr>
					<tr>
						<th scope="col"></th>
						{% for item in data %}
						<th class="text-center" scope="col">
							<a href="{{ item['url'] }}">
								<img src="{{ item['url'] }}" width="128" height="128">
							</a>
						</th>
						{% endfor %}
					</tr>
				</thead>
				<tbody>
					<tr>
						<th>Amount</th>
						{% for item in data %}
						<td class="text-end">{{ item['amount'] | number_format(2) }}</td>
						{% endfor %}
					</tr>
					</tr>
					<tr>
						<th>Litres</th>
						{% for item in data %}
						<td class="text-end">{{ item['litres'] | number_format(2) }}</td>
						{% endfor %}
					</tr>
					<tr>
						<th>Rs./Litres</th>
						{% for item in data %}
						<td class="text-end">{{ item['unitPrice'] | number_format(2) }}</td>
						{% endfor %}
					</tr>
					<tr>
						<th>Status</th>
						{% for item in data %}
						<td class="text-center">
							<span
								class="badge {% if item['status'] == 'OK' %}bg-success{% else %}bg-danger{% endif %} px-3 py-2">
								{{ item['status'] }}
							</span>
						</td>
						{% endfor %}
					</tr>
				<tbody>
			</table>
		</div>
		{% endfor %}
	</div>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>