<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f4f6f9;
            margin: 0;
        }
        .login-box {
            width: 400px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .login-box h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-logo img {
            display: block;
            margin: 0 auto 10px auto;
        }
        .btn-block {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <div class="login-box-header">
            <div class="login-logo">
                        <div>
                            <img src="/images/logo.png" alt="IOML Rewards Logo" width="175" height="171" />
                        </div>
                <h1>Reset Password</h1>
            </div>
        </div>
        <div class="login-box-body">
            {% block sonata_user_reset_form %}
                <p class="login-box-msg">{{ 'resetting.reset.submit'|trans({}, 'SonataUserBundle') }}</p>
                {{ form_start(form, { 'action': path('app_reset_password', {'token': token}) }) }}
                    <div class="form-group">
                        {{ form_widget(form.plainPassword.first, {'attr': {
                            'class': 'form-control',
                            'placeholder': 'form.new_password'|trans({}, 'SonataUserBundle')
                        }}) }}
                        {{ form_errors(form.plainPassword.first) }}
                    </div>
                    <div class="form-group">
                        {{ form_widget(form.plainPassword.second, {'attr': {
                            'class': 'form-control',
                            'placeholder': 'form.new_password_confirmation'|trans({}, 'SonataUserBundle')
                        }}) }}
                        {{ form_errors(form.plainPassword.second) }}
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <button type="submit" name="submit" class="btn btn-primary btn-block btn-flat">
                                {{ 'resetting.reset.submit'|trans({}, 'SonataUserBundle') }}
                            </button>
                        </div>
                    </div>
                {{ form_end(form) }}
            {% endblock %}
        </div>
    </div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
</body>
</html>