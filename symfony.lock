{"doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "c170ded8fc587d6bd670550c43dafcf093762245"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "gesdinet/jwt-refresh-token-bundle": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "2390b4ed5c195e0b3f6dea45221f3b7c0af523a0"}, "files": ["config/packages/gesdinet_jwt_refresh_token.yaml", "config/routes/gesdinet_jwt_refresh_token.yaml", "src/Entity/RefreshToken.php"]}, "knplabs/knp-menu-bundle": {"version": "v3.4.2"}, "lexik/jwt-authentication-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "liip/test-fixtures-bundle": {"version": "3.1.1"}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "sonata-project/admin-bundle": {"version": "4.31", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "0e5931df1732e3dccfba42a20853049e5e9db6ae"}, "files": ["config/packages/sonata_admin.yaml", "config/routes/sonata_admin.yaml", "src/Admin/.gitignore"]}, "sonata-project/block-bundle": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.11", "ref": "b4edd2a1e6ac1827202f336cac2771cb529de542"}, "files": ["config/packages/sonata_block.yaml"]}, "sonata-project/doctrine-extensions": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "4ea4a4b6730f83239608d7d4c849533645c70169"}}, "sonata-project/doctrine-orm-admin-bundle": {"version": "4.17.1"}, "sonata-project/exporter": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "93d6df022ef1dc24bdfa8667ddd560bbde89a7cc"}}, "sonata-project/form-extensions": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "9c8a1e8ce2b1f215015ed16652c4ed18eb5867fd"}, "files": ["config/packages/sonata_form.yaml"]}, "sonata-project/media-bundle": {"version": "4.15", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "ec54b1d3b1ec60cd92f482f65bb707c765fd5e8d"}, "files": ["config/packages/sonata_media.yaml", "config/routes/sonata_media.yaml", "public/uploads/media/.gitignore"]}, "sonata-project/twig-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "30dba2f9b719f21b497a6302f41aac07f9079e13"}}, "sonata-project/user-bundle": {"version": "5.14.0"}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "32126346f25e1cee607cc4aa6783d46034920554"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/google-mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "f8fd4ddb9b477510f8f4bce2b9c054ab428c0120"}}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.61", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/phpunit-bridge": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}}