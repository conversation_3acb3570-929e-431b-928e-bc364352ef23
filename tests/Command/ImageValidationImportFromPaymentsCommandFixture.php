<?php

namespace App\Tests\Command;

use App\Entity\FillingStation;
use App\Entity\PaymentDetails;
use App\Entity\SonataUserUser;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class ImageValidationImportFromPaymentsCommandFixture extends Fixture
{
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(UserPasswordHasherInterface $passwordHasher)
    {
        $this->passwordHasher = $passwordHasher;
    }

    public function load(ObjectManager $manager): void
    {
        // Create test user
        $user = new SonataUserUser();
        $user->setUsername('test_user_fixture');
        $user->setEmail('<EMAIL>');
        $user->setEnabled(true);
        
        // Hash a password for the user
        $hashedPassword = $this->passwordHasher->hashPassword($user, 'test_password');
        $user->setPassword($hashedPassword);
        
        $manager->persist($user);
        
        // Create test filling station
        $fillingStation = new FillingStation();
        $fillingStation->setName('Test Station Fixture');
        $fillingStation->setAddress('Test Address');
        
        $manager->persist($fillingStation);
        
        // Create test payment details
        $this->createPaymentDetail($manager, $user, $fillingStation, [
            'amount' => '100.00',
            'litres' => '10.00',
            'unitPrice' => '10.00',
            'image' => '/test/valid_image.jpg'
        ]);
        
        $this->createPaymentDetail($manager, $user, $fillingStation, [
            'amount' => '95.00',
            'litres' => '10.00',
            'unitPrice' => '10.00',
            'image' => '/test/invalid_math.jpg'
        ]);
        
        $this->createPaymentDetail($manager, $user, $fillingStation, [
            'amount' => '0',
            'litres' => '0',
            'unitPrice' => '0',
            'image' => '/test/missing_data.jpg'
        ]);
        
        $manager->flush();
        
        // Set references for the test
        $this->addReference('test_user', $user);
        $this->addReference('test_filling_station', $fillingStation);
    }
    
    private function createPaymentDetail(
        ObjectManager $manager,
        SonataUserUser $user,
        FillingStation $fillingStation,
        array $data
    ): void {
        $paymentDetail = new PaymentDetails();
        $paymentDetail->setCustomerUser($user);
        $paymentDetail->setCreatedBy($user);
        $paymentDetail->setFillingStation($fillingStation);
        $paymentDetail->setAmount($data['amount']);
        $paymentDetail->setLitres($data['litres']);
        $paymentDetail->setUnitPrice($data['unitPrice']);
        $paymentDetail->setImage($data['image']);
        $paymentDetail->setCreatedAt(new \DateTimeImmutable());
        
        $manager->persist($paymentDetail);
    }
}
