<?php

namespace App\Tests\Entity;

use App\Entity\SonataUserUser;
use PHPUnit\Framework\TestCase;


class SonataUserUserTest extends TestCase
{
    /**
     * @dataProvider providePhoneNumbers
     */
    public function testSanitizePhoneNumber($phone, $expected)
    {
        $this->assertEquals($expected, SonataUserUser::sanitizePhoneNumber($phone));
    }

    public function providePhoneNumbers()
    {
        return [
            ['5373 1224', '+23053731224'],
            ['5373 12 24', '+23053731224'],
            ['+230 5373 12 24', '+23053731224'],
            ['+23053731224', '+23053731224'],
            ['+230 53731224', '+23053731224'],
            ['00230 53731224', '+23053731224'],
        ];
    }
}
