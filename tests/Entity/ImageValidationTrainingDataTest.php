<?php

namespace App\Tests\Entity;

use App\Entity\ImageValidationTrainingData;
use App\Entity\PaymentDetails;
use PHPUnit\Framework\TestCase;

class ImageValidationTrainingDataTest extends TestCase
{
    private ImageValidationTrainingData $entity;
    private PaymentDetails $paymentDetails;

    protected function setUp(): void
    {
        $this->entity = new ImageValidationTrainingData();
        $this->paymentDetails = new PaymentDetails();
    }

    public function testGetSetId(): void
    {
        // ID is set by the database, so we can only test the getter
        $this->assertNull($this->entity->getId());
    }

    public function testGetSetPaymentDetailsId(): void
    {
        $this->assertNull($this->entity->getPayemtDetailsId());
        
        $result = $this->entity->setPayemtDetailsId($this->paymentDetails);
        
        $this->assertSame($this->entity, $result);
        $this->assertSame($this->paymentDetails, $this->entity->getPayemtDetailsId());
    }

    public function testGetSetImagePath(): void
    {
        $imagePath = '/path/to/image.jpg';
        
        $this->assertNull($this->entity->getImagePath());
        
        $result = $this->entity->setImagePath($imagePath);
        
        $this->assertSame($this->entity, $result);
        $this->assertSame($imagePath, $this->entity->getImagePath());
    }

    public function testIsSetValid(): void
    {
        $this->assertNull($this->entity->isValid());
        
        $result = $this->entity->setValid(true);
        
        $this->assertSame($this->entity, $result);
        $this->assertTrue($this->entity->isValid());
        
        $this->entity->setValid(false);
        $this->assertFalse($this->entity->isValid());
    }

    public function testIsSetMathematicalValidation(): void
    {
        $this->assertNull($this->entity->isMathematicalValidation());
        
        $result = $this->entity->setMathematicalValidation(true);
        
        $this->assertSame($this->entity, $result);
        $this->assertTrue($this->entity->isMathematicalValidation());
        
        $this->entity->setMathematicalValidation(false);
        $this->assertFalse($this->entity->isMathematicalValidation());
    }

    public function testGetSetValidationNote(): void
    {
        $note = 'This is a validation note';
        
        $this->assertNull($this->entity->getValidationNote());
        
        $result = $this->entity->setValidationNote($note);
        
        $this->assertSame($this->entity, $result);
        $this->assertSame($note, $this->entity->getValidationNote());
        
        // Test with null value
        $this->entity->setValidationNote(null);
        $this->assertNull($this->entity->getValidationNote());
    }

    public function testGetSetCreatedAt(): void
    {
        $date = new \DateTimeImmutable();
        
        $this->assertNull($this->entity->getCreatedAt());
        
        $result = $this->entity->setCreatedAt($date);
        
        $this->assertSame($this->entity, $result);
        $this->assertSame($date, $this->entity->getCreatedAt());
    }

    public function testGetSetUpdatedAt(): void
    {
        $date = new \DateTimeImmutable();
        
        $this->assertNull($this->entity->getUpdatedAt());
        
        $result = $this->entity->setUpdatedAt($date);
        
        $this->assertSame($this->entity, $result);
        $this->assertSame($date, $this->entity->getUpdatedAt());
        
        // Test with null value
        $this->entity->setUpdatedAt(null);
        $this->assertNull($this->entity->getUpdatedAt());
    }

    public function testIsSetIncludeInExport(): void
    {
        // Default value should be false
        $this->assertFalse($this->entity->isIncludeInExport());
        
        $result = $this->entity->setIncludeInExport(true);
        
        $this->assertSame($this->entity, $result);
        $this->assertTrue($this->entity->isIncludeInExport());
        
        $this->entity->setIncludeInExport(false);
        $this->assertFalse($this->entity->isIncludeInExport());
    }
}
