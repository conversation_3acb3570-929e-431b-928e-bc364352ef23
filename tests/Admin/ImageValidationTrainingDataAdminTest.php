<?php

namespace App\Tests\Admin;

use App\Entity\ImageValidationTrainingData;
use App\Entity\PaymentDetails;
use App\Entity\SonataUserUser;
use Doctrine\ORM\EntityManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

class ImageValidationTrainingDataAdminTest extends WebTestCase
{
    private $admin;
    private $em;
    private $databaseTool;

    public function setUp(): void
    {
        parent::setUp();

        $client = $this->createClient();
        $this->em = $client->getKernel()->getContainer()
            ->get('doctrine.orm.entity_manager');
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $this->admin = new SonataUserUser();
        $this->admin->setUsername('admin');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);
        $this->em->persist($this->admin);
        $this->em->flush();

        // Login the admin user
        $crawler = $client->request(Request::METHOD_GET, '/admin/login');
        $button = $crawler->selectButton('Log in');
        $form = $button->form();
        $form['_username'] = '<EMAIL>';
        $form['_password'] = 'admin';
        $client->submit($form);

        // Follow redirect after login
        $client->followRedirect();
    }

    public function testListImageValidationTrainingData(): void
    {
        $client = $this->getClient();
        $client->request('GET', '/admin/app/imagevalidationtrainingdata/list');
        $this->assertResponseIsSuccessful();
    }

    public function testCreateRouteIsDisabled(): void
    {
        $client = $this->getClient();
        $client->request('GET', '/admin/app/imagevalidationtrainingdata/create');
        $this->assertResponseStatusCodeSame(404);
    }
}
