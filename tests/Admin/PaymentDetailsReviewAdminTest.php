<?php

namespace App\Tests\Admin;

use App\Entity\PaymentDetails;
use App\Entity\SonataUserUser;
use Li<PERSON>\TestFixturesBundle\Services\DatabaseToolCollection;
use Sonata\UserBundle\Entity\UserManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

class PaymentDetailsReviewAdminTest extends WebTestCase
{
    /**
     * @var AbstractDatabaseTool
     */
    private $databaseTool;

    /**
     * @var SonataUserUser
     */
    private $admin;

    /**
     * @var EntityManagerInterface
     */
    private $em;

    public function setUp(): void
    {
        parent::setUp();

        $client = $this->createClient();
        $this->em = $client->getKernel()->getContainer()
            ->get('doctrine.orm.entity_manager');
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $this->admin = new SonataUserUser();
        $this->admin->setUsername('admin');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);
        $this->em->persist($this->admin);
        $this->em->flush();

        $crawler = $client->request(Request::METHOD_GET, '/admin/login');
        $button = $crawler->selectButton('Log in');
        $form = $button->form();
        $form['_username'] = '<EMAIL>';
        $form['_password'] = 'admin';
        $client->submit($form);
    }

    public function testCgetPaymentDetailsReviews(): void
    {
        $client = $this->getClient();
        $client->request('GET', '/admin/app/paymentdetailsreview/list');
        $this->assertResponseIsSuccessful();
    }

    public function testCreatePaymentDetailsReview(): void
    {
        $client = $this->getClient();
        $admin = $this->em
            ->getRepository(SonataUserUser::class)
            ->find($this->admin->getId());

        $this->assertNotNull($this->admin);

        // Create and persist a PaymentDetails entity
        $paymentDetails = new PaymentDetails();
        $paymentDetails->setAmount('100.00');
        $paymentDetails->setLitres('10.00');
        $paymentDetails->setUnitPrice('10.00');
        $paymentDetails->setImage('/payment_details_img.jpg');
        $paymentDetails->setCustomerUser($admin);
        $paymentDetails->setCreatedBy($admin);
        $paymentDetails->setFillingStation(null); // Set appropriate FillingStation entity if needed

        $this->em->persist($paymentDetails);
        $this->em->flush();

        // Test the creation of PaymentDetailsReview
        $crawler = $client->request('GET', '/admin/app/paymentdetailsreview/create?uniqid=unittest');
        $this->assertResponseIsSuccessful();
        $this->assertCount(1, $crawler->filter('#unittest_amount'));
        $this->assertCount(1, $crawler->filter('img[src="/payment_details_img.jpg"]'));

        // Check that the values have already been set from the values of the PaymentDetails
        $form = $crawler->selectButton('btn_create_and_list')->form();
        $this->assertEquals('100.00', $form['unittest[amount]']->getValue());
        $this->assertEquals('10.00', $form['unittest[litres]']->getValue());
        $this->assertEquals('10.00', $form['unittest[unitPrice]']->getValue());

        // Submit the form
        $client->submit($form);

        // Assert the form submission was successful
        $this->assertResponseRedirects();
        $this->assertStringStartsWith(
            '/admin/app/paymentdetailsreview/list',
            $client->getResponse()->headers->get('Location')
        );
        $client->followRedirect();
        $this->assertResponseIsSuccessful();

        // Verify that the paymentDetails.qcState is 'passed'
        /** @var PaymentDetails */
        $updatedPaymentDetails = $this->em
            ->getRepository(PaymentDetails::class)
            ->find($paymentDetails->getId());
        $this->em->refresh($updatedPaymentDetails);
        $this->assertEquals(
            PaymentDetails::QC_STATE_PASSED,
            $updatedPaymentDetails->getQcState()
        );
    }

    public function testCreatePaymentDetailsReviewRejected(): void
    {
        $client = $this->getClient();
        $admin = $this->em
            ->getRepository(SonataUserUser::class)
            ->find($this->admin->getId());

        $this->assertNotNull($this->admin);

        // Create and persist a PaymentDetails entity
        $paymentDetails = new PaymentDetails();
        $paymentDetails->setAmount('100.00');
        $paymentDetails->setLitres('10.00');
        $paymentDetails->setUnitPrice('10.00');
        $paymentDetails->setImage('/payment_details_img.jpg');
        $paymentDetails->setCustomerUser($admin);
        $paymentDetails->setCreatedBy($admin);
        $paymentDetails->setFillingStation(null); // Set appropriate FillingStation entity if needed

        $this->em->persist($paymentDetails);
        $this->em->flush();

        // Test the creation of PaymentDetailsReview
        $crawler = $client->request('GET', '/admin/app/paymentdetailsreview/create?uniqid=unittest');
        $this->assertResponseIsSuccessful();
        $this->assertCount(1, $crawler->filter('#unittest_amount'));
        $this->assertCount(1, $crawler->filter('img[src="/payment_details_img.jpg"]'));

        // Check that the values have already been set from the values of the PaymentDetails
        $form = $crawler->selectButton('btn_create_and_list')->form();
        $form['unittest[rejected]']->setValue(true); // Set rejected to true

        // Submit the form
        $client->submit($form);

        // Assert the form submission was successful
        $this->assertResponseRedirects();
        $this->assertStringStartsWith(
            '/admin/app/paymentdetailsreview/list',
            $client->getResponse()->headers->get('Location')
        );
        $client->followRedirect();
        $this->assertResponseIsSuccessful();

        // Verify that the paymentDetails.qcState is 'rejected'
        /** @var PaymentDetails */
        $updatedPaymentDetails = $this->em
            ->getRepository(PaymentDetails::class)
            ->find($paymentDetails->getId());
        $this->em->refresh($updatedPaymentDetails);
        $this->assertEquals(
            PaymentDetails::QC_STATE_REJECTED,
            $updatedPaymentDetails->getQcState()
        );
    }
}
