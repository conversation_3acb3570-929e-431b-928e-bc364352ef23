<?php

namespace App\Tests\Admin;

use App\Entity\SonataUserUser;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

class RewardsItemAdminTest extends WebTestCase
{
    /**
     * @var AbstractDatabaseTool
     */
    private $databaseTool;

    /**
     * @var SonataUserUser
     */
    private $admin;

    public function setUp(): void
    {
        parent::setUp();

        $client = $this->createClient();
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $container = $this->getContainer();
        /** @var UserManager */
        $userManager = $container->get('sonata.user.manager.user');
        $this->admin = new SonataUserUser();
        $this->admin->setUsername('admin');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);
        $userManager->save($this->admin);

        $crawler = $client->request(Request::METHOD_GET, '/admin/login');
        $button = $crawler->selectButton('Log in');
        $form = $button->form();
        $form['_username'] = '<EMAIL>';
        $form['_password'] = 'admin';
        $client->submit($form);
    }

    public function testCgetRewardsItems(): void
    {
        $client = $this->getClient();
        $client->request('GET', '/admin/app/rewardsitem/list');
        $this->assertResponseIsSuccessful();
    }
}
