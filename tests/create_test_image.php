<?php
// Create a blank image
$image = imagecreatetruecolor(300, 200);

// Define colors
$white = imagecolorallocate($image, 255, 255, 255);
$black = imagecolorallocate($image, 0, 0, 0);

// Fill the background
imagefill($image, 0, 0, $white);

// Add text
imagestring($image, 5, 50, 50, 'Amount: 200.00', $black);
imagestring($image, 5, 50, 100, 'Litres: 3.27', $black);
imagestring($image, 5, 50, 150, 'Price: 61.20', $black);

// Save the image
imagejpeg($image, 'tests/fuel_meter_demo.jpg');

// Free memory
imagedestroy($image);

echo "Test image created successfully.\n";
