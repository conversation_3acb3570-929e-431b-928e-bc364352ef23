<?php

namespace App\Tests\Service;


use App\Service\EmailGenerationService;
use App\Service\PhoneValidationService;
use PHPUnit\Framework\TestCase;

class EmailGenerationServiceTest extends TestCase
{
    private EmailGenerationService $emailGenerationService;
    private $phoneValidationService;

    protected function setUp(): void
    {
        $this->phoneValidationService = $this->createMock(PhoneValidationService::class);

        // Configure the mock to return formatted phone numbers
        $this->phoneValidationService->method('format')
            ->willReturnCallback(function ($phoneNumber) {
                // Special case for the test data
                if (strpos($phoneNumber, '00230') === 0) {
                    return '+23054691234';
                }

                // For empty or special chars only, return default country code
                if (empty(preg_replace('/[^0-9]/', '', $phoneNumber))) {
                    return '+230';
                }

                // For normal numbers, extract only the digits and ensure it has the country code
                $cleanNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

                // If the number already has the country code (230), don't add it again
                if (strpos($cleanNumber, '230') === 0) {
                    return '+' . $cleanNumber;
                }

                // Otherwise, add the country code
                return '+230' . $cleanNumber;
            });

        $this->emailGenerationService = new EmailGenerationService($this->phoneValidationService);
    }

    /**
     * @dataProvider phoneNumberProvider
     */
    public function testGenerateFromPhone(string $phoneNumber, string $expectedEmail): void
    {
        $email = $this->emailGenerationService->generateFromPhone($phoneNumber);
        $this->assertEquals($expectedEmail, $email);
    }

    /**
     * @dataProvider emailProvider
     */
    public function testIsAutoGenerated(string $email, bool $expected): void
    {
        $result = $this->emailGenerationService->isAutoGenerated($email);
        $this->assertEquals($expected, $result);
    }

    /**
     * Test edge cases like empty strings and null values for email generation
     */
    public function testGenerateFromPhoneEdgeCases(): void
    {
        // Empty string
        $email = $this->emailGenerationService->generateFromPhone('');
        $this->assertEquals('<EMAIL>', $email);

        // String with only special characters
        $email = $this->emailGenerationService->generateFromPhone('+-()');
        $this->assertEquals('<EMAIL>', $email);
    }

    /**
     * Data provider for phone numbers and expected emails
     */
    public function phoneNumberProvider(): array
    {
        return [
            // Normal phone numbers
            ['54691234', '<EMAIL>'],
            ['+230 5469 1234', '<EMAIL>'],
            ['00230 546-91234', '<EMAIL>'],
            ['+230-5469-1234', '<EMAIL>'],

            // Phone numbers with international format already
            ['+2305469 1234', '<EMAIL>'],

            // With spaces and special characters
            ['5469 1234', '<EMAIL>'],
            ['5469-1234', '<EMAIL>'],
            ['(5469) 1234', '<EMAIL>'],
        ];
    }

    /**
     * Data provider for emails and expected auto-generation status
     */
    public function emailProvider(): array
    {
        return [
            // Auto-generated emails
            ['<EMAIL>', true],
            ['<EMAIL>', true],
            ['<EMAIL>', true],

            // Regular emails
            ['<EMAIL>', false],
            ['<EMAIL>', false],
            ['<EMAIL>', false],
            ['<EMAIL>', false],
            ['<EMAIL>', false],
            ['<EMAIL>', false],
            ['<EMAIL>', false],
        ];
    }
}
