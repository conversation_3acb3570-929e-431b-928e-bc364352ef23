<?php

namespace App\Tests\Service;

use App\Entity\OTPOrderVerification;
use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Entity\UserPointsTransaction;
use App\Repository\OTPOrderVerificationRepository;
use App\Service\OrderService;
use App\Service\OTPService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class OTPServiceTest extends TestCase
{
    private $entityManager;
    private $otpRepository;
    private $logger;
    private $orderService;
    private $otpService;
    private $customer;
    private $order;
    private $otpVerification;

    protected function setUp(): void
    {
        // Create mocks
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->otpRepository = $this->createMock(OTPOrderVerificationRepository::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->orderService = $this->createMock(OrderService::class);

        // Create OTPService with mocked dependencies
        $this->otpService = new OTPService(
            $this->entityManager,
            $this->otpRepository,
            $this->logger,
            $this->orderService
        );

        // Create test entities
        $this->customer = $this->createMock(SonataUserUser::class);
        $this->customer->method('getId')->willReturn(1);

        $this->order = $this->createMock(RewardsOrder::class);
        $this->order->method('getOrderNumber')->willReturn('ORD-123');

        $this->otpVerification = $this->createMock(OTPOrderVerification::class);
        $this->otpVerification->method('getCustomer')->willReturn($this->customer);
        $this->otpVerification->method('getRewardOrder')->willReturn($this->order);
        $this->otpVerification->method('getCode')->willReturn('123456');
    }

    public function testGenerateOTP(): void
    {
        // Test that the generated OTP is a 6-digit number
        $otp = $this->otpService->generateOTP();
        $this->assertMatchesRegularExpression('/^\d{6}$/', $otp);
    }

    public function testCreateOTPVerification(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('invalidatePreviousOTPs')
            ->with($this->order, $this->customer);

        $this->entityManager->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(OTPOrderVerification::class));

        $this->entityManager->expects($this->once())
            ->method('flush');

        // Call the method
        $result = $this->otpService->createOTPVerification($this->order, $this->customer);

        // Assert result
        $this->assertInstanceOf(OTPOrderVerification::class, $result);
    }

    public function testSendOTP(): void
    {
        // This test is limited because we can't easily mock the private sendSingleMessage method
        // We'll create a test-specific subclass to override the private methods

        // Create a test subclass that exposes the private method
        $testOTPService = new class($this->entityManager, $this->otpRepository, $this->logger, $this->orderService) extends OTPService {
            // Override the private method to avoid actual HTTP requests
            protected function sendSingleMessage(
                string $number,
                string $message,
                int $device = 3,
                ?string $schedule = null,
                bool $isMMS = false,
                ?string $attachments = null,
                bool $prioritize = false
            ): array {
                // Ignore parameters and return a mock response
                return ['id' => '123', 'status' => 'sent'];
            }

            protected function sendRequest(string $url, array $postData): array
            {
                // Ignore parameters and return a mock response
                return ['messages' => [['id' => '123', 'status' => 'sent']]];
            }
        };

        // Call the method
        $result = $testOTPService->sendOTP($this->otpVerification, '+1234567890');

        // Assert result
        $this->assertTrue($result);
    }

    public function testVerifyOTPSuccess(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('findActiveByOrderAndCustomer')
            ->with($this->order, $this->customer)
            ->willReturn($this->otpVerification);

        $this->otpVerification->expects($this->once())
            ->method('hasReachedMaxAttempts')
            ->willReturn(false);

        $this->otpVerification->expects($this->once())
            ->method('incrementAttempts');

        $this->otpVerification->expects($this->once())
            ->method('isExpired')
            ->willReturn(false);

        $this->otpVerification->expects($this->once())
            ->method('setVerifiedAt')
            ->with($this->isInstanceOf(\DateTimeImmutable::class));

        // Expect the OrderService to be called to confirm the order
        $this->orderService->expects($this->once())
            ->method('confirmOrder')
            ->with($this->order);

        // The flush method is called twice in the verifyOTP method:
        // 1. After incrementing attempts
        // 2. After setting verified_at
        $this->entityManager->expects($this->exactly(2))
            ->method('flush');

        // Call the method
        $result = $this->otpService->verifyOTP($this->order, $this->customer, '123456');

        // Assert result
        $this->assertTrue($result['success']);
        $this->assertEquals('OTP verified successfully', $result['message']);
    }

    public function testVerifyOTPInvalidCode(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('findActiveByOrderAndCustomer')
            ->with($this->order, $this->customer)
            ->willReturn($this->otpVerification);

        $this->otpVerification->expects($this->once())
            ->method('hasReachedMaxAttempts')
            ->willReturn(false);

        $this->otpVerification->expects($this->once())
            ->method('incrementAttempts');

        $this->entityManager->expects($this->once())
            ->method('flush');

        // Call the method with an invalid OTP
        $result = $this->otpService->verifyOTP($this->order, $this->customer, '654321');

        // Assert result
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid OTP code', $result['message']);
    }

    public function testVerifyOTPMaxAttemptsReached(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('findActiveByOrderAndCustomer')
            ->with($this->order, $this->customer)
            ->willReturn($this->otpVerification);

        $this->otpVerification->expects($this->once())
            ->method('hasReachedMaxAttempts')
            ->willReturn(true);

        // Call the method
        $result = $this->otpService->verifyOTP($this->order, $this->customer, '123456');

        // Assert result
        $this->assertFalse($result['success']);
        $this->assertEquals('Maximum verification attempts reached', $result['message']);
    }

    public function testVerifyOTPExpired(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('findActiveByOrderAndCustomer')
            ->with($this->order, $this->customer)
            ->willReturn($this->otpVerification);

        $this->otpVerification->expects($this->once())
            ->method('hasReachedMaxAttempts')
            ->willReturn(false);

        $this->otpVerification->expects($this->once())
            ->method('incrementAttempts');

        $this->otpVerification->expects($this->once())
            ->method('isExpired')
            ->willReturn(true);

        $this->entityManager->expects($this->once())
            ->method('flush');

        // Call the method
        $result = $this->otpService->verifyOTP($this->order, $this->customer, '123456');

        // Assert result
        $this->assertFalse($result['success']);
        $this->assertEquals('OTP has expired', $result['message']);
    }

    public function testVerifyOTPNoActiveOTP(): void
    {
        // Set up expectations
        $this->otpRepository->expects($this->once())
            ->method('findActiveByOrderAndCustomer')
            ->with($this->order, $this->customer)
            ->willReturn(null);

        // Call the method
        $result = $this->otpService->verifyOTP($this->order, $this->customer, '123456');

        // Assert result
        $this->assertFalse($result['success']);
        $this->assertEquals('No active OTP found for this order', $result['message']);
    }
}
