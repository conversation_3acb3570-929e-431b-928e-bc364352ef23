<?php

namespace App\Tests\Service;

use App\Service\PhoneValidationService;
use PHPUnit\Framework\TestCase;

class PhoneValidationServiceTest extends TestCase
{
    private PhoneValidationService $phoneValidationService;

    protected function setUp(): void
    {
        $this->phoneValidationService = new PhoneValidationService();
    }

    /**
     * @dataProvider validPhoneNumbersProvider
     */
    public function testValidateWithValidPhoneNumbers(string $phoneNumber): void
    {
        $result = $this->phoneValidationService->validate($phoneNumber);
        $this->assertTrue($result, "Phone number '$phoneNumber' should be valid");
        $this->assertNull($this->phoneValidationService->getErrorMessage());
    }

    /**
     * @dataProvider invalidPhoneNumbersProvider
     */
    public function testValidateWithInvalidPhoneNumbers(string $phoneNumber, string $expectedError): void
    {
        $result = $this->phoneValidationService->validate($phoneNumber);
        $this->assertFalse($result, "Phone number '$phoneNumber' should be invalid");
        $this->assertEquals($expectedError, $this->phoneValidationService->getErrorMessage());
    }

    /**
     * @dataProvider formatPhoneNumbersProvider
     */
    public function testFormat(string $phoneNumber, string $expected): void
    {
        $result = $this->phoneValidationService->format($phoneNumber);
        $this->assertEquals($expected, $result);
    }

    public function testSetDefaultCountryCode(): void
    {
        $this->phoneValidationService->setDefaultCountryCode('+1');
        $this->assertEquals('+1', $this->phoneValidationService->getDefaultCountryCode());

        // Test that formatting uses the new default country code
        $result = $this->phoneValidationService->format('5551234');
        $this->assertEquals('+15551234', $result);
    }

    public function testSetCountryPattern(): void
    {
        // Set a pattern for US numbers
        $this->phoneValidationService->setDefaultCountryCode('+1');
        $this->phoneValidationService->setCountryPattern('+1', '/^\+1[2-9]\d{9}$/');

        // Valid US number
        $this->assertTrue($this->phoneValidationService->validate('+12125551234'));

        // Invalid US number (too short)
        $this->assertFalse($this->phoneValidationService->validate('+1212555'));
    }

    /**
     * Data provider for valid phone numbers
     */
    public function validPhoneNumbersProvider(): array
    {
        return [
            ['5469 1234'],
            ['+230 5469 1234'],
            ['00230 546-91234'],
            ['+230-5469-1234'],
            ['+2305469 1234'],
            ['5469-1234'],
            ['(5469) 1234'],
            ['+230 5123 4567'],
            ['+230 6123 4567'],
            ['+230 7123 4567'],
            ['+230 8123 4567'],
            ['+230 9123 4567'],
        ];
    }

    /**
     * Data provider for invalid phone numbers
     */
    public function invalidPhoneNumbersProvider(): array
    {
        return [
            ['', 'Phone number cannot be empty'],
            ['+230 1234 5678', 'Invalid phone number format'], // Starts with 1, not valid for Mauritius
            ['+230 2345 6789', 'Invalid phone number format'], // Starts with 2, not valid for Mauritius
            ['+230 3456 7890', 'Invalid phone number format'], // Starts with 3, not valid for Mauritius
            ['+230 4567 8901', 'Invalid phone number format'], // Starts with 4, not valid for Mauritius
            ['+230 5123 45', 'Invalid phone number format'],   // Too short
            ['+230 5123 45678', 'Invalid phone number format'], // Too long
            ['****** 1234', 'Invalid country code'],           // Wrong country code
        ];
    }

    /**
     * Data provider for formatting phone numbers
     */
    public function formatPhoneNumbersProvider(): array
    {
        return [
            ['5469 1234', '+23054691234'],
            ['+230 5469 1234', '+23054691234'],
            ['00230 546-91234', '+23054691234'],
            ['+230-5469-1234', '+23054691234'],
            ['+2305469 1234', '+23054691234'],
            ['5469-1234', '+23054691234'],
            ['(5469) 1234', '+23054691234'],
            ['', ''],
        ];
    }
}
