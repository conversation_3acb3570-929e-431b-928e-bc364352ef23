<?php

namespace App\Tests\Service;

use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Entity\UserPointsTransaction;
use App\Service\OrderService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class OrderServiceTest extends TestCase
{
    private $entityManager;
    private $logger;
    private $orderService;
    private $customer;
    private $order;
    private $item;

    protected function setUp(): void
    {
        // Create mocks
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        // Create OrderService with mocked dependencies
        $this->orderService = new OrderService(
            $this->entityManager,
            $this->logger
        );

        // Create test entities
        $this->customer = $this->createMock(SonataUserUser::class);
        $this->customer->method('getId')->willReturn(1);

        $this->item = $this->createMock(RewardsItem::class);
        $this->item->method('getName')->willReturn('Test Item');
        $this->item->method('getPoints')->willReturn(100);

        $this->order = $this->createMock(RewardsOrder::class);
        $this->order->method('getOrderNumber')->willReturn('ORD-123');
        $this->order->method('getItem')->willReturn($this->item);
        $this->order->method('getUser')->willReturn($this->customer);
    }

    public function testConfirmOrder(): void
    {
        // Set up expectations
        $this->order->expects($this->once())
            ->method('setState')
            ->with(RewardsOrder::STATUS_DELIVERED);

        $this->entityManager->expects($this->once())
            ->method('flush');

        // Call the method
        $this->orderService->confirmOrder($this->order);
    }

    public function testHasPointsTransactionTrue(): void
    {
        // Mock the transaction repository
        $transactionRepository = $this->createMock(\Doctrine\ORM\EntityRepository::class);
        $transactionRepository->expects($this->once())
            ->method('findOneBy')
            ->with(['rewardsOrder' => $this->order])
            ->willReturn(new UserPointsTransaction());

        // Set up the entity manager to return our mock repository
        $this->entityManager->expects($this->once())
            ->method('getRepository')
            ->with(UserPointsTransaction::class)
            ->willReturn($transactionRepository);

        // Call the method
        $result = $this->orderService->hasPointsTransaction($this->order);

        // Assert result
        $this->assertTrue($result);
    }

    public function testHasPointsTransactionFalse(): void
    {
        // Mock the transaction repository
        $transactionRepository = $this->createMock(\Doctrine\ORM\EntityRepository::class);
        $transactionRepository->expects($this->once())
            ->method('findOneBy')
            ->with(['rewardsOrder' => $this->order])
            ->willReturn(null);

        // Set up the entity manager to return our mock repository
        $this->entityManager->expects($this->once())
            ->method('getRepository')
            ->with(UserPointsTransaction::class)
            ->willReturn($transactionRepository);

        // Call the method
        $result = $this->orderService->hasPointsTransaction($this->order);

        // Assert result
        $this->assertFalse($result);
    }

    public function testCreatePointsTransaction(): void
    {
        // Set up expectations
        $this->entityManager->expects($this->once())
            ->method('persist')
            ->with($this->callback(function ($entity) {
                return $entity instanceof UserPointsTransaction
                    && $entity->getType() === UserPointsTransaction::TYPE_SPEND
                    && $entity->getPoints() === 100
                    && $entity->getUser() === $this->customer
                    && $entity->getRewardsOrder() === $this->order;
            }));

        $this->entityManager->expects($this->once())
            ->method('flush');

        // Call the method
        $result = $this->orderService->createPointsTransaction($this->order, $this->customer);

        // Assert result
        $this->assertInstanceOf(UserPointsTransaction::class, $result);
        $this->assertEquals(UserPointsTransaction::TYPE_SPEND, $result->getType());
        $this->assertEquals(100, $result->getPoints());
        $this->assertEquals($this->customer, $result->getUser());
        $this->assertEquals($this->order, $result->getRewardsOrder());
    }
}
