<?php

namespace App\Tests\Controller;

use App\Entity\RegistrationOTP;
use App\Repository\RegistrationOTPRepository;
use App\Service\OTPService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class RegistrationOTPControllerTest extends WebTestCase
{
    private ?EntityManagerInterface $entityManager;
    private ?RegistrationOTPRepository $registrationOTPRepository;
    private ?OTPService $otpService;
    private ?KernelBrowser $client;

    protected function setUp(): void
    {
        // Create a client
        $this->client = static::createClient();

        // Get the entity manager
        $this->entityManager = static::getContainer()->get('doctrine')->getManager();
        $this->registrationOTPRepository = $this->entityManager->getRepository(RegistrationOTP::class);

        // Get the OTP service
        $this->otpService = static::getContainer()->get(OTPService::class);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Clean up any test data
        if ($this->entityManager) {
            $this->entityManager->close();
            $this->entityManager = null;
        }

        $this->registrationOTPRepository = null;
        $this->otpService = null;
        $this->client = null;
    }

    public function testRegister(): void
    {
        // Generate a unique phone number and email for this test
        $uniqueId = uniqid();
        $phoneNumber = '+230 5496' . rand(1000, 9999); // Format: +230 5496XXXX
        $email = 'user_' . $uniqueId . '@example.com';

        // Test with valid registration data
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register',
            [
                'mobilePhone' => $phoneNumber,
                'password' => 'securePassword123',
                'email' => $email,
                'firstName' => 'John',
                'lastName' => 'Doe'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('Content-Type', 'application/json');

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('registrationId', $responseData);
        $this->assertArrayHasKey('expiresIn', $responseData);
        $this->assertStringStartsWith('reg-', $responseData['registrationId']);
        $this->assertLessThanOrEqual(600, $responseData['expiresIn']);

        // Test with missing phone
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register',
            [
                'password' => 'securePassword123'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function testVerifyOTP(): void
    {
        // Generate a unique phone number and email for this test
        $uniqueId = uniqid();
        $phoneNumber = '+230 5496' . rand(1000, 9999); // Format: +230 5496XXXX
        $email = 'test_verify_' . $uniqueId . '@example.com';

        // First create a registration
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register',
            [
                'mobilePhone' => $phoneNumber,
                'password' => 'securePassword123',
                'email' => $email,
                'firstName' => 'Jane',
                'lastName' => 'Doe'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $registrationId = $responseData['registrationId'];

        // Get the OTP code from the database
        $registrationOTP = $this->registrationOTPRepository->findByRegistrationId($registrationId);
        $this->assertNotNull($registrationOTP, 'Registration OTP not found in database');
        $otpCode = $registrationOTP->getOtpCode();

        // Test with valid OTP verification data
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register_check',
            [
                'registrationId' => $registrationId,
                'code' => $otpCode,
                'password' => 'securePassword123'
            ]
        );

        // The verification logic works and creates a user successfully
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);

        // Verify that the response contains a token
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('token', $responseData);
        $this->assertArrayHasKey('refresh_token', $responseData);

        // Test with invalid OTP code
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register_check',
            [
                'registrationId' => $registrationId,
                'code' => '000000',
                'password' => 'securePassword123'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test with missing registration ID
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register_check',
            [
                'code' => $otpCode,
                'password' => 'securePassword123'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test with missing OTP code
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register_check',
            [
                'registrationId' => $registrationId,
                'password' => 'securePassword123'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test with missing password
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register_check',
            [
                'registrationId' => $registrationId,
                'code' => $otpCode
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function testResendOTP(): void
    {
        // Generate a unique phone number and email for this test
        $uniqueId = uniqid();
        $phoneNumber = '+230 5496' . rand(1000, 9999); // Format: +230 5496XXXX
        $email = 'test_resend_' . $uniqueId . '@example.com';

        // First create a registration
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register',
            [
                'mobilePhone' => $phoneNumber,
                'password' => 'securePassword123',
                'email' => $email,
                'firstName' => 'Bob',
                'lastName' => 'Smith'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $registrationId = $responseData['registrationId'];

        // Get the original OTP code
        $originalOTP = $this->registrationOTPRepository->findByRegistrationId($registrationId);
        $this->assertNotNull($originalOTP, 'Registration OTP not found in database');
        $originalOTPCode = $originalOTP->getOtpCode();

        // Test with valid resend OTP data
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register/resend',
            [
                'registrationId' => $registrationId
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        $this->assertEquals('', $this->client->getResponse()->getContent());

        // Verify that the OTP code has changed
        $this->entityManager->clear(); // Clear entity manager to get fresh data
        $updatedOTP = $this->registrationOTPRepository->findByRegistrationId($registrationId);
        $this->assertNotNull($updatedOTP, 'Updated Registration OTP not found in database');
        $updatedOTPCode = $updatedOTP->getOtpCode();

        $this->assertNotEquals($originalOTPCode, $updatedOTPCode, 'OTP code should have changed after resend');

        // Test with missing registration ID
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register/resend',
            []
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test with invalid registration ID
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register/resend',
            [
                'registrationId' => 'invalid-id'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function testSendOTPToSpecificPhoneNumber(): void
    {
        // Use the specific phone number as requested
        $phoneNumber = '+230 54961111';
        $password = 'securePassword123';

        // Test sending OTP to the specific phone number
        $this->client->jsonRequest(
            'POST',
            '/api/otp/register',
            [
                'mobilePhone' => $phoneNumber,
                'password' => $password,
                'firstName' => 'Test',
                'lastName' => 'User'
            ]
        );

        // Assert the response is successful
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('Content-Type', 'application/json');

        // Check the response data
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('registrationId', $responseData);
        $this->assertArrayHasKey('expiresIn', $responseData);
        $this->assertStringStartsWith('reg-', $responseData['registrationId']);
        $this->assertLessThanOrEqual(600, $responseData['expiresIn']);

        // Get the registration ID from the response
        $registrationId = $responseData['registrationId'];

        // Verify that an OTP was created in the database for this phone number
        $registrationOTP = $this->registrationOTPRepository->findByRegistrationId($registrationId);
        $this->assertNotNull($registrationOTP, 'Registration OTP not found in database');

        // Verify the phone number in the database matches the one we sent
        $tempUser = new \App\Entity\SonataUserUser();
        $tempUser->setMobilePhone($phoneNumber);
        $canonicalPhone = $tempUser->getMobilePhoneCanonical();

        $this->assertEquals(
            $canonicalPhone,
            $registrationOTP->getMobilePhoneCanonical(),
            'Phone number in database does not match the one sent in the request'
        );

        // Verify the OTP code was generated (6 digits)
        $otpCode = $registrationOTP->getOtpCode();
        $this->assertMatchesRegularExpression('/^\d{6}$/', $otpCode, 'OTP code should be 6 digits');
    }
}
