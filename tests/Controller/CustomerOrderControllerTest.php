<?php

namespace App\Tests\Controller;

use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerOrderControllerTest extends WebTestCase
{
    private $userManager;
    private $user;
    private $moderator;
    private $databaseTool;
    private $client;
    private $entityManager;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $container = static::getContainer();
        $this->userManager = $container->get('sonata.user.manager.user');
        $this->entityManager = $container->get('doctrine.orm.entity_manager');

        // Create a moderator user
        $this->moderator = new SonataUserUser();
        $this->moderator->setUsername('moderator');
        $this->moderator->setEmail('<EMAIL>');
        $this->moderator->setPlainPassword('moderator');
        $this->moderator->setEnabled(true);
        $this->moderator->addRole('ROLE_MODERATOR');

        // Create a regular customer
        $this->user = new SonataUserUser();
        $this->user->setUsername('customer');
        $this->user->setEmail('<EMAIL>');
        $this->user->setPlainPassword('customer');
        $this->user->setEnabled(true);
        $this->user->setMobilePhone('1234567890');
        $this->user->setPoints(1000);

        $this->userManager->save($this->user);
        $this->userManager->save($this->moderator);
    }

    private function createRewardsItemAndOrder(): RewardsOrder
    {
        // Create a rewards item
        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test Item');
        $rewardsItem->setPoints(100);
        $rewardsItem->setQuantity(10);
        $rewardsItem->setCreatedAt(new \DateTimeImmutable());

        $this->entityManager->persist($rewardsItem);

        // Create a pending order
        $order = new RewardsOrder();
        $order->setState(RewardsOrder::STATUS_PENDING);
        $order->setUser($this->user);
        $order->setItem($rewardsItem);
        $order->setCreatedAt(new \DateTimeImmutable());

        // We need to set these manually for testing since we're not using the lifecycle callback
        $order->setNumber('12345');
        $order->setOrderNumber('ORD-12345');

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return $order;
    }

    public function testGetPendingOrdersWithModeratorRole(): void
    {
        // Create test data
        $order = $this->createRewardsItemAndOrder();

        // Authenticate as moderator
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'moderator',
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $token = $data['token'];

        // Make request to the endpoint
        $this->client->request(
            Request::METHOD_GET,
            '/api/v1/customers/' . $this->user->getId() . '/pending-orders',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer ' . $token]
        );

        $this->assertResponseIsSuccessful();
        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        // Verify response structure
        $this->assertIsArray($responseData);
        $this->assertCount(1, $responseData);
        $this->assertArrayHasKey('orderNumber', $responseData[0]);
        $this->assertArrayHasKey('itemName', $responseData[0]);
        $this->assertArrayHasKey('pointCost', $responseData[0]);

        // Verify data
        $this->assertEquals('ORD-00001', $responseData[0]['orderNumber']);
        $this->assertEquals('Test Item', $responseData[0]['itemName']);
        $this->assertEquals(100, $responseData[0]['pointCost']);
    }

    public function testGetPendingOrdersWithNonExistentCustomer(): void
    {
        // Authenticate as moderator
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'moderator',
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $token = $data['token'];

        // Make request with non-existent customer ID
        $this->client->request(
            Request::METHOD_GET,
            '/api/v1/customers/999999/pending-orders',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer ' . $token]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEmpty($responseData);
    }

    public function testGetPendingOrdersWithoutModeratorRole(): void
    {
        // Authenticate as regular user
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'customer',
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $token = $data['token'];

        // Make request to the endpoint
        $this->client->request(
            Request::METHOD_GET,
            '/api/v1/customers/' . $this->user->getId() . '/pending-orders',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer ' . $token]
        );

        // Should be forbidden for non-moderator users
        $this->assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
