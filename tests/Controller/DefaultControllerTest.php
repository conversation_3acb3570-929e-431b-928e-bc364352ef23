<?php

namespace App\Tests\Controller;

use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataMediaMedia;
use App\Entity\SonataUserUser;
use App\Entity\Vehicle;
use App\Service\EmailGenerationService;
use Doctrine\ORM\EntityManager;
use Sonata\UserBundle\Document\UserManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use Sonata\MediaBundle\Model\MediaManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;

class DefaultControllerTest extends WebTestCase
{
    /**
     * @var AbstractDatabaseTool
     */
    private $databaseTool;

    /**
     * @var MediaManagerInterface
     */
    private $mediaManager;

    /**
     * @var SonataUserUser
     */
    private $admin;

    /**
     * @var SonataUserUser
     */
    private $user;

    /**
     * @var UserManager
     */
    private $userManager;

    /**
     * @var KernelBrowser
     */
    private $client;

    public function setUp(): void
    {
        parent::setUp();

        // Get services from container without creating a client
        $this->client = self::createClient();
        $container = self::getContainer();
        $this->mediaManager = $container->get(MediaManagerInterface::class);
        $this->databaseTool = $container->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $this->userManager = $container->get('sonata.user.manager.user');

        // Create test users
        $this->admin = new SonataUserUser();
        $this->admin->setUsername('admin');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);

        $this->user = new SonataUserUser();
        $this->user->setUsername('<EMAIL>');
        $this->user->setEmail('<EMAIL>');
        $this->user->setPlainPassword('usersecret');
        $this->user->setSuperAdmin(false);
        $this->user->setEnabled(true);
        $this->user->setMobilePhone('5496 1234');
        $this->user->addRole('ROLE_MODERATOR');

        $this->userManager->save($this->user);
        $this->userManager->save($this->admin);
    }

    public function testCgetRewardsItems(): void
    {
        $client = $this->createAuthenticatedClient();
        $imagePath = 'tests/fuel_meter_demo.jpg';
        $media = new SonataMediaMedia();
        $media->setBinaryContent(new File($imagePath));
        $media->setProviderName('sonata.media.provider.image');
        $media->setContext('default');
        $this->mediaManager->save($media);

        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test');
        $rewardsItem->setPoints(100);
        $rewardsItem->setFeaturedImage($media);
        $rewardsItem->setQuantity(50);

        /** @var EntityManager */
        $em = $this->getContainer()->get('doctrine.orm.entity_manager');
        $em->persist($rewardsItem);
        $em->flush();

        $this->assertNotNull($this->admin->getId());
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/v1/rewards-items',
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);
        $this->assertIsArray($data);
        $this->assertEquals(1, count($data));
        $this->assertEquals('Test', $data[0]['name']);
        $this->assertEquals(100, $data[0]['points']);
        $this->assertStringStartsWith('/uploads/media', $data[0]['featuredImage']);
    }

    public function testPostRewardsOrder(): void
    {
        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test');
        $rewardsItem->setPoints(100);
        $rewardsItem->setQuantity(50);

        $container = $this->getContainer();
        /** @var EntityManager */
        $em = $container->get('doctrine.orm.entity_manager');

        // Add points to the user to ensure they have enough to make the order
        $this->user->setPoints(100);
        $em->persist($this->user);
        $em->persist($rewardsItem);
        $em->flush();

        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/rewards-orders',
            [
                'itemId' => $rewardsItem->getId(),
            ]
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $jsonContent = json_decode($content, true);

        $order = $em->getRepository(RewardsOrder::class)
            ->findOneBy(['id' => $jsonContent['id']]);
        $this->assertNotNull($order);

        $this->assertEquals(RewardsOrder::STATUS_PENDING, $order->getState());
        $this->assertEquals("ORD-00001", $order->getOrderNumber());
    }

    public function testUpdateProfile()
    {
        // TODO: test if email already in used
        $requestContent = <<<JSON
{
    "mobilePhone": "5496 1234",
    "email": "<EMAIL>",
    "password": "",
    "first_name": "John",
    "last_name": "Doe",
    "dob": "1991-11-12",
    "address": "123, Main Street",
    "district": "Grand Port",
    "idCardNumber": "ID123456",
    "vehicleNumber": "ABC1234",
    "model": "Toyota",
    "fuel": "Petrol",
    "vehicleType": "Car"
}
JSON;

        $this->assertJson($requestContent);
        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_PUT,
            '/api/v1/users/' . $this->user->getId(),
            json_decode($requestContent, true)
        );
        $this->assertResponseIsSuccessful();

        /** @var SontatUserUser */
        $user = $this->userManager->find($this->user->getId());
        $this->assertEquals('5496 1234', $user->getMobilePhone());
        $this->assertEquals("<EMAIL>", $user->getEmail());
        $this->assertEquals("John", $user->getFirstName());
        $this->assertEquals("Doe", $user->getLastName());
        $this->assertEquals("123, Main Street", $user->getAddress());
        $this->assertEquals("Grand Port", $user->getDistrict());
        $this->assertEquals("ID123456", $user->getIdCardNumber());
        $this->assertNotNull($user->getDob());
        $this->assertEquals("1991-11-12", $user->getDob()->format('Y-m-d'));
        $vehicles = $user->getVehicles() ?? [];
        $this->assertGreaterThan(0, count($vehicles));
        /** @var Vehicle */
        $vehicle = $vehicles[0];
        $this->assertEquals("ABC1234", $vehicle->getLicensePlate());
        $this->assertEquals("Toyota", $vehicle->getModel());
        $this->assertEquals("Petrol", $vehicle->getFuel());
        $this->assertEquals("Car", $vehicle->getVehicleType());
    }

    public function testGetProfile()
    {
        $requestContent = <<<JSON
{
    "mobilePhone": "5496 1234",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "dob": "1991-11-12",
    "address": "123, Main Street",
    "district": "Grand Port",
    "idCardNumber": "ID123456",
    "vehicleNumber": "ABC1234",
    "model": "Toyota",
    "fuel": "Petrol",
    "vehicleType": "Car"
}
JSON;

        $userData = json_decode($requestContent, true);
        $this->user->setFromFlatArray($userData);
        $this->userManager->save($this->user);
        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/v1/users/' . $this->user->getId(),
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);
        foreach ($userData as $key => $value) {
            $this->assertEquals($value, $data[$key]);
        }
    }

    public function testSendOTP()
    {
        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/otp/login',
            [
                'phone' => '5496 1234',
            ]
        );

        $this->assertResponseIsSuccessful();
        /** @var SonataUserUser */
        $user = $this->userManager->find($this->user->getId());
        $this->assertNotEmpty($user->getOtp(), 'OTP is not empty');
    }

    public function testVerifyOTP()
    {
        $code = (string) random_int(1000, 9999);
        $this->user->setOtp($code);
        $this->userManager->save($this->user);
        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/otp/login_check',
            [
                'phone' => '+230 5496 1234',
                'code' => $code,
            ]
        );

        $this->assertResponseIsSuccessful();
    }

    public function testGetUserOrders(): void
    {
        // Create a rewards item
        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test Item');
        $rewardsItem->setPoints(100);
        $rewardsItem->setQuantity(50);

        // Create a media for the item
        $imagePath = 'tests/fuel_meter_demo.jpg';
        $media = new SonataMediaMedia();
        $media->setBinaryContent(new File($imagePath));
        $media->setProviderName('sonata.media.provider.image');
        $media->setContext('default');
        $this->mediaManager->save($media);
        $rewardsItem->setFeaturedImage($media);

        // Create an order for the user
        $order = new RewardsOrder();
        $order->setUser($this->user);
        $order->setItem($rewardsItem);
        $order->setState(RewardsOrder::STATUS_PENDING);
        $order->setCreatedAt(new \DateTimeImmutable());

        // Save to database
        $container = $this->getContainer();
        /** @var EntityManager */
        $em = $container->get('doctrine.orm.entity_manager');
        $em->persist($rewardsItem);
        $em->persist($order);
        $em->flush();

        // Test the endpoint
        $client = $this->createAuthenticatedClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/v1/users/' . $this->user->getId() . '/orders'
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Verify the response structure
        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $this->assertEquals($order->getId(), $data[0]['id']);
        $this->assertEquals($order->getState(), $data[0]['state']);
        $this->assertEquals('Test Item', $data[0]['item']['name']);
        $this->assertEquals(100, $data[0]['item']['points']);
        $this->assertStringStartsWith('/uploads/media', $data[0]['item']['featuredImage']);
    }

    private function createAuthenticatedClient()
    {
        // Create a new client for each test
        $client = $this->client;
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'usersecret',
            ]
        );

        $data = json_decode($client->getResponse()->getContent(), true);
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $data['token']));
        return $client;
    }

    public function testScanFuelMeter(): void
    {
        // Create a test image file
        $imagePath = 'tests/fuel_meter_demo.jpg';
        if (!file_exists($imagePath)) {
            throw new \RuntimeException('Test image file not found: ' . $imagePath);
        }

        // Create a temporary copy of the file
        $tempDir = sys_get_temp_dir();
        $tempFile = $tempDir . '/temp_' . uniqid() . '_fuel_meter_demo.jpg';
        copy($imagePath, $tempFile);

        // Create a client first
        $client = $this->client;

        // Create a mock for FuelMeterScanner that will be used in the controller
        $mockScanner = new class() extends \App\FuelMeterScanner {
            public function scanWithGemini(string $filePath, string $apiKey): array
            {
                return [
                    'amount' => 200.00,
                    'litres' => 3.27,
                    'unit_price' => 61.20,
                    'fuel_type' => 'gasoline'
                ];
            }
        };

        // Replace the service in the container
        static::getContainer()->set('App\\FuelMeterScanner', $mockScanner);

        // Authenticate the client
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'usersecret',
            ]
        );

        $data = json_decode($client->getResponse()->getContent(), true);
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $data['token']));

        // Create a file upload using the temporary file
        $file = new UploadedFile(
            $tempFile,
            'fuel_meter_demo.jpg',
            'image/jpeg',
            null,
            true
        );

        // Make a request to the endpoint
        $client->request(
            'POST',
            '/api/v1/fuel-meter/scan',
            [],
            ['file' => $file]
        );

        // Assert the response
        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the response contains the expected fields
        $this->assertArrayHasKey('amount', $data);
        $this->assertArrayHasKey('litres', $data);
        $this->assertArrayHasKey('unit_price', $data);
        $this->assertArrayHasKey('image', $data);
        $this->assertArrayHasKey('success', $data);

        // Check that the image path is returned
        $this->assertStringStartsWith('/uploads/fuel_meter/', $data['image']);

        // Check the values match our mock
        $this->assertEquals(200.00, $data['amount']);
        $this->assertEquals(3.27, $data['litres']);
        $this->assertEquals(61.20, $data['unit_price']);
        // $this->assertEquals('gasoline', $data['fuel_type']);
        $this->assertTrue($data['success']);
    }

    /**
     * Test registration with provided email
     */
    public function testRegistrationWithProvidedEmail(): void
    {
        $client = $this->client;

        // Test data with provided email
        $userData = [
            'firstName' => 'Test',
            'lastName' => 'User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'mobilePhone' => '5496 5678'
        ];

        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/register',
            $userData
        );

        $this->assertResponseStatusCodeSame(201); // Created
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the response contains the user ID
        $this->assertArrayHasKey('id', $data);

        // Verify the user was created with the provided email
        $userManager = $this->getContainer()->get('sonata.user.manager.user');
        $user = $userManager->findUserByEmail('<EMAIL>');
        $this->assertNotNull($user);
        $this->assertEquals('Test', $user->getFirstname());
        $this->assertEquals('User', $user->getLastname());
        $this->assertEquals('5496 5678', $user->getMobilePhone());
    }

    /**
     * Test registration with auto-generated email
     */
    public function testRegistrationWithAutoGeneratedEmail(): void
    {
        $client = $this->client;

        // Test data without email
        $userData = [
            'firstName' => 'Phone',
            'lastName' => 'User',
            'password' => 'password123',
            'mobilePhone' => '5496 9999'
        ];

        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/register',
            $userData
        );

        $this->assertResponseStatusCodeSame(201); // Created
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the response contains the user ID
        $this->assertArrayHasKey('id', $data);

        // Get the created user
        $userManager = $this->getContainer()->get('sonata.user.manager.user');
        $user = $userManager->find($data['id']);
        $this->assertNotNull($user);

        // Verify the user was created with an auto-generated email
        $this->assertEquals('Phone', $user->getFirstname());
        $this->assertEquals('User', $user->getLastname());
        $this->assertEquals('5496 9999', $user->getMobilePhone());

        // Check that the email was auto-generated in the expected format
        $emailGenerationService = $this->getContainer()->get(EmailGenerationService::class);
        $this->assertTrue($emailGenerationService->isAutoGenerated($user->getEmail()));
        $this->assertEquals(
            $emailGenerationService->generateFromPhone('5496 9999'),
            $user->getEmail()
        );
    }

    /**
     * Test registration with existing email
     */
    public function testRegistrationWithExistingEmail(): void
    {
        $client = $this->client;

        // First, create a user with a specific email
        $existingEmail = '<EMAIL>';
        $existingUser = new SonataUserUser();
        $existingUser->setUsername($existingEmail);
        $existingUser->setEmail($existingEmail);
        $existingUser->setPlainPassword('password123');
        $existingUser->setSuperAdmin(false);
        $existingUser->setEnabled(true);
        $existingUser->setMobilePhone('5496 1111');
        $existingUser->setFirstname('Existing');
        $existingUser->setLastname('User');

        $this->userManager->save($existingUser);

        // Now try to register a new user with a different phone number but the same email
        $userData = [
            'firstName' => 'New',
            'lastName' => 'User',
            'email' => $existingEmail, // Using the same email as the existing user
            'password' => 'password123',
            'mobilePhone' => '5496 2222' // Different phone number
        ];

        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/register',
            $userData
        );

        // Should get a 400 Bad Request response
        $this->assertResponseStatusCodeSame(400);
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the error message is correct
        $this->assertArrayHasKey('error', $data);
        $this->assertEquals('Email already exists.', $data['error']);
    }

    /**
     * Test profile endpoint excludes auto-generated emails
     */
    public function testProfileExcludesAutoGeneratedEmails(): void
    {
        // Create a user with an auto-generated email
        $emailGenerationService = $this->getContainer()->get(EmailGenerationService::class);
        $autoGeneratedEmail = $emailGenerationService->generateFromPhone('5496 7777');

        $userWithAutoEmail = new SonataUserUser();
        $userWithAutoEmail->setUsername($autoGeneratedEmail);
        $userWithAutoEmail->setEmail($autoGeneratedEmail);
        $userWithAutoEmail->setPlainPassword('password123');
        $userWithAutoEmail->setSuperAdmin(false);
        $userWithAutoEmail->setEnabled(true);
        $userWithAutoEmail->setMobilePhone('5496 7777');
        $userWithAutoEmail->setFirstname('Auto');
        $userWithAutoEmail->setLastname('Generated');

        $this->userManager->save($userWithAutoEmail);

        // Create a client and authenticate as this user
        $client = $this->client;
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => $autoGeneratedEmail,
                'password' => 'password123',
            ]
        );

        $authData = json_decode($client->getResponse()->getContent(), true);
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $authData['token']));

        // Get the profile
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/v1/users/' . $userWithAutoEmail->getId()
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the email field is included but empty for auto-generated emails
        $this->assertArrayHasKey('email', $data);
        $this->assertEquals('', $data['email']);

        // Now test with a user that has a regular email
        $userWithRegularEmail = new SonataUserUser();
        $regularEmail = '<EMAIL>';
        $userWithRegularEmail->setUsername($regularEmail);
        $userWithRegularEmail->setEmail($regularEmail);
        $userWithRegularEmail->setPlainPassword('password123');
        $userWithRegularEmail->setSuperAdmin(false);
        $userWithRegularEmail->setEnabled(true);
        $userWithRegularEmail->setMobilePhone('5496 8888');
        $userWithRegularEmail->setFirstname('Regular');
        $userWithRegularEmail->setLastname('User');

        $this->userManager->save($userWithRegularEmail);

        // Authenticate as the regular user
        $client = $this->client;
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => $regularEmail,
                'password' => 'password123',
            ]
        );

        $authData = json_decode($client->getResponse()->getContent(), true);
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $authData['token']));

        // Get the profile
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/v1/users/' . $userWithRegularEmail->getId()
        );

        $this->assertResponseIsSuccessful();
        $content = $client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the email field is included in the response
        $this->assertArrayHasKey('email', $data);
        $this->assertEquals($regularEmail, $data['email']);
    }
}
