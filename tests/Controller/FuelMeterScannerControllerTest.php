<?php

namespace App\Tests\Controller;

use App\Entity\SonataUserUser;
use <PERSON><PERSON>\TestFixturesBundle\Services\DatabaseToolCollection;
use <PERSON><PERSON>\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use Sonata\UserBundle\Document\UserManager;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;

class FuelMeterScannerControllerTest extends WebTestCase
{
    /**
     * @var AbstractDatabaseTool
     */
    private $databaseTool;

    /**
     * @var SonataUserUser
     */
    private $user;

    /**
     * @var UserManager
     */
    private $userManager;

    /**
     * @var KernelBrowser
     */
    private $client;

    public function setUp(): void
    {
        parent::setUp();

        // Create a client and disable reboot to maintain the container between tests
        $this->client = self::createClient();
        $this->client->disableReboot();

        // Get services from container
        $container = self::getContainer();
        $this->databaseTool = $container->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $this->userManager = $container->get('sonata.user.manager.user');

        // Create test user
        $this->user = new SonataUserUser();
        $this->user->setUsername('<EMAIL>');
        $this->user->setEmail('<EMAIL>');
        $this->user->setPlainPassword('usersecret');
        $this->user->setSuperAdmin(false);
        $this->user->setEnabled(true);
        $this->user->setMobilePhone('5496 1234');
        $this->user->addRole('ROLE_MODERATOR');

        $this->userManager->save($this->user);

        // Authenticate the client once for all tests
        $this->authenticateClient();
    }

    private function authenticateClient()
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'usersecret',
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $this->client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $data['token']));
    }

    public function testScanFuelMeterWithError(): void
    {
        // Create a test image file
        $imagePath = 'tests/fuel_meter_demo.jpg';
        if (!file_exists($imagePath)) {
            throw new \RuntimeException('Test image file not found: ' . $imagePath);
        }

        $tempDir = sys_get_temp_dir();
        $tempFile = $tempDir . '/temp_' . uniqid() . '_fuel_meter_demo.jpg';
        copy($imagePath, $tempFile);

        // Create a mock for FuelMeterScanner that will throw an exception
        $mockScanner = new class() extends \App\FuelMeterScanner {
            public function scanWithGemini(string $filePath, string $apiKey): array
            {
                throw new \Exception('Failed to extract data from image', 1004);
            }
        };

        // Replace the service in the container
        static::getContainer()->set('App\\FuelMeterScanner', $mockScanner);

        // Create a file upload
        $file = new UploadedFile(
            $tempFile,
            'fuel_meter_demo.jpg',
            'image/jpeg',
            null,
            true
        );

        // Make a request to the endpoint
        $this->client->request(
            'POST',
            '/api/v1/fuel-meter/scan',
            [],
            ['file' => $file]
        );

        // Assert the response is still HTTP 200
        $this->assertResponseIsSuccessful();
        $content = $this->client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check that the response contains the expected fields for an error
        $this->assertArrayHasKey('amount', $data);
        $this->assertArrayHasKey('litres', $data);
        $this->assertArrayHasKey('unit_price', $data);
        $this->assertArrayHasKey('image', $data);
        $this->assertArrayHasKey('success', $data);
        $this->assertArrayHasKey('error_code', $data);
        $this->assertArrayHasKey('error_details', $data);

        // Check that the image path is returned
        $this->assertStringStartsWith('/uploads/fuel_meter/', $data['image']);

        // Check the values are zeros
        $this->assertEquals(0, $data['amount']);
        $this->assertEquals(0, $data['litres']);
        $this->assertEquals(0, $data['unit_price']);
        $this->assertEquals('unknown', $data['fuel_type']);
        $this->assertFalse($data['success']);
        $this->assertEquals('SCAN_FAILED', $data['error_code']);
        $this->assertEquals('processing', $data['error_details']['type']);
    }

    public function testScanFuelMeterWithApiError(): void
    {
        // Create a test image file
        $imagePath = 'tests/fuel_meter_demo.jpg';
        if (!file_exists($imagePath)) {
            throw new \RuntimeException('Test image file not found: ' . $imagePath);
        }
        $tempDir = sys_get_temp_dir();
        $tempFile = $tempDir . '/temp_' . uniqid() . '_fuel_meter_demo.jpg';
        copy($imagePath, $tempFile);

        // Create a mock for FuelMeterScanner that will throw an API exception
        $mockScanner = new class() extends \App\FuelMeterScanner {
            public function scanWithGemini(string $filePath, string $apiKey): array
            {
                throw new \Exception('API request timed out', 2003);
            }
        };

        // Replace the service in the container
        static::getContainer()->set('App\\FuelMeterScanner', $mockScanner);

        // Create a file upload
        $file = new UploadedFile(
            $tempFile,
            'fuel_meter_demo.jpg',
            'image/jpeg',
            null,
            true
        );

        // Make a request to the endpoint
        $this->client->request(
            'POST',
            '/api/v1/fuel-meter/scan',
            [],
            ['file' => $file]
        );

        // Assert the response is still HTTP 200
        $this->assertResponseIsSuccessful();
        $content = $this->client->getResponse()->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);

        // Check the error code and type
        $this->assertEquals('API_ERROR', $data['error_code']);
        $this->assertEquals('api', $data['error_details']['type']);
    }
}
