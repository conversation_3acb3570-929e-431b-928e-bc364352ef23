<?php

namespace App\Tests\Controller;

use App\Entity\OTPOrderVerification;
use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use App\Service\OrderService;
use App\Service\OTPService;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerControllerTest extends WebTestCase
{
    private $userManager;
    private $user;
    private $admin;
    private $databaseTool;
    private $client;
    private $entityManager;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $container = static::getContainer();
        $this->userManager = $container->get('sonata.user.manager.user');
        $this->entityManager = $container->get('doctrine.orm.entity_manager');

        $this->admin = new SonataUserUser();
        $this->admin->setUsername('admin');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);

        $this->user = new SonataUserUser();
        $this->user->setUsername('<EMAIL>');
        $this->user->setEmail('<EMAIL>');
        $this->user->setPlainPassword('usersecret');
        $this->user->setSuperAdmin(false);
        $this->user->setEnabled(true);
        $this->user->setMobilePhone('5496 1234');
        // Use the sanitizePhoneNumber method to ensure consistency
        $this->user->setMobilePhoneCanonical(SonataUserUser::sanitizePhoneNumber('5496 1234'));

        $this->userManager->save($this->user);
        $this->userManager->save($this->admin);

        // Create test data for pending orders
        $this->createTestPendingOrder();

        // Authenticate as admin
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'admin',
            ]
        );

        if ($this->client->getResponse()->getStatusCode() !== Response::HTTP_OK) {
            throw new \Exception('Failed to authenticate as admin');
        }

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $this->client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $data['token']));
    }

    public function testSearchCustomerByPhone(): void
    {
        // Test with valid phone number
        $this->client->request(
            'GET',
            '/api/v1/customers/search?phone=5496 1234'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('id', $responseData);
        $this->assertEquals($this->user->getId(), $responseData['id']);

        // Test without phone number
        $this->client->request(
            'GET',
            '/api/v1/customers/search'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test with non-existent phone number
        $this->client->request(
            'GET',
            '/api/v1/customers/search?phone=9999 9999'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Customer not found', $responseData['error']);
    }

    /**
     * Helper method to create a test pending order for the user
     *
     * @return RewardsOrder The created order
     */
    private function createTestPendingOrder(): RewardsOrder
    {
        // Create a rewards item
        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test Item');
        $rewardsItem->setPoints(100);
        $rewardsItem->setQuantity(10);
        $rewardsItem->setCreatedAt(new \DateTimeImmutable());
        $this->entityManager->persist($rewardsItem);

        // Create a pending order
        $order = new RewardsOrder();
        $order->setState(RewardsOrder::STATUS_PENDING);
        $order->setUser($this->user);
        $order->setItem($rewardsItem);
        $order->setCreatedAt(new \DateTimeImmutable());

        // We need to set these manually for testing since we're not using the lifecycle callback
        $order->setNumber('12345');
        $order->setOrderNumber('ORD-12345');

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return $order;
    }

    public function testGetPendingOrdersWithValidCustomerId(): void
    {
        // Test with valid customer ID - use the user ID from the test setup
        $this->client->request(
            'GET',
            '/api/v1/customers/' . $this->user->getId() . '/pending-orders'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        // Verify response structure
        $this->assertIsArray($responseData);
        $this->assertNotEmpty($responseData);
        $this->assertArrayHasKey('orderNumber', $responseData[0]);
        $this->assertArrayHasKey('itemName', $responseData[0]);
        $this->assertArrayHasKey('pointCost', $responseData[0]);

        // Verify data types
        $this->assertIsString($responseData[0]['orderNumber']);
        $this->assertIsString($responseData[0]['itemName']);
        $this->assertIsInt($responseData[0]['pointCost']);
    }

    public function testGetPendingOrdersWithInvalidCustomerId(): void
    {
        // Test with invalid customer ID
        $this->client->request(
            'GET',
            '/api/v1/customers/0/pending-orders'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertEmpty($responseData);
    }

    public function testGetPendingOrdersWithoutAuthentication(): void
    {
        // Save the current authorization header
        $authHeader = $this->client->getServerParameter('HTTP_Authorization');

        // Remove the authorization header
        $this->client->setServerParameter('HTTP_Authorization', '');

        // Test without authentication
        $this->client->request(
            'GET',
            '/api/v1/customers/1/pending-orders'
        );

        // Should return 401 Unauthorized
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());

        // Restore the authorization header for other tests
        $this->client->setServerParameter('HTTP_Authorization', $authHeader);
    }

    public function testGetCustomerPointsWithValidCustomerId(): void
    {
        // Set a specific points value for testing
        $this->user->setPoints(500);
        $this->entityManager->flush();

        // Test with valid customer ID - use the user ID from the test setup
        $this->client->request(
            'GET',
            '/api/v1/customers/' . $this->user->getId() . '/points'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        // Verify response structure
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('availablePoints', $responseData);

        // Verify data type and value
        $this->assertIsInt($responseData['availablePoints']);
        $this->assertEquals(500, $responseData['availablePoints']);
    }

    public function testGetCustomerPointsWithInvalidCustomerId(): void
    {
        // Test with invalid customer ID
        $this->client->request(
            'GET',
            '/api/v1/customers/0/points'
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetCustomerPointsWithoutAuthentication(): void
    {
        // Save the current authorization header
        $authHeader = $this->client->getServerParameter('HTTP_Authorization');

        // Remove the authorization header
        $this->client->setServerParameter('HTTP_Authorization', '');

        // Test without authentication
        $this->client->request(
            'GET',
            '/api/v1/customers/1/points'
        );

        // Should return 401 Unauthorized
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());

        // Restore the authorization header for other tests
        $this->client->setServerParameter('HTTP_Authorization', $authHeader);
    }

    public function testGenerateOtpWithValidData(): void
    {
        // Create a test order
        $order = $this->createTestPendingOrder();

        // Create a test OTPService that doesn't actually send SMS
        $orderService = $this->getContainer()->get(OrderService::class);
        $testOTPService = new class($this->entityManager, $this->entityManager->getRepository(OTPOrderVerification::class), $this->getContainer()->get('logger'), $orderService) extends OTPService {
            protected function sendSingleMessage(
                string $number,
                string $message,
                int $device = 3,
                ?string $schedule = null,
                bool $isMMS = false,
                ?string $attachments = null,
                bool $prioritize = false
            ): array {
                // Return a mock response instead of sending a real SMS
                return ['id' => '123', 'status' => 'sent'];
            }

            protected function sendRequest(string $url, array $postData): array
            {
                // Return a mock response instead of making a real HTTP request
                return ['messages' => [['id' => '123', 'status' => 'sent']]];
            }
        };

        // Replace the OTPService in the container with our test service
        self::getContainer()->set(OTPService::class, $testOTPService);

        // Test with valid data
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/generate-otp',
            [
                'phoneNumber' => '5496 1234',
                'orderId' => $order->getOrderNumber()
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Verify that an OTP verification entry was created in the database
        $otpVerification = $this->entityManager->getRepository(OTPOrderVerification::class)
            ->findByOrderAndCustomer($order, $this->user);

        $this->assertNotNull($otpVerification, 'OTP verification entry should be created');
        $this->assertMatchesRegularExpression('/^\d{6}$/', $otpVerification->getCode(), 'OTP code should be a 6-digit number');
        $this->assertEquals($this->user->getId(), $otpVerification->getCustomer()->getId(), 'Customer ID should match');
        $this->assertEquals($order->getId(), $otpVerification->getRewardOrder()->getId(), 'Order ID should match');
        $this->assertNull($otpVerification->getVerifiedAt(), 'OTP should not be verified yet');
        $this->assertEquals(0, $otpVerification->getAttempts(), 'Attempts should be 0');
    }

    public function testGenerateOtpInvalidatesPreviousOTPs(): void
    {
        // Create a test order
        $order = $this->createTestPendingOrder();

        // Create a first OTP verification
        $otpVerification1 = new OTPOrderVerification();
        $otpVerification1->setRewardOrder($order);
        $otpVerification1->setCustomer($this->user);
        $otpVerification1->setCode('123456');
        $this->entityManager->persist($otpVerification1);
        $this->entityManager->flush();

        // Store the ID for later lookup
        $otpId = $otpVerification1->getId();

        // Verify the first OTP is active
        $this->assertFalse($otpVerification1->isExpired(), 'First OTP should not be expired');

        // Create a test OTPService that doesn't actually send SMS
        $orderService = $this->getContainer()->get(OrderService::class);
        $testOTPService = new class($this->entityManager, $this->entityManager->getRepository(OTPOrderVerification::class), $this->getContainer()->get('logger'), $orderService) extends OTPService {
            protected function sendSingleMessage(
                string $number,
                string $message,
                int $device = 3,
                ?string $schedule = null,
                bool $isMMS = false,
                ?string $attachments = null,
                bool $prioritize = false
            ): array {
                // Return a mock response instead of sending a real SMS
                return ['id' => '123', 'status' => 'sent'];
            }

            protected function sendRequest(string $url, array $postData): array
            {
                // Return a mock response instead of making a real HTTP request
                return ['messages' => [['id' => '123', 'status' => 'sent']]];
            }
        };

        // Replace the OTPService in the container with our test service
        self::getContainer()->set(OTPService::class, $testOTPService);

        // Generate a new OTP for the same order and customer
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/generate-otp',
            [
                'phoneNumber' => '5496 1234',
                'orderId' => $order->getOrderNumber()
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Clear the entity manager to force reload from database
        $this->entityManager->clear();

        // Fetch the first OTP again from the database
        $otpVerification1 = $this->entityManager->getRepository(OTPOrderVerification::class)->find($otpId);

        // Verify the first OTP is now expired
        $this->assertTrue($otpVerification1->isExpired(), 'First OTP should be expired after generating a new one');

        // Verify a new OTP was created
        $otpVerification2 = $this->entityManager->getRepository(OTPOrderVerification::class)
            ->findByOrderAndCustomer($order, $this->user);

        $this->assertNotNull($otpVerification2, 'New OTP verification entry should be created');
        $this->assertMatchesRegularExpression('/^\d{6}$/', $otpVerification2->getCode(), 'New OTP code should be a 6-digit number');
    }

    public function testGenerateOtpWithInvalidCustomerId(): void
    {
        // Test with invalid customer ID
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/0/generate-otp',
            [
                'phoneNumber' => '5496 1234',
                'orderId' => 'ORD-12345'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testGenerateOtpWithoutRequiredFields(): void
    {
        // Test without required fields
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/1/generate-otp',
            [
                'phoneNumber' => '5496 1234'
                // Missing orderId
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function testGenerateOtpWithoutAuthentication(): void
    {
        // Save the current authorization header
        $authHeader = $this->client->getServerParameter('HTTP_Authorization');

        // Remove the authorization header
        $this->client->setServerParameter('HTTP_Authorization', '');

        // Test without authentication
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/1/generate-otp',
            [
                'phoneNumber' => '5496 1234',
                'orderId' => 'ORD-12345'
            ]
        );

        // Should return 401 Unauthorized
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());

        // Restore the authorization header for other tests
        $this->client->setServerParameter('HTTP_Authorization', $authHeader);
    }

    public function testVerifyOtpWithValidData(): void
    {
        // Create a test order
        $order = $this->createTestPendingOrder();

        // Create an OTP verification
        $otpVerification = new OTPOrderVerification();
        $otpVerification->setRewardOrder($order);
        $otpVerification->setCustomer($this->user);
        $otpVerification->setCode('123456');
        $this->entityManager->persist($otpVerification);
        $this->entityManager->flush();

        // Create a test OTPService that doesn't actually send SMS
        $orderService = $this->getContainer()->get(OrderService::class);
        $testOTPService = new class($this->entityManager, $this->entityManager->getRepository(OTPOrderVerification::class), $this->getContainer()->get('logger'), $orderService) extends OTPService {
            // Override the private methods to avoid actual HTTP requests
            protected function sendSingleMessage($number, $message, $device = 3, $schedule = null, $isMMS = false, $attachments = null, $prioritize = false): array
            {
                return ['id' => '123', 'status' => 'sent'];
            }

            protected function sendRequest($url, $postData): array
            {
                return ['messages' => [['id' => '123', 'status' => 'sent']]];
            }
        };

        // Replace the OTPService in the container with our test service
        self::getContainer()->set(OTPService::class, $testOTPService);

        // Test with valid data
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/verify-otp',
            [
                'orderId' => $order->getOrderNumber(),
                'otp' => '123456'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Clear the entity manager to force reload from database
        $this->entityManager->clear();

        // Verify that the OTP is marked as verified
        $verifiedOtp = $this->entityManager->getRepository(OTPOrderVerification::class)
            ->findByOrderAndCustomer($order, $this->user);

        $this->assertNotNull($verifiedOtp, 'OTP verification entry should exist');
        $this->assertNotNull($verifiedOtp->getVerifiedAt(), 'OTP should be marked as verified');

        // Verify that the order status is updated to delivered
        $updatedOrder = $this->entityManager->getRepository(RewardsOrder::class)->find($order->getId());
        $this->assertEquals(
            RewardsOrder::STATUS_DELIVERED,
            $updatedOrder->getState(),
            'Order status should be updated to delivered'
        );

        // Verify that a points transaction was created
        $pointsTransaction = $this->entityManager->getRepository('App\Entity\UserPointsTransaction')
            ->findOneBy(['rewardsOrder' => $order]);

        $this->assertNotNull($pointsTransaction, 'Points transaction should be created');
        $this->assertEquals('spend', $pointsTransaction->getType(), 'Transaction type should be spend');
        $this->assertEquals($order->getItem()->getPoints(), $pointsTransaction->getPoints(), 'Transaction points should match item points');
        $this->assertEquals($this->user->getId(), $pointsTransaction->getUser()->getId(), 'User ID should match');
    }

    public function testVerifyOtpWithInvalidOtpFormat(): void
    {
        // Create a test order
        $order = $this->createTestPendingOrder();

        // Create an OTP verification
        $otpVerification = new OTPOrderVerification();
        $otpVerification->setRewardOrder($order);
        $otpVerification->setCustomer($this->user);
        $otpVerification->setCode('123456');
        $this->entityManager->persist($otpVerification);
        $this->entityManager->flush();

        // Test with invalid OTP format (not 6 digits)
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/verify-otp',
            [
                'orderId' => $order->getOrderNumber(),
                'otp' => '12345' // Only 5 digits
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Invalid OTP format', $responseData['error']);
    }

    public function testVerifyOtpWithInvalidCustomerId(): void
    {
        // Test with invalid customer ID
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/999999/verify-otp',
            [
                'orderId' => 'ORD-12345',
                'otp' => '123456'
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Customer not found', $responseData['error']);
    }

    public function testVerifyOtpWithoutRequiredFields(): void
    {
        // Test without required fields
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/verify-otp',
            [
                'orderId' => 'ORD-12345'
                // Missing otp
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Order ID and OTP are required', $responseData['error']);
    }

    public function testVerifyOtpWithInvalidOtpCode(): void
    {
        // Create a test order
        $order = $this->createTestPendingOrder();

        // Create an OTP verification
        $otpVerification = new OTPOrderVerification();
        $otpVerification->setRewardOrder($order);
        $otpVerification->setCustomer($this->user);
        $otpVerification->setCode('123456');
        $this->entityManager->persist($otpVerification);
        $this->entityManager->flush();

        // Create a test OTPService that returns a specific error for invalid OTP
        $orderService = $this->getContainer()->get(OrderService::class);
        $testOTPService = new class($this->entityManager, $this->entityManager->getRepository(OTPOrderVerification::class), $this->getContainer()->get('logger'), $orderService) extends OTPService {
            // Override the verifyOTP method to return a specific error
            public function verifyOTP(RewardsOrder $order, SonataUserUser $customer, string $otpCode): array
            {
                if ($otpCode !== '123456') {
                    return [
                        'success' => false,
                        'message' => 'Invalid OTP code'
                    ];
                }

                return parent::verifyOTP($order, $customer, $otpCode);
            }

            // Override the private methods to avoid actual HTTP requests
            protected function sendSingleMessage($number, $message, $device = 3, $schedule = null, $isMMS = false, $attachments = null, $prioritize = false): array
            {
                return ['id' => '123', 'status' => 'sent'];
            }

            protected function sendRequest($url, $postData): array
            {
                return ['messages' => [['id' => '123', 'status' => 'sent']]];
            }
        };

        // Replace the OTPService in the container with our test service
        self::getContainer()->set(OTPService::class, $testOTPService);

        // Test with invalid OTP code
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/' . $this->user->getId() . '/verify-otp',
            [
                'orderId' => $order->getOrderNumber(),
                'otp' => '654321' // Different from the stored OTP
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Invalid OTP code', $responseData['error']);
    }

    public function testVerifyOtpWithoutAuthentication(): void
    {
        // Save the current authorization header
        $authHeader = $this->client->getServerParameter('HTTP_Authorization');

        // Remove the authorization header
        $this->client->setServerParameter('HTTP_Authorization', '');

        // Test without authentication
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/customers/1/verify-otp',
            [
                'orderId' => 'ORD-12345',
                'otp' => '123456'
            ]
        );

        // Should return 401 Unauthorized
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());

        // Restore the authorization header for other tests
        $this->client->setServerParameter('HTTP_Authorization', $authHeader);
    }
}
