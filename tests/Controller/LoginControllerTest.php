<?php

namespace App\Tests\Controller;

use App\Entity\SonataUserUser;
use Doctrine\ORM\EntityManagerInterface; // Added
use Sonata\UserBundle\Model\UserManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class LoginControllerTest extends WebTestCase
{
    private ?UserManagerInterface $userManager = null;
    private ?KernelBrowser $client = null;
    private ?EntityManagerInterface $entityManager = null; // Added

    private ?SonataUserUser $emailUser = null;
    private string $emailUserPassword = 'passwordEmail123';

    private ?SonataUserUser $phoneUser = null;
    private string $phoneUserPassword = 'passwordPhone456';

    private ?SonataUserUser $emailAndPhoneUser = null;
    private string $emailAndPhoneUserPassword = 'passwordBoth789';

    private ?SonataUserUser $disabledUser = null;
    private string $disabledUserPassword = 'passwordDisabled000';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
        $container = self::getContainer();
        $this->userManager = $container->get('sonata.user.manager.user');
        $this->entityManager = $container->get('doctrine.orm.entity_manager'); // Added

        // Clean up any existing test users from previous runs if necessary
        $this->removeTestUserByUsername('test_email_login');
        $this->removeTestUserByUsername('test_phone_login');
        $this->removeTestUserByUsername('test_both_login');
        $this->removeTestUserByUsername('test_disabled_login');

        // Create Email User
        $this->emailUser = new SonataUserUser(); // Changed from $this->userManager->createUser()
        $this->emailUser->setUsername('test_email_login');
        $this->emailUser->setEmail('<EMAIL>');
        // Set a unique mobile phone to avoid unique constraint violation
        $this->emailUser->setMobilePhone('555111000');
        $this->emailUser->setPlainPassword($this->emailUserPassword);
        $this->emailUser->setEnabled(true);
        $this->userManager->save($this->emailUser);

        // Create Phone User
        $this->phoneUser = new SonataUserUser(); // Changed from $this->userManager->createUser()
        $this->phoneUser->setUsername('test_phone_login');
        $this->phoneUser->setEmail('<EMAIL>'); // Dummy email
        $this->phoneUser->setMobilePhone('555000111');
        // setMobilePhone should trigger sanitizePhoneNumber and set mobilePhoneCanonical
        $this->phoneUser->setPlainPassword($this->phoneUserPassword);
        $this->phoneUser->setEnabled(true);
        $this->userManager->save($this->phoneUser);

        // Create Email and Phone User
        $this->emailAndPhoneUser = new SonataUserUser(); // Changed from $this->userManager->createUser()
        $this->emailAndPhoneUser->setUsername('test_both_login');
        $this->emailAndPhoneUser->setEmail('<EMAIL>');
        $this->emailAndPhoneUser->setMobilePhone('555000222');
        $this->emailAndPhoneUser->setPlainPassword($this->emailAndPhoneUserPassword);
        $this->emailAndPhoneUser->setEnabled(true);
        $this->userManager->save($this->emailAndPhoneUser);

        // Create Disabled User
        $this->disabledUser = new SonataUserUser(); // Changed from $this->userManager->createUser()
        $this->disabledUser->setUsername('test_disabled_login');
        $this->disabledUser->setEmail('<EMAIL>');
        // Set a unique mobile phone to avoid unique constraint violation
        $this->disabledUser->setMobilePhone('555333000');
        $this->disabledUser->setPlainPassword($this->disabledUserPassword);
        $this->disabledUser->setEnabled(false);
        $this->userManager->save($this->disabledUser);
    }

    private function removeTestUserByUsername(string $username): void
    {
        $user = $this->userManager->findUserByUsername($username);
        if ($user) {
            // Changed from $this->userManager->deleteUser($user);
            $this->entityManager->remove($user);
            $this->entityManager->flush();
        }
    }

    protected function tearDown(): void
    {
        $usersToDelete = [];
        if ($this->emailUser && $this->emailUser->getId()) {
            $user = $this->entityManager->find(SonataUserUser::class, $this->emailUser->getId());
            if ($user) $usersToDelete[] = $user;
        }
        if ($this->phoneUser && $this->phoneUser->getId()) {
            $user = $this->entityManager->find(SonataUserUser::class, $this->phoneUser->getId());
            if ($user) $usersToDelete[] = $user;
        }
        if ($this->emailAndPhoneUser && $this->emailAndPhoneUser->getId()) {
            $user = $this->entityManager->find(SonataUserUser::class, $this->emailAndPhoneUser->getId());
            if ($user) $usersToDelete[] = $user;
        }
        if ($this->disabledUser && $this->disabledUser->getId()) {
            $user = $this->entityManager->find(SonataUserUser::class, $this->disabledUser->getId());
            if ($user) $usersToDelete[] = $user;
        }

        foreach ($usersToDelete as $user) {
            $this->entityManager->remove($user);
        }

        if (count($usersToDelete) > 0) {
            $this->entityManager->flush();
        }

        parent::tearDown();
        $this->userManager = null;
        $this->client = null;
        $this->entityManager = null; // Added
        $this->emailUser = null;
        $this->phoneUser = null;
        $this->emailAndPhoneUser = null;
        $this->disabledUser = null;
    }

    private function assertSuccessfulLoginResponse(Response $response): void
    {
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $content = $response->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);
        $this->assertArrayHasKey('token', $data);
        $this->assertArrayHasKey('refresh_token', $data);
    }

    private function assertFailedLoginResponse(Response $response, int $expectedStatusCode = Response::HTTP_UNAUTHORIZED): void
    {
        $this->assertResponseStatusCodeSame($expectedStatusCode);
        $content = $response->getContent();
        $this->assertJson($content);
        $data = json_decode($content, true);
        $this->assertArrayHasKey('code', $data);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals($expectedStatusCode, $data['code']);
        // $this->assertEquals('Invalid credentials.', $data['message']); // Message can vary
    }

    public function testLoginWithValidEmailAndPasswordSucceeds(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>',
            'password' => $this->emailUserPassword,
        ]);
        $this->assertSuccessfulLoginResponse($this->client->getResponse());
    }

    public function testLoginWithValidEmailAndWrongPasswordFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>',
            'password' => 'wrongPassword',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithNonExistentEmailFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>',
            'password' => 'anypassword',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithValidPhoneNumberAndPasswordSucceeds(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '555000111', // Using phone number as identifier
            'password' => $this->phoneUserPassword,
        ]);
        $this->assertSuccessfulLoginResponse($this->client->getResponse());
    }

    public function testLoginWithValidPhoneNumberAndWrongPasswordFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '555000111',
            'password' => 'wrongPassword',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithNonExistentPhoneNumberFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '555999999',
            'password' => 'anypassword',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithEmailForUserWithBothEmailAndPhoneSucceeds(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>',
            'password' => $this->emailAndPhoneUserPassword,
        ]);
        $this->assertSuccessfulLoginResponse($this->client->getResponse());
    }

    public function testLoginWithPhoneNumberForUserWithBothEmailAndPhoneSucceeds(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '555000222', // Using phone number as identifier
            'password' => $this->emailAndPhoneUserPassword,
        ]);
        $this->assertSuccessfulLoginResponse($this->client->getResponse());
    }

    public function testLoginWithWrongPasswordForUserWithBothEmailAndPhoneFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>', // Identifier can be email
            'password' => 'wrongPasswordForBoth',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithInvalidIdentifierFormatFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '!@#$%^',
            'password' => 'anypassword',
        ]);
        $this->assertFailedLoginResponse($this->client->getResponse());
    }

    public function testLoginWithDisabledUserFails(): void
    {
        $this->client->jsonRequest('POST', '/api/login_check', [
            'username' => '<EMAIL>',
            'password' => $this->disabledUserPassword,
        ]);
        // The UserProvider checks isEnabled(), so this should result in UserNotFound or similar
        // which translates to a 401 by the security system.
        $this->assertFailedLoginResponse($this->client->getResponse());
    }
}
