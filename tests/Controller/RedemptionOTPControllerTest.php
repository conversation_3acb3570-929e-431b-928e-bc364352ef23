<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class RedemptionOTPControllerTest extends WebTestCase
{
    public function testGenerateOTP(): void
    {
        $client = static::createClient();

        // Test with valid phone number
        $client->jsonRequest(
            'POST',
            '/api/v1/otp/pending-orders',
            ['phone_number' => '+230 5496 1234']
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Test without phone number
        $client->jsonRequest(
            'POST',
            '/api/v1/otp/pending-orders',
            []
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }
}
