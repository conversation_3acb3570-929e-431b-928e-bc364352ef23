<?php

namespace App\Tests\Controller;

use App\Entity\FillingStation;
use App\Entity\PaymentDetails;
use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataMediaMedia;
use App\Entity\SonataUserUser;
use App\Entity\Vehicle;
use Doctrine\ORM\EntityManager;
use Sonata\UserBundle\Document\UserManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use Sonata\MediaBundle\Model\MediaManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Request;

class PaymentAdminControllerTest extends WebTestCase
{
    /**
     * @var AbstractDatabaseTool
     */
    private $databaseTool;

    /**
     * @var MediaManagerInterface
     */
    private $mediaManager;

    /**
     * @var SonataUserUser
     */
    private $admin;

    /**
     * @var SonataUserUser
     */
    private $user;

    /**
     * @var UserManager
     */
    private $userManager;

    public function setUp(): void
    {
        parent::setUp();

        $client = $this->createClient();
        $this->mediaManager = static::getContainer()->get(MediaManagerInterface::class);
        $this->databaseTool = static::getContainer()->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures();

        $container = $this->getContainer();
        $this->userManager = $container->get('sonata.user.manager.user');
        $this->admin = new SonataUserUser();
        $this->admin->setUsername('<EMAIL>');
        $this->admin->setEmail('<EMAIL>');
        $this->admin->setPlainPassword('admin');
        $this->admin->setSuperAdmin(true);
        $this->admin->setEnabled(true);
        // Set a unique mobile phone to avoid unique constraint violation
        $this->admin->setMobilePhone('555444000');

        $this->user = new SonataUserUser();
        $this->user->setUsername('<EMAIL>');
        $this->user->setEmail('<EMAIL>');
        $this->user->setPlainPassword('usersecret');
        $this->user->setSuperAdmin(false);
        $this->user->setEnabled(true);
        // Set a unique mobile phone to avoid unique constraint violation
        $this->user->setMobilePhone('555444111');

        $this->userManager->save($this->user);
        $this->userManager->save($this->admin);

        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/login_check',
            [
                'username' => '<EMAIL>',
                'password' => 'admin',
            ]
        );

        $data = json_decode($client->getResponse()->getContent(), true);
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $data['token']));
    }

    /**
     * @dataProvider paymentDetailsProvider
     */
    public function testSavePaymentDetails(array $requestData)
    {
        $container = $this->getContainer();
        /** @var EntityManager */
        $em = $container->get('doctrine.orm.entity_manager');
        $fillingStation = new FillingStation();
        $fillingStation->setName('Test');
        $fillingStation->setAddress('123, Main Street');
        $em->persist($fillingStation);
        $em->flush();
        $this->assertNotNull($fillingStation->getId());

        $client = $this->getClient();
        if ($requestData['customerUser'] === true) {
            $requestData['customerUser'] = $this->user->getId();
            $expectedUser = $this->user;
        } elseif ($requestData['customerUser'] === false) {
            $requestData['customerUser'] = "";
            $expectedUser = $this->admin;
        } else {
            $requestData['customerUser'] = null;
            $expectedUser = $this->admin;
        }
        $requestData['fillingStationId'] = $fillingStation->getId();
        $client->jsonRequest(
            Request::METHOD_POST,
            '/api/v1/payments-details',
            $requestData
        );

        $this->assertResponseIsSuccessful();
        $repo = $em->getRepository(PaymentDetails::class);
        /** @var PaymentDetails */
        $paymentDetails = $repo->findOneBy(['customer_user' => $expectedUser->getId()]);
        $this->assertNotNull($paymentDetails);
        $this->assertEquals($requestData['amount'], $paymentDetails->getAmount());
        $this->assertNotNull($paymentDetails->getFillingStation());
        $this->assertEquals($requestData['latitude'], $paymentDetails->getLatitude());
        $this->assertEquals($requestData['longitude'], $paymentDetails->getLongitude());
        $this->assertEquals(json_encode($requestData['position']), $paymentDetails->getPosition());
    }

    public function paymentDetailsProvider()
    {
        return [
            [
                [
                    'customerUser' => true, // This will be replaced in the test method
                    'fillingStationId' => 1, // This will be replaced in the test method
                    'amount' => 5702.92,
                    'milage' => 0.0,
                    'unitPrice' => 63.95,
                    'litres' => 89.17,
                    'image' => "/uploads/fuel_meter/6790cdc3817b2.jpg",
                    'latitude' => 0.4,
                    'longitude' => 0.5,
                    'position' => ['x' => 0.4, 'y' => 0.5],
                ]
            ],
            [
                [
                    'customerUser' => false, // This will be replaced in the test method
                    'fillingStationId' => 1, // This will be replaced in the test method
                    'amount' => 5702.92,
                    'milage' => 0.0,
                    'unitPrice' => 63.95,
                    'litres' => 89.17,
                    'image' => "/uploads/fuel_meter/6790cdc3817b2.jpg",
                    'latitude' => 0.4,
                    'longitude' => 0.5,
                    'position' => ['x' => 0.4, 'y' => 0.5],
                ]
            ],
            [
                [
                    'customerUser' => null, // This will be replaced in the test method
                    'fillingStationId' => 1, // This will be replaced in the test method
                    'amount' => 5702.92,
                    'milage' => 0.0,
                    'unitPrice' => 63.95,
                    'litres' => 89.17,
                    'image' => "/uploads/fuel_meter/6790cdc3817b2.jpg",
                    'latitude' => 0.4,
                    'longitude' => 0.5,
                    'position' => ['x' => 0.4, 'y' => 0.5],
                ]
            ]
        ];
    }
}
