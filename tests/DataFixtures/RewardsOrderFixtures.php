<?php

namespace App\Tests\DataFixtures;

use App\Entity\RewardsItem;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class RewardsOrderFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Find the test user (created in CustomerControllerTest setup)
        $userRepository = $manager->getRepository(SonataUserUser::class);
        $user = $userRepository->findOneBy(['email' => '<EMAIL>']);

        if (!$user) {
            // If the user doesn't exist yet, we'll create one
            // This is a fallback, as the user should be created in the test setup
            $user = new SonataUserUser();
            $user->setUsername('test_user');
            $user->setEmail('<EMAIL>');
            $user->setPlainPassword('test_password');
            $user->setEnabled(true);
            $manager->persist($user);
            $manager->flush(); // Flush to get an ID for the user
        }

        // Create a rewards item
        $rewardsItem = new RewardsItem();
        $rewardsItem->setName('Test Item');
        $rewardsItem->setPoints(100);
        $rewardsItem->setQuantity(10);
        $rewardsItem->setCreatedAt(new \DateTimeImmutable());
        $manager->persist($rewardsItem);

        // Create a pending order
        $order = new RewardsOrder();
        $order->setState(RewardsOrder::STATUS_PENDING);
        $order->setUser($user);
        $order->setItem($rewardsItem);
        $order->setCreatedAt(new \DateTimeImmutable());

        // We need to set these manually for testing since we're not using the lifecycle callback
        $order->setNumber('12345');
        $order->setOrderNumber('ORD-12345');

        $manager->persist($order);
        $manager->flush();

        // Add a reference to the order so we can access it in tests if needed
        $this->addReference('test-order', $order);
        $this->addReference('test-item', $rewardsItem);
    }
}
