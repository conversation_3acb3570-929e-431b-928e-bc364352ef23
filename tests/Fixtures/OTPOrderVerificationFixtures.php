<?php

namespace App\Tests\Fixtures;

use App\Entity\OTPOrderVerification;
use App\Entity\RewardsOrder;
use App\Entity\SonataUserUser;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class OTPOrderVerificationFixtures extends Fixture implements DependentFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        // Get references from other fixtures
        $customer = $this->getReference('customer');
        $order = $this->getReference('rewards_order');

        // Create a test OTP verification
        $otpVerification = new OTPOrderVerification();
        $otpVerification->setRewardOrder($order);
        $otpVerification->setCustomer($customer);
        $otpVerification->setCode('123456');
        
        $manager->persist($otpVerification);
        $manager->flush();

        // Add reference for use in tests
        $this->addReference('otp_verification', $otpVerification);
    }

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            RewardsOrderFixtures::class,
        ];
    }
}
