<?php

namespace App\Tests;

use App\FuelMeterScanner;
use OpenAI;
use PHPUnit\Framework\TestCase;

class FuelMeterScannerTest extends TestCase
{
    public function testScan(): void
    {
        $apiKey = getenv('OPENAI_API_KEY');
        $image_base64 = base64_encode(file_get_contents('tests/fuel_meter_demo.jpg'));
        $scanner = new FuelMeterScanner($apiKey);
        $extractJson = $scanner->scan($image_base64);

        $this->assertGreaterThan(0, $extractJson['amount']);
        $this->assertGreaterThan(0, $extractJson['litres']);
        $this->assertGreaterThan(0, $extractJson['unit_price']);
    }

    public function testScanWithGemini(): void
    {
        $apiKey = $_ENV['GEMINI_API_KEY'] ?? null;
        if (empty($apiKey)) {
            $this->markTestSkipped('GEMINI_API_KEY environment variable not set');
        }

        $filePath = 'tests/fuel_meter_demo.jpg';
        if (!file_exists($filePath)) {
            $this->markTestSkipped('Test image file not found: ' . $filePath);
        }

        $scanner = new FuelMeterScanner();
        $extractJson = $scanner->scanWithGemini($filePath, $apiKey);

        $this->assertNotNull($extractJson, 'Extracted JSON should not be null');
        $this->assertArrayHasKey('amount', $extractJson);
        $this->assertArrayHasKey('litres', $extractJson);
        $this->assertArrayHasKey('unit_price', $extractJson);
        $this->assertGreaterThan(0, $extractJson['amount']);
        $this->assertGreaterThan(0, $extractJson['litres']);
        $this->assertGreaterThan(0, $extractJson['unit_price']);
    }
}
