document.addEventListener('DOMContentLoaded', function () {
    var inputElements = document.querySelectorAll('input[iomlMapPicker]');

    for (const inputElement of inputElements) {
        inputElement.style.marginTop = '8px';
        inputElement.style.marginBottom = '8px';

        // Create a new button element
        var confirmBtn = document.createElement('button');
        confirmBtn.innerText = 'Confirm Position';
        confirmBtn.type = 'button';
        confirmBtn.className = 'btn btn-primary';
        inputElement.parentNode.insertBefore(confirmBtn, inputElement.nextSibling);

        // Create a new div element
        var mapDiv = document.createElement('div');
        mapDiv.id = 'map';
        mapDiv.style.width = '100%';
        mapDiv.style.height = '350px';
        inputElement.parentNode.insertBefore(mapDiv, inputElement);

        // init lat and lng
        var hasInitialLocation = inputElement.value && inputElement.value.split(',').length == 2;
        var mapPickerOption = hasInitialLocation ? {
            lat: parseFloat(inputElement.value.split(',')[0]) || 0,
            lng: parseFloat(inputElement.value.split(',')[1]) || 0,
            setCurrentPosition: false
        } : {
            setCurrentPosition: true
        };


        // Initialize locationPicker plugin
        var lp = new locationPicker('map', mapPickerOption, {
            zoom: 15 // You can set any google map options here, zoom defaults to 15
        });

        // Listen to button onclick event
        confirmBtn.onclick = function () {
            // Get current location and show it in HTML
            var location = lp.getMarkerPosition();
            inputElement.value = location.lat + ',' + location.lng;
        };
    }
});
