/**
 * Initialize the Quality Control dashboard
 *
 * @param {Object} summaryMetrics - Summary metrics for the different systems
 * @param {Object} fieldAccuracy - Field-specific accuracy data
 * @param {Object} errorTypes - Error type distribution data
 * @param {Object} correlationData - Data for correlation validation
 * @param {Object} geolocationMetrics - Geolocation accuracy metrics
 */
function init_quality_control(summaryMetrics, fieldAccuracy, errorTypes, correlationData, geolocationMetrics) {
    // Display summary metrics
    const currentAccuracyEl = document.getElementById('current-accuracy');
    currentAccuracyEl.innerHTML = summaryMetrics.currentSystem.overallAccuracy + '%' +
        '<br><small>(' + summaryMetrics.currentSystem.accuracyRawCounts.passedCount + '/' +
        summaryMetrics.currentSystem.accuracyRawCounts.totalCount + ')</small>';

    // Update the progress bar for current system accuracy
    const currentAccuracyProgressBar = currentAccuracyEl.parentElement.querySelector('.progress-bar');
    if (currentAccuracyProgressBar) {
        currentAccuracyProgressBar.style.width = summaryMetrics.currentSystem.overallAccuracy + '%';
    }

    const improvedAccuracyEl = document.getElementById('improvement1-accuracy');
    improvedAccuracyEl.innerHTML = summaryMetrics.improvedSystem.overallAccuracy + '%' +
        '<br><small>(' + summaryMetrics.improvedSystem.accuracyRawCounts.passedCount + '/' +
        summaryMetrics.improvedSystem.accuracyRawCounts.totalCount + ')</small>';

    // Update the progress bar for improved system accuracy
    const improvedAccuracyProgressBar = improvedAccuracyEl.parentElement.querySelector('.progress-bar');
    if (improvedAccuracyProgressBar) {
        improvedAccuracyProgressBar.style.width = summaryMetrics.improvedSystem.overallAccuracy + '%';
    }

    document.getElementById('accuracy-improvement').textContent =
        '+' + (summaryMetrics.improvedSystem.overallAccuracy - summaryMetrics.currentSystem.overallAccuracy).toFixed(1) + '%';

    const currentCorrelationEl = document.getElementById('current-correlation');
    currentCorrelationEl.innerHTML = summaryMetrics.currentSystem.correlationValidation + '%' +
        '<br><small>(' + summaryMetrics.currentSystem.correlationRawCounts.passedCount + '/' +
        summaryMetrics.currentSystem.correlationRawCounts.totalCount + ')</small>';

    // Update the correlation progress bar
    const correlationProgressBar = document.querySelector('.info-box-icon.bg-purple').nextElementSibling.querySelector('.progress-bar');
    if (correlationProgressBar) {
        correlationProgressBar.style.width = summaryMetrics.improvedSystem.correlationValidation + '%';
    }

    const improvedCorrelationEl = document.getElementById('improvement1-correlation');
    improvedCorrelationEl.innerHTML = summaryMetrics.improvedSystem.correlationValidation + '%' +
        '<br><small>(' + summaryMetrics.improvedSystem.correlationRawCounts.passedCount + '/' +
        summaryMetrics.improvedSystem.correlationRawCounts.totalCount + ')</small>';

    document.getElementById('correlation-improvement').textContent =
        '+' + (summaryMetrics.improvedSystem.correlationValidation - summaryMetrics.currentSystem.correlationValidation).toFixed(1) + '%';

    document.getElementById('total-processed').textContent = summaryMetrics.currentSystem.totalProcessed;

    // Display geolocation metrics
    if (document.getElementById('geolocation-accuracy')) {
        document.getElementById('geolocation-accuracy').innerHTML = summaryMetrics.currentSystem.geolocationAccuracy + '%' +
            '<br><small>(' + geolocationMetrics.correctCount + '/' + geolocationMetrics.totalCount + ')</small>';

        // Update geolocation accuracy progress bar
        const geoAccuracyProgressBar = document.getElementById('geolocation-accuracy').parentElement.querySelector('.progress-bar');
        if (geoAccuracyProgressBar) {
            geoAccuracyProgressBar.style.width = summaryMetrics.currentSystem.geolocationAccuracy + '%';
        }
    }

    if (document.getElementById('average-distance')) {
        document.getElementById('average-distance').innerHTML = geolocationMetrics.averageDistance + ' km' +
            '<br><small>Average distance to filling station</small>';

        // Calculate a normalized progress value for average distance
        // Assuming 10km is the max reasonable distance, so we'll use a reverse scale
        // (lower distance = higher progress)
        const maxDistance = 10; // km
        const normalizedProgress = Math.max(0, Math.min(100, (1 - (geolocationMetrics.averageDistance / maxDistance)) * 100));

        // Update average distance progress bar
        const avgDistanceProgressBar = document.getElementById('average-distance').parentElement.querySelector('.progress-bar');
        if (avgDistanceProgressBar) {
            avgDistanceProgressBar.style.width = normalizedProgress + '%';
        }
    }

    // Update geolocation metrics table
    if (document.getElementById('geo-total-count')) {
        document.getElementById('geo-total-count').textContent = geolocationMetrics.totalCount;
    }

    if (document.getElementById('geo-correct-count')) {
        document.getElementById('geo-correct-count').textContent =
            geolocationMetrics.correctCount + ' (' + summaryMetrics.currentSystem.geolocationAccuracy + '%)';
    }

    if (document.getElementById('geo-avg-distance')) {
        document.getElementById('geo-avg-distance').textContent = geolocationMetrics.averageDistance + ' km';
    }

    if (document.getElementById('geo-close-proximity')) {
        document.getElementById('geo-close-proximity').textContent =
            geolocationMetrics.distanceDistribution.counts[0] + ' (' +
            ((geolocationMetrics.distanceDistribution.counts[0] / geolocationMetrics.totalCount) * 100).toFixed(1) + '%)';
    }

    // Initialize charts
    // Field Accuracy Comparison Chart
    const fieldAccuracyChart = new Chart(
        document.getElementById('field-accuracy-chart'),
        {
            type: 'bar',
            data: {
                labels: fieldAccuracy.fields,
                datasets: [
                    {
                        label: 'Manual QC (Ground Truth)',
                        data: fieldAccuracy.manualQC,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Current System',
                        data: fieldAccuracy.currentSystem,
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Improved System',
                        data: fieldAccuracy.improvedSystem,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Accuracy (%)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Field-specific Accuracy Comparison'
                    }
                }
            }
        }
    );

    // Error Type Distribution Chart
    const errorTypesChart = new Chart(
        document.getElementById('error-types-chart'),
        {
            type: 'pie',
            data: {
                labels: errorTypes.labels,
                datasets: [
                    {
                        label: 'Current System',
                        data: errorTypes.currentSystem,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Error Type Distribution - Current System'
                    }
                }
            }
        }
    );

    // Correlation Validation Chart
    const correlationChart = new Chart(
        document.getElementById('correlation-chart'),
        {
            type: 'scatter',
            data: {
                datasets: [
                    {
                        label: 'Current System',
                        data: correlationData.currentSystem.calculated.map((value, index) => ({
                            x: value,
                            y: correlationData.currentSystem.extracted[index]
                        })),
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                    },
                    {
                        label: 'Improved System',
                        data: correlationData.improvedSystem.calculated.map((value, index) => ({
                            x: value,
                            y: correlationData.improvedSystem.extracted[index]
                        })),
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                    },
                    {
                        label: 'Perfect Correlation',
                        data: [
                            { x: 0, y: 0 },
                            { x: 100, y: 100 }
                        ],
                        type: 'line',
                        borderColor: 'rgba(54, 162, 235, 0.7)',
                        borderDash: [5, 5],
                        pointRadius: 0,
                        fill: false,
                        tension: 0
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Calculated Amount (Unit Price × Volume)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Extracted Amount'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Correlation Validation (Amount ≈ Unit Price × Volume)'
                    }
                }
            }
        }
    );

    // Accuracy Trend Chart
    const trendChart = new Chart(
        document.getElementById('accuracy-trend-chart'),
        {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                    {
                        label: 'Current System',
                        data: [76.5, 77.2, 77.8, 78.0, 78.3, 78.5],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        tension: 0.1,
                        fill: false
                    },
                    {
                        label: 'Improved System',
                        data: [null, null, null, 92.8, 93.5, 94.2],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        tension: 0.1,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 70,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Accuracy (%)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Accuracy Trend Over Time'
                    }
                }
            }
        }
    );

    // Toggle buttons functionality for data source selection
    document.querySelectorAll('.toggle-datasource').forEach(button => {
        button.addEventListener('click', function () {
            const dataSource = this.getAttribute('data-source');
            const chartId = this.getAttribute('data-chart');
            const chart = Chart.getChart(chartId);

            // Toggle the active state of the button
            this.classList.toggle('active');
            const isActive = this.classList.contains('active');

            // Find the dataset index for this data source
            const datasetIndex = chart.data.datasets.findIndex(ds => ds.label.includes(dataSource));
            if (datasetIndex !== -1) {
                // Toggle visibility of the dataset
                chart.setDatasetVisibility(datasetIndex, isActive);
                chart.update();
            }
        });
    });

    // Switch between error type charts
    document.getElementById('error-type-selector').addEventListener('change', function () {
        const selectedSystem = this.value;
        errorTypesChart.data.datasets[0].data = errorTypes[selectedSystem];
        errorTypesChart.options.plugins.title.text = `Error Type Distribution - ${selectedSystem === 'currentSystem' ? 'Current System' : 'Improved System'}`;
        errorTypesChart.update();
    });

    // Update geolocation accuracy display
    if (document.getElementById('geo-accuracy')) {
        document.getElementById('geo-accuracy').textContent = geolocationMetrics.accuracy + '%';
    }

    // Custom plugin to ensure minimum bar width for zero values
    const minBarPlugin = {
        id: 'minBar',
        beforeDraw: function (chart) {
            const ctx = chart.ctx;
            const chartArea = chart.chartArea;
            const meta = chart.getDatasetMeta(0);
            const xScale = chart.scales.x;

            // Only apply to horizontal bar charts
            if (chart.config.type === 'bar' && chart.options.indexAxis === 'y') {
                meta.data.forEach((bar, index) => {
                    const value = chart.data.datasets[0].data[index];
                    if (value === 0) {
                        // Set a minimum width for zero values (0.5% of chart width)
                        const minWidth = chartArea.width * 0.005;
                        bar.width = minWidth;

                        // Store original x position for tooltip positioning
                        bar._originalX = bar.x;
                        bar.x = chartArea.left + minWidth / 2;
                    }
                });
            }
        }
    };

    // Create geolocation accuracy chart if the element exists
    let geoAccuracyChart;
    if (document.getElementById('geo-accuracy-chart')) {
        // Default to showing accuracy by station
        const chartData = {
            labels: geolocationMetrics.accuracyByStation.labels,
            datasets: [
                {
                    label: 'Accuracy (%)',
                    data: geolocationMetrics.accuracyByStation.accuracy,
                    backgroundColor: function (context) {
                        // Use a different color for zero values
                        const value = context.raw;
                        return value === 0 ? 'rgba(220, 53, 69, 0.7)' : 'rgba(153, 102, 255, 0.7)';
                    },
                    borderColor: function (context) {
                        const value = context.raw;
                        return value === 0 ? 'rgba(220, 53, 69, 1)' : 'rgba(153, 102, 255, 1)';
                    },
                    borderWidth: 1
                }
            ]
        };

        // Register our custom plugin
        Chart.register(minBarPlugin);

        geoAccuracyChart = new Chart(
            document.getElementById('geo-accuracy-chart'),
            {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    indexAxis: 'y', // This makes the bars horizontal
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Accuracy (%)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Filling Station'
                            }
                        }
                    },
                    plugins: {
                        minBar: {}, // Enable our custom plugin
                        title: {
                            display: true,
                            text: 'Geolocation Accuracy by Filling Station'
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    const index = context.dataIndex;
                                    const counts = geolocationMetrics.accuracyByStation.counts[index];
                                    const accuracy = context.raw;
                                    return [
                                        `Accuracy: ${accuracy}%`,
                                        `Correct: ${counts.correct}/${counts.total}`,
                                        `Attempts: ${counts.total}`
                                    ];
                                },
                                title: function (context) {
                                    const label = context[0].label;
                                    return label;
                                }
                            }
                        },
                        // Add data labels to show attempts count
                        datalabels: {
                            align: 'end',
                            anchor: 'end',
                            formatter: function (value, context) {
                                const index = context.dataIndex;
                                const counts = geolocationMetrics.accuracyByStation.counts[index];
                                return `${value}% (${counts.total})`;
                            },
                            color: function (context) {
                                return context.raw === 0 ? '#fff' : '#333';
                            },
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    maintainAspectRatio: false
                }
            }
        );

        // Add event listener for the chart selector
        document.getElementById('geo-chart-selector').addEventListener('change', function () {
            const selectedView = this.value;
            let chartData, chartOptions;

            if (selectedView === 'time') {
                chartData = {
                    labels: geolocationMetrics.accuracyByTime.labels,
                    datasets: [
                        {
                            label: 'Accuracy (%)',
                            data: geolocationMetrics.accuracyByTime.accuracy,
                            backgroundColor: function (context) {
                                // Use a different color for zero values
                                const value = context.raw;
                                return value === 0 ? 'rgba(220, 53, 69, 0.7)' : 'rgba(75, 192, 192, 0.7)';
                            },
                            borderColor: function (context) {
                                const value = context.raw;
                                return value === 0 ? 'rgba(220, 53, 69, 1)' : 'rgba(75, 192, 192, 1)';
                            },
                            borderWidth: 1
                        }
                    ]
                };

                chartOptions = {
                    indexAxis: 'y', // Keep horizontal bars for time view too
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                text: 'Accuracy (%)'
                            }
                        },
                        y: {
                            title: {
                                text: 'Time Period (Month)'
                            }
                        }
                    },
                    plugins: {
                        minBar: {}, // Enable our custom plugin
                        title: {
                            text: 'Geolocation Accuracy Over Time'
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    const index = context.dataIndex;
                                    const counts = geolocationMetrics.accuracyByTime.counts[index];
                                    const accuracy = context.raw;
                                    return [
                                        `Accuracy: ${accuracy}%`,
                                        `Correct: ${counts.correct}/${counts.total}`,
                                        `Attempts: ${counts.total}`
                                    ];
                                },
                                title: function (context) {
                                    const label = context[0].label;
                                    return label;
                                }
                            }
                        },
                        // Add data labels to show attempts count
                        datalabels: {
                            align: 'end',
                            anchor: 'end',
                            formatter: function (value, context) {
                                const index = context.dataIndex;
                                const counts = geolocationMetrics.accuracyByTime.counts[index];
                                return `${value}% (${counts.total})`;
                            },
                            color: function (context) {
                                return context.raw === 0 ? '#fff' : '#333';
                            },
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    maintainAspectRatio: false
                };
            } else { // station
                chartData = {
                    labels: geolocationMetrics.accuracyByStation.labels,
                    datasets: [
                        {
                            label: 'Accuracy (%)',
                            data: geolocationMetrics.accuracyByStation.accuracy,
                            backgroundColor: function (context) {
                                // Use a different color for zero values
                                const value = context.raw;
                                return value === 0 ? 'rgba(220, 53, 69, 0.7)' : 'rgba(153, 102, 255, 0.7)';
                            },
                            borderColor: function (context) {
                                const value = context.raw;
                                return value === 0 ? 'rgba(220, 53, 69, 1)' : 'rgba(153, 102, 255, 1)';
                            },
                            borderWidth: 1
                        }
                    ]
                };

                chartOptions = {
                    indexAxis: 'y', // Keep horizontal bars for station view
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                text: 'Accuracy (%)'
                            }
                        },
                        y: {
                            title: {
                                text: 'Filling Station'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            text: 'Geolocation Accuracy by Filling Station'
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    const index = context.dataIndex;
                                    const counts = geolocationMetrics.accuracyByStation.counts[index];
                                    return [
                                        `Accuracy: ${context.raw}%`,
                                        `Correct: ${counts.correct}/${counts.total}`
                                    ];
                                }
                            }
                        }
                    },
                    maintainAspectRatio: false
                };
            }

            // Update chart data and options
            geoAccuracyChart.data.labels = chartData.labels;
            geoAccuracyChart.data.datasets = chartData.datasets;

            // Update all the options
            geoAccuracyChart.options.indexAxis = chartOptions.indexAxis;
            geoAccuracyChart.options.scales.x.beginAtZero = chartOptions.scales.x.beginAtZero;
            geoAccuracyChart.options.scales.x.max = chartOptions.scales.x.max;
            geoAccuracyChart.options.scales.x.title.text = chartOptions.scales.x.title.text;
            geoAccuracyChart.options.scales.y.title.text = chartOptions.scales.y.title.text;
            geoAccuracyChart.options.plugins.title.text = chartOptions.plugins.title.text;
            geoAccuracyChart.options.plugins.tooltip.callbacks = chartOptions.plugins.tooltip.callbacks;
            geoAccuracyChart.options.maintainAspectRatio = chartOptions.maintainAspectRatio;

            geoAccuracyChart.update();
        });
    }
}