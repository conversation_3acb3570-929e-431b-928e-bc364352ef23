# Redeem Process Documentation

This document outlines the implementation of the Redeem Process feature, which allows customers to redeem rewards using their accumulated points.

## API Endpoints

### 1. Customer Search by Phone API
```
GET /api/v1/customers/search?phone={phoneNumber}
```
- **Purpose**: Find customer by phone number
- **Request Parameters**:
  - `phone`: Customer's phone number
- **Response**:
  - Success (200):
    ```json
    {
      "id": "string",
    }
    ```
  - Not Found (404)
  - Error (500)

### 2. Pending Orders API
```
GET /api/v1/customers/{customerId}/pending-orders
```
- **Purpose**: Retrieve list of pending orders for a customer
- **Request Parameters**:
  - `customerId`: Customer's ID
- **Request Headers**:
  - `Authorization`: Bearer token
- **Response**:
  - Success (200):
    ```json
    [
      {
        "orderNumber": "string",
        "itemName": "string",
        "pointCost": number
      }
    ]
    ```
  - Not Found (404): Returns empty array []
  - Error (500)

### 3. Customer Points Endpoint
```
GET /api/v1/customers/{customerId}/points
```
- **Purpose**: Retrieve available points for a customer
- **Request Parameters**:
  - `customerId`: Customer's ID
- **Request Headers**:
  - `Authorization`: Bearer token
- **Response**:
  - Success (200):
    ```json
    {
      "availablePoints": number
    }
    ```
  - Not Found (404)
  - Error (500)

### 4. Generate OTP for Customer
```
POST /api/v1/customers/{customerId}/generate-otp
```
- **Purpose**: Generate and send OTP to a customer for order verification
- **Request Parameters**:
  - `customerId`: Customer's ID
- **Request Body**:
  ```json
  {
    "phoneNumber": "string",
    "orderId": "string"
  }
  ```
- **Request Headers**:
  - `Authorization`: Bearer token
- **Response**:
  - Success (204): No content
  - Not Found (404)
  - Error (500)

### 5. OTP Verification Endpoint
```
POST /api/v1/customers/{customerId}/verify-otp
```
- **Purpose**: Verify OTP and confirm order
- **Request Parameters**:
  - `customerId`: Customer's ID
- **Request Body**:
  ```json
  {
    "orderId": "string",
    "otp": "string"
  }
  ```
- **Request Headers**:
  - `Authorization`: Bearer token
- **Response**:
  - Success (204): No content
  - Invalid OTP (400)
  - Not Found (404)
  - Error (500)

## Implementation Details

### OTPOrderVerification Entity
- Fields:
  - id (UUID)
  - order_id (UUID)
  - otp_code (string)
  - customer_id (integer)
  - expires_at (timestamp)
  - verified_at (timestamp)
  - created_at (timestamp)
  - attempts (integer, default 0)

### OTP Service
- Handles OTP generation and sending
- Implements SMS sending functionality using existing sendSingleMessage function
- Provides methods to:
  - Generate and store OTP for a specific order
  - Send OTP via SMS with appropriate message template
  - Verify OTP against stored value
  - Mark OTP as used (update verified_at timestamp)
  - Confirm the order (update order status)

### Security
- All endpoints require authentication with Bearer token
- Proper validation for:
  - Customer ID
  - Phone number format
  - Order ID
  - OTP format

### Error Handling
- Proper error responses for:
  - Invalid request formats
  - Non-existent customers
  - Invalid/missing authentication
  - SMS sending failures

### Testing
- Comprehensive test coverage for all endpoints:
  - Success scenarios
  - Error scenarios
  - Authentication validation
  - Request validation
