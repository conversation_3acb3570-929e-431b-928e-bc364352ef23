# OTP Registration Integration Guide

This guide explains how to integrate with the OTP-based registration system for the Indian Oil Mauritius Loyalty app.

## Overview

The OTP registration flow consists of three main steps:

1. **Request OTP**: Send registration data and receive a registration ID
2. **Verify OTP**: Submit the OTP code to verify the phone number and create the account
3. **Resend OTP** (optional): Request a new OTP if the original one expires or is lost

## API Endpoints

### 1. Request OTP

**Endpoint**: `POST /api/otp/register`

**Purpose**: Submit registration data and request an OTP verification code

**Request Body**:
```json
{
  "mobilePhone": "+230 5496 1111",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Required Fields**:
- `mobilePhone`: Phone number in international format (e.g., +230 XXXX XXXX)

**Optional Fields**:
- `email`: User's email address (if not provided, one will be generated)
- `firstName`: User's first name
- `lastName`: User's last name

**Successful Response** (200 OK):
```json
{
  "registrationId": "reg-12345abcdef",
  "expiresIn": 600
}
```

**Notes**:
- `registrationId` is a unique identifier for this registration attempt
- `expiresIn` is the time in seconds until the OTP expires (10 minutes)
- The system will send an SMS with a 6-digit OTP to the provided phone number
- The password is not required at this step; it will be provided during the verification step

**Error Responses**:
- 400 Bad Request: Missing required fields or validation errors
- 400 Bad Request: Phone number already in use
- 500 Internal Server Error: Server-side error

### 2. Verify OTP

**Endpoint**: `POST /api/otp/register_check`

**Purpose**: Verify the OTP code and create the user account

**Request Body**:
```json
{
  "registrationId": "reg-12345abcdef",
  "code": "123456",
  "password": "securePassword123"
}
```

**Required Fields**:
- `registrationId`: The registration ID received from the previous step
- `code`: The 6-digit OTP code received via SMS
- `password`: User's chosen password

**Successful Response** (201 Created):
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Notes**:
- Upon successful verification, the user account is created with the provided password
- The response includes JWT tokens for immediate authentication
- The user can now log in to the application

**Error Responses**:
- 400 Bad Request: Invalid OTP code
- 400 Bad Request: OTP expired
- 400 Bad Request: Maximum verification attempts reached
- 400 Bad Request: Registration not found
- 500 Internal Server Error: Server-side error

### 3. Resend OTP

**Endpoint**: `POST /api/otp/register/resend`

**Purpose**: Request a new OTP code for an existing registration

**Request Body**:
```json
{
  "registrationId": "reg-12345abcdef"
}
```

**Required Fields**:
- `registrationId`: The registration ID from the initial request

**Successful Response** (204 No Content):
Empty response body

**Notes**:
- A new OTP code will be sent to the same phone number
- The expiration time will be reset to 10 minutes
- The verification attempts counter will be reset

**Error Responses**:
- 400 Bad Request: Registration not found
- 400 Bad Request: Registration already verified
- 500 Internal Server Error: Server-side error

## Implementation Example

### JavaScript/Fetch API Example

```javascript
// Step 1: Request OTP
async function requestOTP(userData) {
  // Note: userData should contain mobilePhone and optionally email, firstName, lastName
  // Password is not required at this step
  try {
    const response = await fetch('/api/otp/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to request OTP');
    }

    return await response.json();
  } catch (error) {
    console.error('Error requesting OTP:', error);
    throw error;
  }
}

// Step 2: Verify OTP
async function verifyOTP(registrationId, otpCode, password) {
  try {
    const response = await fetch('/api/otp/register_check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        registrationId: registrationId,
        code: otpCode,
        password: 'securePassword123'
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to verify OTP');
    }

    return await response.json();
  } catch (error) {
    console.error('Error verifying OTP:', error);
    throw error;
  }
}

// Step 3: Resend OTP (if needed)
async function resendOTP(registrationId) {
  try {
    const response = await fetch('/api/otp/register/resend', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        registrationId: registrationId
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to resend OTP');
    }

    return true; // Success
  } catch (error) {
    console.error('Error resending OTP:', error);
    throw error;
  }
}
```

## Best Practices

1. **Validate Input**: Always validate phone numbers and other user inputs before sending to the API
2. **Handle Errors**: Implement proper error handling for all API responses
3. **Timeout Handling**: Implement a timer to track OTP expiration and offer resend option
4. **User Feedback**: Provide clear feedback to users about the registration process
5. **Security**: Never store OTP codes or registration IDs in localStorage or cookies
6. **Rate Limiting**: Implement client-side rate limiting for OTP resend requests

## Testing

For testing purposes, you can use the following test phone number:

- Phone: `+230 5496 1111`

This number is configured to work with the OTP system in the test environment.

## Support

For any issues or questions regarding the OTP registration integration, please contact the development team.
