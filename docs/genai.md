# Image understanding

Gemini models can process images, enabling many frontier developer use cases that would have historically required domain specific models.
Some of Gemini's vision capabilities include the ability to:

- Caption and answer questions about images
- Transcribe and reason over PDFs, including up to 2 million tokens
- Detect objects in an image and return bounding box coordinates for them
- Segment objects within an image

Gemini was built to be multimodal from the ground up and we continue to push the frontier of what is possible. This guide shows how to use the Gemini API to generate text responses based on image inputs and perform common image understanding tasks.

### Before you begin

Before calling the Gemini API, ensure you have your SDK of choice installed, and a Gemini API key configured and ready to use.

## Image input

You can provide images as input to <PERSON> in the following ways:

- Upload an image file using the File API before making a request to `generateContent`. Use this method for files larger than 20MB or when you want to reuse the file across multiple requests.
- Pass inline image data with the request to `generateContent`. Use this method for smaller files (<20MB total request size) or images fetched directly from URLs.

### Upload an image file

You can use the Files API to upload an image file. Always use the Files API when the total request size (including the file, text prompt, system instructions, etc.) is larger than 20 MB, or if you intend to use the same image in multiple prompts.

The following code uploads an image file and then uses the file in a call to `generateContent`.

```bash
IMAGE_PATH="path/to/sample.jpg"
MIME_TYPE=$(file -b --mime-type "${IMAGE_PATH}")
NUM_BYTES=$(wc -c < "${IMAGE_PATH}")
DISPLAY_NAME=IMAGE

tmp_header_file=upload-header.tmp

# Initial resumable request defining metadata.
# The upload url is in the response headers dump them to a file.
curl "https://generativelanguage.googleapis.com/upload/v1beta/files?key=${GOOGLE_API_KEY}" \
  -D upload-header.tmp \
  -H "X-Goog-Upload-Protocol: resumable" \
  -H "X-Goog-Upload-Command: start" \
  -H "X-Goog-Upload-Header-Content-Length: ${NUM_BYTES}" \
  -H "X-Goog-Upload-Header-Content-Type: ${MIME_TYPE}" \
  -H "Content-Type: application/json" \
  -d "{'file': {'display_name': '${DISPLAY_NAME}'}}" 2> /dev/null

upload_url=$(grep -i "x-goog-upload-url: " "${tmp_header_file}" | cut -d" " -f2 | tr -d "\r")
rm "${tmp_header_file}"

# Upload the actual bytes.
curl "${upload_url}" \
  -H "Content-Length: ${NUM_BYTES}" \
  -H "X-Goog-Upload-Offset: 0" \
  -H "X-Goog-Upload-Command: upload, finalize" \
  --data-binary "@${IMAGE_PATH}" 2> /dev/null > file_info.json

file_uri=$(jq ".file.uri" file_info.json)
echo file_uri=$file_uri

# Now generate content using that file
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$GOOGLE_API_KEY" \
    -H 'Content-Type: application/json' \
    -X POST \
    -d '{
      "contents": [{
        "parts":[
          {"file_data":{"mime_type": "${MIME_TYPE}", "file_uri": '$file_uri'}},
          {"text": "Caption this image."}]
        }]
      }' 2> /dev/null > response.json

cat response.json
echo

jq ".candidates[].content.parts[].text" response.json
```

To learn more about working with media files, see Files API.

### Pass image data inline

Instead of uploading an image file, you can pass inline image data in the request to `generateContent`. This is suitable for smaller images (less than 20MB total request size) or images fetched directly from URLs.

You can provide image data as Base64 encoded strings or by reading local files directly (depending on the SDK).

**Local image file:**

```bash
# Base64 encode the image file
IMAGE_PATH="path/to/small-sample.jpg"
MIME_TYPE=$(file -b --mime-type "${IMAGE_PATH}")
BASE64_IMAGE=$(base64 -w 0 "${IMAGE_PATH}")

# Generate content with inline image
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GOOGLE_API_KEY}" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [{
      "parts":[
        {"inline_data":{"mime_type":"'${MIME_TYPE}'", "data":"'${BASE64_IMAGE}'"}},
        {"text": "Caption this image."}]
      }]
    }' 2> /dev/null > response.json

cat response.json
echo

jq ".candidates[].content.parts[].text" response.json
```

## Prompting with multiple images

You can include multiple images in a single prompt to Gemini. This is useful for comparing images, analyzing sequences, or providing additional context.

```bash
# Base64 encode the image files
IMAGE_PATH1="path/to/image1.jpg"
IMAGE_PATH2="path/to/image2.jpg"
MIME_TYPE1=$(file -b --mime-type "${IMAGE_PATH1}")
MIME_TYPE2=$(file -b --mime-type "${IMAGE_PATH2}")
BASE64_IMAGE1=$(base64 -w 0 "${IMAGE_PATH1}")
BASE64_IMAGE2=$(base64 -w 0 "${IMAGE_PATH2}")

# Generate content with multiple inline images
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GOOGLE_API_KEY}" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [{
      "parts":[
        {"inline_data":{"mime_type":"'${MIME_TYPE1}'", "data":"'${BASE64_IMAGE1}'"}},
        {"text": "This is the first image."},
        {"inline_data":{"mime_type":"'${MIME_TYPE2}'", "data":"'${BASE64_IMAGE2}'"}},
        {"text": "This is the second image. What are the differences between these two images?"}]
      }]
    }' 2> /dev/null > response.json

cat response.json
echo

jq ".candidates[].content.parts[].text" response.json
```

## Get a bounding box for an object

You can ask Gemini to identify objects in an image and return bounding box coordinates for them. This is useful for object detection and localization tasks.

```bash
# Base64 encode the image file
IMAGE_PATH="path/to/image-with-objects.jpg"
MIME_TYPE=$(file -b --mime-type "${IMAGE_PATH}")
BASE64_IMAGE=$(base64 -w 0 "${IMAGE_PATH}")

# Generate content with bounding box request
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GOOGLE_API_KEY}" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [{
      "parts":[
        {"inline_data":{"mime_type":"'${MIME_TYPE}'", "data":"'${BASE64_IMAGE}'"}},
        {"text": "Find the dog in this image and give me the bounding box coordinates in the format [left, top, right, bottom] with values between 0 and 1."}]
      }]
    }' 2> /dev/null > response.json

cat response.json
echo

jq ".candidates[].content.parts[].text" response.json
```

## Image segmentation

Gemini can perform image segmentation by identifying regions in an image that correspond to specific objects or categories. The model returns a mask that highlights the segmented area.

```bash
# Base64 encode the image file
IMAGE_PATH="path/to/segmentation-image.jpg"
MIME_TYPE=$(file -b --mime-type "${IMAGE_PATH}")
BASE64_IMAGE=$(base64 -w 0 "${IMAGE_PATH}")

# Generate content with segmentation request
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GOOGLE_API_KEY}" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [{
      "parts":[
        {"inline_data":{"mime_type":"'${MIME_TYPE}'", "data":"'${BASE64_IMAGE}'"}},
        {"text": "Segment the cat in this image and provide the mask as a binary array."}]
      }]
    }' 2> /dev/null > response.json

cat response.json
echo

jq ".candidates[].content.parts[].text" response.json
```

## Supported image formats

Gemini supports the following image formats:

- JPEG/JPG
- PNG
- WEBP
- HEIC
- HEIF
- GIF (treated as a static image)

## Technical details about images

- **Maximum file size**: 20MB for inline images, 100MB for uploaded files
- **Resolution**: Images are automatically resized if they exceed the maximum dimensions
- **Color depth**: 8-bit, 16-bit, and 32-bit images are supported
- **Transparency**: Alpha channels are preserved
- **Metadata**: EXIF data is stripped for privacy reasons

## What's next

- Explore the [Files API](https://ai.google.dev/gemini-api/docs/files) for working with larger media files
- Learn about [multimodal prompting](https://ai.google.dev/gemini-api/docs/multimodal-prompting) techniques
- Try [image generation](https://ai.google.dev/gemini-api/docs/image-generation) capabilities
