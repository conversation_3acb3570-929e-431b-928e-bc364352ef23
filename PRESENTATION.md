# Indian Oil Mauritius Loyalty System
## Executive Presentation Document

### Run Project

- admin `symfony server:start`
- launch 2 emulators in vscode
- Reverse Proxy port 8000
    - `adb -s emulator-5554 reverse tcp:8000 tcp:8000`
    - `adb -s emulator-5556 reverse tcp:8000 tcp:8000`
- Run mobile application on respective emulator
    - `flutter run -d emulator-5554`
    - `flutter run -d emulator-5556`

### Key Feature

- OCR
    - Results
        - QC Dashboard — http://localhost:8000/admin/app/paymentdetailsreview/quality_control
            - OCR + ChatGPT — Give the best result for the moment
            - Multi Modal Model
                — ChatGPT
                - Gemini 2
                - Llama 3.2
            - Raw accuracy without optimisation (55%)
            - No Corralation in wrong reading for Price / Price per Rs or Volume
            - Improve system use a reasoning model that may take up to 40s
            - Train Specialise model
                - On Going Process — Sanitize & Weighting
                - AI Agent
                    - Season / Weather Condition
                    - Time of day / Lighting
                    - Season / Weather Condition
        - Detail Result
            - Passed — http://localhost:8000/admin/app/paymentdetails/275/show
            - Failed — http://localhost:8000/admin/app/paymentdetails/271/show
            - Rejected — http://localhost:8000/admin/app/paymentdetails/251/show
            - Invalid — http://localhost:8000/admin/app/paymentdetails/243/show
            - Distance — http://localhost:8000/admin/app/paymentdetails/123/show
            - Damaged Screen — http://localhost:8000/admin/app/paymentdetails/215/show
            - Screen Protector — http://localhost:8000/admin/app/paymentdetails/121/show
            - Dispensor Error — http://localhost:8000/admin/app/paymentdetails/60/show 
            - Blur — http://127.0.0.1:8000/admin/app/paymentdetails/268/show
            - Missing Info — http://127.0.0.1:8000/admin/app/paymentdetails/27/show
    - Solution
        - Glare & Reflection
            - Anti-glare screen protectors
            - Polarizing filters on cameras
            - Light diffusers — very unlikely but can give other ideas
        - Train a lite embedded model assist staff to take better photo
            - At least 1,000 photos
            - On Going Process
        - Short Term Proposition #1
            - Take Photo
            - Wait for initial value from 1st model
            - If value is not correct, ask staff to enter the correct value
            - Use reasoning model for QC in the background
        - Short Term Proposition #2
            - Ask staff to enter the correct value
            - Take Photo
            - Process in the background

### Show case

- Missing ID Card
- Order history
- Redeem Process
- Registration

## Future Roadmap

- Benchark other solution
    - Gemini 2.5
    - Llama 4
    - ChatGPT 4.1

- User Data Entry
    - Photo
    - if error enter manually — notifiy that will be validated manually
- Automatic photo on valid context
