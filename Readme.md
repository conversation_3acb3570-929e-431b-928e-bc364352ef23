# Indian Oil Mauritius Loyalty System Backend

## Overview
Backend component for the IOML Rewards loyalty system dashboard that compares OCR accuracy. The system processes fuel dispenser screen images using ChatGPT Vision API to extract structured data.

## Core Features
- Image processing and OCR analysis
- OCR accuracy comparison dashboard
- Admin interface using Sonata Admin
- Quality control and review system
- REST API endpoints for frontend consumption

## Technical Stack
- **Framework**: Symfony 6.4
- **PHP Version**: 8.1+
- **Database**: MariaDB with Doctrine ORM
- **Admin Interface**: Sonata Admin Bundle
- **UI Framework**: Admin LTE & Bootstrap
- **OCR Services**: ChatGPT Vision API, Google Gemini 2.5 Pro
- **Authentication**: JWT

## Project Structure
```
├── config                  # Configuration files
│   ├── jwt                 # JWT authentication keys
│   ├── packages            # Package configuration
│   ├── routes              # Route definitions
├── migrations              # Database migrations
├── public                  # Publicly accessible files
├── src                     # Application source code
│   ├── Admin               # Admin controllers and services
│   ├── Command             # Console commands
│   ├── Controller          # HTTP controllers
│   ├── DataFixtures        # Data fixtures for testing
│   ├── Entity              # Doctrine entities
│   ├── EventListener      # Event listeners/subscribers
│   ├── Mailer             # Email functionality
│   ├── Repository         # Doctrine repositories
│   └── Service            # Application services
├── templates              # Twig templates
├── tests                  # Test suite
├── translations          # Translation files
```

## Development Setup

### Prerequisites
- Docker & Docker Compose
- PHP 8.1+
- Composer

### Local Development
1. Clone the repository
2. Start the development container:
```bash
docker-compose up -d
```
3. Install dependencies:
```bash
composer install
```
4. Run database migrations:
```bash
php bin/console doctrine:migrations:migrate
```
5. Run tests:
```bash
php bin/phpunit
```

### Deployment
Use the provided Makefile commands:
```bash
# Build and deploy
make deploy

# Deploy with migrations
make deploy-migrate

# Run tests
make test
```

## Coding Standards
- Follow PSR-1, PSR-2, and PSR-12
- Use PHP 8.1+ features
- Follow Symfony best practices
- Use constructor dependency injection
- Implement SOLID principles

## API Guidelines
- RESTful architecture
- JSON request/response format
- Proper HTTP status codes
- API versioning for breaking changes
- OpenAPI documentation

## Fuel Meter Scanner

The system supports scanning fuel meter images using both OpenAI's ChatGPT Vision API and Google's Gemini 2.5 Pro API.

### Using Gemini 2.5 Pro

To scan an image using the Gemini 2.5 Pro API:

1. Set up your Gemini API key in the `.env` file:
```bash
GEMINI_API_KEY=your_api_key_here
```

2. Use the command line tool:
```bash
php bin/console app:gemini:scan-image /path/to/your/image.jpg
```

The command will:
- Validate the image file
- Send it to the Gemini 2.5 Pro API
- Extract and display the structured data (amount, litres, unit price, fuel type)

### API Endpoint

The system also provides an API endpoint that uses the same scanning functionality:
```
POST /api/v1/scan-fuel-meter
```

This endpoint accepts an image file upload and returns the extracted data in JSON format.

## Database and Uploads Import from Production

### Automated Import Script
The project includes an automated script for importing production data to your local environment:

```bash
# Run the import script with default options
bin/import-production-data.sh

# Skip database import, only sync uploads
bin/import-production-data.sh --skip-db

# Skip uploads sync, only import database
bin/import-production-data.sh --skip-uploads

# Show what would be done without making changes
bin/import-production-data.sh --dry-run

# Skip confirmation prompts
bin/import-production-data.sh --force
```

For detailed information about the script implementation, see [Import Script Plan](docs/import-script-plan.md).

> **Known Issue**: There is a known issue with the database import script where the database is downloaded but not automatically loaded into the development database. After running the script, you may need to manually load the data using:
> ```bash
> mysql -u root -h db -pmariadb indianoil_dev < var/tmp/indianoil_prod_backup.sql
> ```

### Manual Import Process
If you prefer to import data manually, follow these steps:

#### Database Import
1. Create SSH tunnel to production server:
```bash
ssh rewards.ioml.mu -l debian -L 3306:localhost:3306
```

2. Export production database (in a new terminal):
```bash
mysqldump -h 127.0.0.1 -u root -pmariadb indianoil_prod > indianoil_prod_backup.sql
```

3. Close the SSH tunnel to prevent any concurrent database modifications

4. Reset local database:
```bash
# Drop existing database
mysql -h db -u root -pmariadb -e "DROP DATABASE IF EXISTS indianoil_dev"

# Create fresh database
mysql -h db -u root -pmariadb -e "CREATE DATABASE indianoil_dev"

# Import production data
mysql -h db -u root -pmariadb indianoil_dev < indianoil_prod_backup.sql

# Remove tmp file
rm indianoil_prod_backup.sql
```

#### Uploads Directory Sync
1. Create local uploads directory if it doesn't exist:
```bash
mkdir -p public/uploads
```

2. Use rsync to download production uploads:
```bash
rsync -avz --progress <EMAIL>:/srv/indian_oil_admin/uploads/ ./public/uploads/
```

Note: The uploads directory is mounted as a volume in both development and production environments:
- Production path: `/srv/indian_oil_admin/uploads`
- Local path: `./public/uploads`
- Docker container path: `/var/www/html/public/uploads`

## Deleting Users by Phone Number

To delete a user from the database based on their canonical phone number (e.g., if the number contains `1111`), you can use the Doctrine DQL command-line tool:

```bash
bin/console doctrine:query:dql "DELETE FROM App\\Entity\\SonataUserUser u WHERE u.mobilePhoneCanonical LIKE '%1111%'"
```

This command will remove all users whose canonical phone number contains `1111`. Use with caution, as this operation is irreversible.

## Obtaining the Last OTP Sent for Registration

To retrieve the last OTP code sent for registration using Doctrine DQL, you can use the following command in your terminal:

```bash
bin/console doctrine:query:dql "SELECT r.otpCode FROM App\\Entity\\RegistrationOTP r ORDER BY r.createdAt DESC" --max-result=1
```

This command will return the most recent OTP code sent for registration. Make sure your database contains data and that the `createdAt` field is properly set for each OTP entry.

## Registration Guidelines

The Indian Oil Mauritius Loyalty System uses an OTP-based registration process to ensure secure and user-friendly onboarding. The registration flow consists of requesting an OTP, verifying the OTP to create an account, and optionally resending the OTP if needed. This process is designed to protect user accounts and streamline registration for new users.

For detailed step-by-step instructions, request/response examples, and error handling, please refer to the [OTP Registration Integration Guide](docs/OTP_Registration_Integration_Guide.md).

## Retrieving Order Confirmation OTPs

To retrieve the last order confirmation OTP from the database using the terminal, you can use the following command:

```bash
# Using Doctrine SQL query
php bin/console doctrine:query:sql "SELECT * FROM otporder_verification ORDER BY id DESC LIMIT 1"
```

This command will display the most recent order confirmation OTP record with all its details, including:
- OTP code
- Associated reward order ID
- Customer ID
- Creation timestamp
- Expiration timestamp
- Verification status
- Number of verification attempts

Alternatively, you can also use MySQL directly:

```bash
# Using MySQL client
mysql -h db -u root -pmariadb -e "USE indianoil_dev; SELECT * FROM otporder_verification ORDER BY id DESC LIMIT 1;"
```

To find a specific OTP for a particular order or customer, you can add filtering conditions:

```bash
# Filter by reward order ID
php bin/console doctrine:query:sql "SELECT * FROM otporder_verification WHERE reward_order_id = 123 ORDER BY created_at DESC LIMIT 1"

# Filter by customer ID
php bin/console doctrine:query:sql "SELECT * FROM otporder_verification WHERE customer_id = 456 ORDER BY created_at DESC LIMIT 1"
```
