FROM php:8.2-apache-bookworm

RUN apt-get update \
    && apt-get install -y libzip-dev libxml2-dev unzip \
    && apt-get install -y libpng-dev libjpeg-dev libfreetype6-dev \
    && apt-get clean -y && rm -rf /var/lib/apt/lists/*

RUN docker-php-ext-install intl mysqli pdo pdo_mysql zip xml
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
ENV COMPOSER_ALLOW_SUPERUSER=1

WORKDIR /var/www/html
COPY composer.json composer.lock /var/www/html/
RUN composer install --no-dev --optimize-autoloader --no-scripts

RUN a2enmod rewrite
COPY . /var/www/html
COPY ./.env.prod /var/www/html/.env
COPY ./php.prod.ini /usr/local/etc/php/conf.d/php.prod.ini
COPY ./apache.prod.conf /etc/apache2/sites-available/000-default.conf

CMD ["sh", "-c", "composer install --no-dev --optimize-autoloader && apache2-foreground"]