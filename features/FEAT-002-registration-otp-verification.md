## Server Implementation Requirements

This section outlines the minimum server implementation required to support the OTP verification during registration. These are the static responses the server should provide to enable client-side development and testing.

### 1. POST `/api/otp/register`

**Purpose**: Send registration data and request OTP

**Request Body**:
```json
{
  "phone": "1234567890",
  "password": "securePassword123",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "vehicleNumber": "ABC123"
}
```

**Successful Response**:
- Status Code: 200 OK
- Response Body:
```json
{
  "registrationId": "reg-12345-abcde",
  "expiresIn": 600
}
```

**Notes**:
- `registrationId` is a unique identifier for this registration attempt
- `expiresIn` is the time in seconds until the registration data expires (10 minutes)
- The server should store all registration data temporarily
- The server should send an OTP to the provided phone number

### 2. POST `/api/otp/register_check`

**Purpose**: Verify <PERSON><PERSON> and create account

**Request Body**:
```json
{
  "registrationId": "reg-12345-abcde",
  "code": "123456"
}
```

**Successful Response**:
- Status Code: 201 Created
- Response Body:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Notes**:
- The server should retrieve the stored registration data using the registration ID
- If the OTP is valid, the server should create the user account
- The response includes authentication tokens for immediate login

### 3. POST `/api/otp/register/resend`

**Purpose**: Resend OTP for an existing registration

**Request Body**:
```json
{
  "registrationId": "reg-12345-abcde"
}
```

**Successful Response**:
- Status Code: 204 No Content
- Response Body: Empty

**Notes**:
- The server should check if the registration ID exists and has not expired
- The server should generate a new OTP and send it to the phone number
- The server should implement rate limiting (e.g., max 3 resend attempts)

### Implementation Notes for Server Team

1. **Temporary Storage**:
   - Store registration data with an expiration time (10 minutes recommended)
   - Use a secure method for storing passwords in temporary storage
   - Implement cleanup for expired registration data

2. **OTP Generation**:
   - Generate 6-digit numeric OTPs
   - Implement rate limiting for OTP requests
   - Consider implementing a cooldown period between resend requests

3. **Security Considerations**:
   - Implement proper validation for all input data
   - Use HTTPS for all API endpoints
   - Consider implementing IP-based rate limiting to prevent abuse
   - Log all registration attempts for security auditing
