# Requirements Specification: PaymentDetails Review Feature

## 1. Introduction

### 1.1 Purpose
This document outlines the functional and non-functional requirements for implementing a payment details review feature in the existing Symfony/Sonata admin back office for the India Oil filling station loyalty program.

### 1.2 Project Scope
The scope of this project is to add functionality that allows back office staff to review manually entered transactions, approve them or modify incorrect values, and visualize the loyalty points calculation before finalizing the review.

### 1.3 Definitions
- **Manually entered transaction**: A transaction that was manually entered by filling station staff rather than scanned from the dispenser
- **Transaction review**: The process of validating and potentially modifying a manually entered transaction
- **Loyalty points**: Points awarded to customers based on transaction value (Rs100 = 1 point)

## 2. System Overview

### 2.1 Current System
The current system is a Symfony/Sonata admin back office used to manage a loyalty program for India Oil filling stations. The system allows:
- Registration of customer transactions through scanning dispenser screens
- Manual entry of transaction details when scanning fails
- Management of customer loyalty points

### 2.2 Proposed Enhancement
Add a new capability for back office staff to review manually entered transactions, approve them if correct, or modify values if there are errors. The enhancement will also display the loyalty points calculation (Rs100 = 1 point) that will result from the transaction.

## 3. Functional Requirements

### 3.1 Transaction Review List

#### 3.1.1
The system shall display a list of transactions that have been manually entered and require review.

#### 3.1.2
The list shall include key transaction information:
- ID
- Customer ID/information
- Transaction date and time
- Transaction amount
- Filling station location
- Staff member who entered the transaction
- Current review status

#### 3.1.3
The list shall provide filtering capabilities to allow staff to filter by:
- Date range
- Transaction status
- Filling station location
- Amount range

#### 3.1.4
The list shall provide sorting capabilities on all displayed columns.

#### 3.1.5
The system shall display a visual indicator or label that clearly identifies transactions requiring review.

### 3.2 Transaction Review Action

#### 3.2.1
The system shall provide a dedicated "Review" action button/link for each manually entered transaction that has not yet been reviewed.

#### 3.2.2
The review action shall only be available for transactions marked as "manually entered" in the system.

#### 3.2.3
The review action shall open a form that displays all relevant transaction details.

#### 3.2.4
The system shall prevent modification of transactions that have already been reviewed.

### 3.3 Transaction Review Form

#### 3.3.1
The review form shall display all current transaction details:
- ID (non-editable)
- Customer information (non-editable)
- Transaction date and time (editable)
- Transaction amount (editable)
- Product/fuel type (editable)
- Quantity (editable)
- Unit price (editable)
- Filling station location (non-editable)
- Staff member who entered the transaction (non-editable)

#### 3.3.2
The form shall allow modification of incorrect transaction values, including but not limited to:
- Transaction amount
- Product/fuel type
- Quantity
- Unit price

#### 3.3.3
The form shall display the loyalty points calculation based on the current transaction amount (Rs100 = 1 point).

#### 3.3.4
The loyalty points calculation shall update dynamically if the transaction amount is modified.

#### 3.3.5
The form shall display the customer's current loyalty points balance before and the projected balance after the transaction (if approved).

#### 3.3.6
The form shall provide a field for reviewers to enter notes or comments regarding their review.

### 3.4 Transaction Approval/Modification

#### 3.4.1
The system shall provide an "Approve" button to approve the transaction as-is without modifications.

#### 3.4.2
The system shall provide a "Save Changes" button to approve the transaction with modifications.

#### 3.4.3
The system shall provide a "Reject" button to reject the transaction entirely.

#### 3.4.4
The system shall require the reviewer to enter a reason when rejecting a transaction.

#### 3.4.5
Upon approval (with or without modifications), the system shall update the transaction status to "Reviewed" or "Approved".

#### 3.4.6
Upon approval, the system shall record the identity of the reviewer and the timestamp of the review.

#### 3.4.7
If modifications were made, the system shall maintain a record of the original values and the modified values.

### 3.5 Post-Review Processing

#### 3.5.1
Upon approval, the system shall update the customer's loyalty points balance according to the approved transaction amount.

#### 3.5.2
The system shall generate a notification to the original staff member who entered the transaction, informing them of the review outcome.

#### 3.5.3
Approved transactions shall be marked as processed and shall no longer appear in the list of transactions requiring review.

#### 3.5.4
Rejected transactions shall be marked as rejected and shall require further attention from designated staff.

## 4. Non-Functional Requirements

### 4.1 Usability

#### 4.1.1
The transaction review interface shall be intuitive and consistent with the existing Sonata admin interface design.

#### 4.1.2
The review process shall be completable within 3 minutes for experienced users.

#### 4.1.3
Error messages shall be clear and provide actionable information.

### 4.2 Performance

#### 4.2.1
The transaction list shall load within 3 seconds, even when displaying up to 100 transactions.

#### 4.2.2
The transaction review form shall load within 2 seconds.

#### 4.2.3
The loyalty points calculation shall update in real-time (less than 1 second) when transaction values are modified.

### 4.3 Security

#### 4.3.1
Only authorized staff members with the appropriate role/permission shall have access to the review functionality.

#### 4.3.2
The system shall log all review actions, including who performed the review, what changes were made, and when.

#### 4.3.3
The system shall implement appropriate validation to prevent invalid data entry during the review process.

### 4.4 Reliability

#### 4.4.1
The transaction review process shall be transactional, ensuring that no partial updates occur.

#### 4.4.2
The system shall provide clear confirmation messages after successful review actions.

#### 4.4.3
The system shall provide a way to recover from errors during the review process without data loss.

## 5. Technical Requirements

### 5.1 Symfony/Sonata Integration

#### 5.1.1
The solution shall be implemented within the existing Symfony framework and Sonata admin bundle.

#### 5.1.2
The implementation shall follow Symfony best practices and Sonata admin conventions.

#### 5.1.3
The solution shall leverage existing entity structures, extending them as necessary.

### 5.2 Database Requirements

#### 5.2.1
The solution shall add the following fields to the transaction entity (if not already present):
- Review status (enum: pending_review, approved, rejected)
- Reviewer ID (foreign key to user table)
- Review timestamp
- Review notes/comments
- Original values (for tracking modifications)

#### 5.2.2
Database migrations shall be provided to update the schema without data loss.

### 5.3 Interface Requirements

#### 5.3.1
The review action shall be integrated into the existing Sonata admin list view.

#### 5.3.2
The review form shall be implemented as a custom Sonata admin action.

#### 5.3.3
The loyalty points calculation display shall be implemented using appropriate form events to update dynamically.

## 6. User Roles and Permissions

### 6.1 Transaction Reviewer

#### 6.1.1
Users with the "Transaction Reviewer" role shall have permission to:
- View the list of transactions requiring review
- Open the review form
- Approve, modify, or reject transactions
- View transaction history and audit logs

### 6.2 Administrator

#### 6.2.1
Users with the "Administrator" role shall have all the permissions of the Transaction Reviewer, plus:
- Ability to assign reviewer permissions to other users
- Access to reports and statistics about the review process
- Ability to override previous review decisions if necessary

## 7. Test Requirements

### 7.1 User Acceptance Testing

#### 7.1.1
The solution shall be tested with actual back office staff before deployment to production.

#### 7.1.2
Test scenarios shall include:
- Reviewing and approving a transaction without modifications
- Reviewing and approving a transaction with modifications
- Rejecting a transaction
- Handling edge cases (very large transaction amounts, zero amounts, etc.)

### 7.2 Integration Testing

#### 7.2.1
The solution shall be tested to ensure proper integration with the existing loyalty points system.

#### 7.2.2
The solution shall be tested to ensure proper logging and audit trail functionality.

## 8. Documentation and Training Requirements

### 8.1 User Documentation

#### 8.1.1
User documentation shall be provided for the transaction review process.

#### 8.1.2
The documentation shall include step-by-step instructions with screenshots.

### 8.2 Training

#### 8.2.1
Training sessions shall be conducted for back office staff before the feature is deployed to production.

#### 8.2.2
Training materials shall be provided for future reference.

## 9. Deployment and Support

### 9.1 Deployment

#### 9.1.1
The solution shall be deployed in a staging environment for testing before production deployment.

#### 9.1.2
The deployment process shall include database migration scripts.

### 9.2 Support

#### 9.2.1
Initial support shall be provided for a period of 2 weeks after deployment.

#### 9.2.2
A system for reporting and tracking issues shall be in place.

## 10. Future Considerations

### 10.1 Potential Enhancements

#### 10.1.1
Future enhancement may include automated validation rules to flag potentially incorrect manual entries.

#### 10.1.2
Future enhancement may include a mobile interface for transaction review.

#### 10.1.3
Future enhancement may include integration with a customer notification system.

## 11. Implementation Plan

### 11.1 Current State Analysis

#### 11.1.1
The current system already has the following components in place:
- `PaymentDetails` entity with `isManualEntry` and `ocrFailReason` fields
- `PaymentDetailsAdmin` class for managing payment details in Sonata Admin
- `PaymentDetailsReview` entity and `PaymentDetailsReviewAdmin` class with basic review functionality
- QC state tracking with states: passed, corrected, rejected

### 11.2 Implementation Tasks

#### 11.2.1 Database Schema Updates
- [ ] Update `PaymentDetailsReview` entity to include additional fields for reviewer notes/comments
- [ ] Add audit fields to track review history and modifications
- [ ] Run `bin/console doctrine:schema:update --force`

#### 11.2.2 Admin Interface Enhancements
- [x] Enhance `PaymentDetailsAdmin` list view to clearly identify manual entries requiring review
- [x] Add filtering capabilities for manual entries and review status

#### 11.2.3 Review Form Development
- [ ] Create new action to review payment details and to display all fields
- [ ] Action should appear only when manually entered flag is true for the payment details
- [ ] Implement dynamic loyalty points calculation display
- [ ] Add fields for reviewer notes and comments
- [ ] Implement validation for modified values

#### 11.2.4 Review Process Implementation
- [ ] Implement the approval workflow with options to approve, modify, or reject
- [ ] Create service to handle transaction state changes and audit logging

#### 11.2.5 Testing
- [ ] Create unit tests for review functionality
- [ ] Develop integration tests for the complete review workflow
- [ ] Perform user acceptance testing with back office staff

### 11.3 Technical Approach

#### 11.3.1 Leveraging Existing Components
- Use the existing `PaymentDetailsReview` entity and extend it with additional fields
- Enhance the existing QC state system to support the full review workflow
- Build upon the existing Sonata Admin integration

#### 11.3.2 New Components
- Create a dedicated service for handling the review process
- Implement a notification system for review outcomes
- Develop a specialized dashboard for transactions requiring review

### 11.4 Timeline Estimate
- Database schema updates: 1 day
- Admin interface enhancements: 2 days
- Review form development: 3 days
- Review process implementation: 3 days
- Testing and refinement: 2 days
- Total estimated time: 11 working days