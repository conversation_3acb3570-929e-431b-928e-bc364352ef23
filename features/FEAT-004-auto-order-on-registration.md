# FEAT-004: Auto-Order on Registration

## User Story
As a system administrator,
I want new users to automatically receive an order for item ID 1 when they create an account,
So that they can immediately engage with the rewards system and have a positive onboarding experience.

## Acceptance Criteria
- [ ] When a user successfully registers through any registration method (standard or OTP), an order for item ID 1 is automatically created
- [ ] The order is associated with the newly created user account
- [ ] The order is created with a "delivered" status to avoid requiring further confirmation
- [ ] No points are deducted from the user's account for this initial order
- [ ] The system properly handles the case where item ID 1 does not exist or is out of stock
- [ ] The auto-order creation is properly logged for audit purposes
- [ ] The feature works with both registration endpoints: `/api/register` and `/api/otp/register_check`

## Implementation Tasks

### Increment 1: Core Service for Auto-Order Creation
- [ ] Create or extend OrderService to add a method for creating an auto-order for new users (Medium)
  - Create `createAutoOrderForNewUser(SonataUserUser $user, int $itemId)` method
  - Method should handle finding the item, creating the order, and setting appropriate status
  - No points should be deducted from the user's balance
  - Method should handle error cases gracefully (item not found, out of stock)
  - Add proper logging for audit purposes
- [ ] Create unit tests for the auto-order creation service (Simple)
  - Test successful order creation
  - Test handling of error cases (item not found, out of stock)
  - Test that no points are deducted

### Increment 2: Integration with Registration Endpoints
- [ ] Modify DefaultController's register method to create auto-order after user creation (Simple)
  - Inject OrderService into the controller
  - Add call to createAutoOrderForNewUser after successful user creation
  - Add try/catch to handle potential errors without disrupting registration
  - Add logging for both success and failure cases
- [ ] Modify RegistrationOTPController's verifyOTP method to create auto-order after user creation (Simple)
  - Inject OrderService into the controller
  - Add call to createAutoOrderForNewUser after successful user creation
  - Add try/catch to handle potential errors without disrupting registration
  - Add logging for both success and failure cases

### Increment 3: Testing and Validation
- [ ] Create functional tests for auto-order creation during standard registration (Medium)
  - Test that an order is created when a user registers through `/api/register`
  - Verify the order has the correct properties (item ID, status, user association)
  - Test error handling cases
- [ ] Create functional tests for auto-order creation during OTP registration (Medium)
  - Test that an order is created when a user registers through `/api/otp/register_check`
  - Verify the order has the correct properties (item ID, status, user association)
  - Test error handling cases
- [ ] Verify that no points are deducted for the auto-order (Simple)
  - Test that user's points balance remains unchanged after registration and auto-order

## Technical Notes
* Architecture: Extend OrderService with new method for creating auto-orders, integrate with registration endpoints
* API Endpoints affected:
  * POST /api/register - Standard registration endpoint in DefaultController
  * POST /api/otp/register_check - OTP registration endpoint in RegistrationOTPController
* No database schema changes required
* Auto-Order Strategy:
  * Create order for item ID 1 with "delivered" status
  * No points deduction for this initial order
  * Handle error cases gracefully to ensure registration process is not disrupted
* Logging:
  * Log successful auto-order creation with user ID and order ID
  * Log failures with appropriate error messages for debugging

## Testing Notes
* Test registration with auto-order creation for both registration methods
* Test error handling when item ID 1 does not exist
* Test error handling when item ID 1 is out of stock
* Verify no points are deducted from the user's account
* Verify the order is created with "delivered" status
* Verify the order is properly associated with the newly created user
* Test with different user registration data to ensure consistent behavior

## Status
* [x] Planning
* [ ] In Progress
* [ ] Ready for Review
* [ ] Completed

## Completion Date
Pending implementation
