# Features Index

## Workflow Instructions for Code Agent

### Feature Definition Workflow

1. **Gather Requirements**:
   - Ask clarifying questions about the feature purpose, scope, and constraints
   - Help define who the feature is for (target users)
   - Understand why the feature is valuable to those users
2. **Create Feature Documentation**:
   - Create a new file in the `/features/` directory using the naming convention `FEAT-XXX-feature-name.md`
   - Help draft a clear user story in the format: "As a [user], I want to [action] so that [benefit]"
   - Define measurable acceptance criteria that will determine when the feature is complete
   - Set initial status to "Planning"
3. **Task Breakdown**:
   - Break down the implementation into smaller, manageable tasks
   - Estimate complexity for each task (Simple, Medium, Complex)
   - Organize tasks in logical implementation order
   - Document technical considerations or dependencies
4. **Implementation Strategy**:
   - Suggest an approach for implementing the feature
   - Identify which parts of the codebase will need to be modified
   - Note any potential challenges or risks

### Implementation Workflow

For each implementation task:

1. **Focused Development**:
   - Work on one task at a time
   - Generate code changes specifically addressing the current task
   - Update the feature file to mark tasks as complete
2. **Incremental Testing**:
   - Suggest test cases for the implemented functionality
   - Help write unit or integration tests as needed
   - Document testing notes in the feature file
3. **Progress Tracking**:
   - Update the status in the feature documentation as work progresses
   - Add any technical notes or decisions made during implementation
   - Document any deviations from the original plan

### Completion Workflow

When the feature implementation is complete:

1. **Acceptance Criteria Validation**:
   - Verify each acceptance criterion has been met
   - Document how each criterion was satisfied
2. **Documentation Updates**:
   - Update any relevant project documentation
   - Add the feature to project changelog if applicable
   - Mark feature as "Completed" in the feature file and INDEX.md
3. **Next Steps**:
   - Suggest any follow-up improvements or related features
   - Help prioritize the next feature to work on

## Feature Template

```
# FEAT-XXX: Feature Name

## User Story
As a [type of user],
I want to [perform some action],
So that [I can achieve some goal/benefit].

## Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

## Implementation Tasks
- [ ] Task 1
- [ ] Task 2
- [ ] Task 3

## Technical Notes
* Any architecture decisions
* API endpoints affected
* Database changes

## Testing Notes
* Test scenarios
* Edge cases to consider

## Status
* [ ] Planning
* [ ] In Progress
* [ ] Ready for Review
* [ ] Completed

## Completion Date
MM/DD/YYYY (when completed)
```

## Current Sprint

- [FEAT-001: Simplified Registration Backend](/workspaces/india_oil_admin/features/FEAT-001-simplified-registration-backend.md)
- [FEAT-003: PaymentDetails Review Feature](/workspaces/india_oil_admin/features/FEAT-003-review-manual-entry.md)
- [FEAT-004: Auto-Order on Registration](/workspaces/india_oil_admin/features/FEAT-004-auto-order-on-registration.md)

## Completed Features

- [No completed features yet]

## Backlog

- [No features in backlog yet]