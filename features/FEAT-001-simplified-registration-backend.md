# FEAT-001: Simplified Registration Backend

## User Story
As a system administrator,
I want to implement backend support for a simplified user registration process,
So that users can create accounts with only essential information while maintaining system integrity.

## Acceptance Criteria
- [ ] API endpoints validate user registration with only phone number and password as required fields
- [ ] Backend automatically generates an email using phone number with `_user` suffix and rewards.ioml.mu domain when not provided
- [ ] System uses user-provided email when available, only generating one when missing
- [ ] User entity maintains existing validation rules with email remaining required
- [ ] Profile retrieval endpoints exclude auto-generated emails from response payloads
- [ ] JWT token generation works correctly with minimal user information
- [ ] Admin interface properly displays and manages users with partial information
- [ ] API documentation updated to reflect new field requirements

## Implementation Tasks and Increments

### Increment 1: Email Generation Core Service
- [x] Create service to generate email addresses in format {phone_number}<EMAIL> using SonataUserUser::sanitizePhoneNumber (Simple)
  - Create `src/Service/EmailGenerationService.php` class
  - Implement `generateFromPhone($phoneNumber)` method using `SonataUserUser::sanitizePhoneNumber`
  - Return email in format `{sanitized_phone_number}<EMAIL>`
- [x] Create detection method to identify auto-generated emails (Simple)
  - Add `isAutoGenerated($email)` method to check if an email matches auto-generated pattern
  - Use regex pattern matching to identify auto-generated emails
- [x] Create unit tests for email generation service (Simple)
  - Test email generation with various phone number formats
  - Test detection of auto-generated vs. regular emails
  - Test handling of edge cases (empty strings, null values)

### Increment 2: Registration Flow Modification
- [x] Modify DefaultController's register method to support simplified registration (Medium)
  - Locate register method in `src/Controller/DefaultController.php` that handles `/api/register` endpoint
  - Inject `EmailGenerationService` into the controller
  - Add logic to check for email presence and generate one from phone if missing
  - Ensure phone number and password are required fields
- [x] Update API validation to make email field optional from frontend (Medium)
  - Update request validation in the register method to make email field optional
  - Keep existing validation for when email is provided
  - Add validation for phone number format
- [x] Add validation for auto-generated emails (Simple)
  - Ensure auto-generated emails are properly validated
  - Use EmailGenerationService's isAutoGenerated method to detect auto-generated emails
- [x] Create functional tests for registration with both provided and auto-generated emails (Medium)
  - Test registration with provided email
  - Test registration with only phone and password
  - Test edge cases for both scenarios

### Increment 3: Profile Endpoint Updates
- [x] Update profile endpoints to exclude auto-generated emails from responses (Medium)
  - Locate profile endpoint in `src/Controller/DefaultController.php`
  - Inject `EmailGenerationService` and use `isAutoGenerated` method
  - Modify response serialization to exclude auto-generated emails
- [x] Update user details endpoint to exclude auto-generated emails (Medium)
  - Apply same logic to user details endpoint
  - Ensure consistent behavior across all user data endpoints
- [x] Test profile endpoints to verify auto-generated emails are excluded (Medium)
  - Create test users with both real and auto-generated emails
  - Test profile responses for both user types
  - Verify auto-generated emails are excluded while real emails are included

### Increment 4: Authentication and Admin Interface
- [ ] Update authentication services to work with either real or generated emails (Medium)
  - Ensure JWT token generation works with auto-generated emails
  - Verify refresh tokens work properly with auto-generated emails
  - Test login functionality with both email types
- [ ] Add visual indicators in Sonata Admin for auto-generated emails (Simple)
  - Modify admin templates to show indicator for auto-generated emails
  - Add color coding or icon to distinguish auto-generated emails
- [ ] Add filtering capability for auto-generated emails in admin interface (Simple)
  - Add custom filter to Sonata Admin configuration
  - Allow admins to filter users with auto-generated vs. real emails

### Increment 5: Documentation and Cleanup
- [ ] Update API documentation to reflect new requirements (Simple)
  - Update API annotations or OpenAPI specifications
  - Document the optional email field and auto-generation process
  - Clarify behavior of profile endpoints with auto-generated emails
- [ ] Create end-to-end tests for the complete registration flow (Medium)
  - Test the entire user journey from registration to profile viewing
  - Verify all components work together correctly
- [ ] Verify email sending works correctly with auto-generated emails (Simple)
  - Test system emails sent to auto-generated addresses
  - Ensure proper handling of email communication with these users

## Technical Notes
* Architecture: Create new email generation service, update registration API endpoints
* API Endpoints affected:
  * POST /api/register - Registration endpoint in DefaultController to accept minimal fields
  * GET /api/profile - Profile endpoint to exclude auto-generated emails
  * GET /api/user - User details endpoint to exclude auto-generated emails
  * POST /api/token - JWT token generation (unchanged)
* No database schema changes required - email remains required in database
* Email Generation Strategy:
  * Format: {sanitized_phone_number}<EMAIL>
  * Sanitized phone number is processed using the existing SonataUserUser::sanitizePhoneNumber method
  * Example: <EMAIL>
  * This format applies only when user doesn't provide an email address
* Auto-generated Email Detection:
  * Use EmailGenerationService's isAutoGenerated method to identify auto-generated emails
  * Use this to filter responses in profile-related endpoints

## Testing Notes
* Test registration with only phone number and password, verifying auto-generated email format
* Test registration with user-provided email, verifying it's used instead of generating one
* Test profile endpoints to ensure auto-generated emails are excluded from responses
* Test profile endpoints with real user-provided emails to ensure they are included in responses
* Test uniqueness of generated emails for different phone numbers
* Test with phone numbers containing special characters or international formats
* Test user login/authentication with both real and auto-generated emails
* Test error handling for invalid registration data
* Verify system emails work correctly with both real and auto-generated email addresses

## Status
* [x] Planning
* [x] In Progress
* [x] Ready for Review
* [ ] Completed

## Completion Date
Pending implementation (started May 12, 2025)
