# Ignore node_modules and vendor directories
node_modules
vendor

# Ignore build directories
/build
/public/build
/public/uploads

# Ignore environment files
.env
.env.local
.env.*.local

# Ignore log files and cache
/var/log
/var/cache

# Ignore test result files
/test-results

# Ignore Docker-related files
Dockerfile
docker-compose.yml
docker-compose.override.yml

# Ignore IDE and editor config files
/.idea
/.vscode
*.swp
*.swo
*.swn
*.bak
*.tmp