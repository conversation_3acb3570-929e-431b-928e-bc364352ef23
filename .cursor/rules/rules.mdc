---
description: General Guideline for the project
globs: *.php,*.yaml,*.yml
alwaysApply: false
---
## Project Overview
This backend component supports a dashboard for comparing OCR accuracy in the Indian Oil Mauritius loyalty system. The system captures fuel dispenser screen images, processes them in this Symfony/PHP backend, and uses ChatGPT Vision API to extract structured data.

## Backend Architecture
- **Backend Application**: Built with Symfony/PHP and sonata admin
- **Image Processing**: Converts images to grayscale before sending to ChatGPT Vision API
- **OCR Process**: Uses ChatGPT Vision API to extract structured JSON data from images
- **API Layer**: Provides endpoints for the dashboard frontend to consume accuracy metrics
- **UI**: Admin LTE and bootstrap

## Directory Structure
The project follows Symfony's standard directory structure with some custom directories:

```
├── config                  # Configuration files
│   ├── jwt                 # JWT authentication keys
│   ├── packages            # Package configuration
│   ├── routes              # Route definitions
├── migrations              # Database migrations
├── public                  # Publicly accessible files
├── src                     # Application source code
│   ├── Admin               # Admin controllers and services
│   ├── Command             # Console commands
│   ├── Controller          # HTTP controllers
│   ├── DataFixtures        # Data fixtures for testing
│   ├── Entity              # Doctrine entities
│   ├── EventListener       # Event listeners/subscribers
│   ├── Mailer              # Email functionality
│   ├── Repository          # Doctrine repositories
│   └── Service             # Application services
├── templates               # Twig templates
├── tests                   # Test suite
├── translations            # Translation files
```

## Code Standards

### PHP Coding Standards
- Follow PSR-1, PSR-2, and PSR-12 coding standards
- Use PHP 8.1+ features where appropriate
- Use camelCase for method names, variables, and properties
- Use PascalCase for class names
- Use snake_case for translation keys and route names

### Symfony Best Practices
- Use constructor dependency injection
- Configure services in YAML using `config/services.yaml`
- Use annotations/attributes for routing, validation, and ORM mapping
- Respect Symfony's naming conventions for services and controllers

### Object-Oriented Design Principles
- Follow SOLID principles
- Keep controllers thin, move logic to services
- Use interfaces for service contracts
- Favor composition over inheritance

### API Development
- Follow REST principles
- Use JSON for request and response formats
- Return appropriate HTTP status codes
- Version APIs when making breaking changes
- Document APIs using API Platform or OpenAPI

### Database
- Use doctrine orm
- Define entity relationships explicitly
- Use appropriate database indices
- Write optimized queries using Doctrine Query Builder or DQL

### Testing
- Use PHPUnit for testing. Usage: `php bin/phpunit`
- After each changes suggest to run the test

## Implementation Guidelines

### Error Handling
- Use custom exception classes for domain-specific errors
- Return structured error responses from API endpoints
- Log errors appropriately with different severity levels
- Handle external API failures gracefully

### Security
- Validate and sanitize all user inputs
- Use Symfony Security component for authentication and authorization
- Implement CSRF protection for forms
- Follow OWASP security guidelines