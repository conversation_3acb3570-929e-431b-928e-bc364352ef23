---
description: Sonata Admin
globs: src/Admin/**/*.php
alwaysApply: false
---
# How to Configure Sonata Admin

## Template Overriding

There are two main approaches to override Sonata Admin templates:

### 1. Service Configuration Method (Preferred)

Add the template configuration in your `sonata_admin.yaml` service definition:

```yaml
# config/packages/sonata_admin.yaml
services:
    admin.your_admin:
        class: App\Admin\YourAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\YourEntity, manager_type: orm, label: "Your Label" }
        calls:
            - [ setTemplate, ['show', 'admin/your_entity/show.html.twig'] ]
            - [ setTemplate, ['edit', 'admin/your_entity/edit.html.twig'] ]
            - [ setTemplate, ['list', 'admin/your_entity/list.html.twig'] ]
```

This allows you to override specific templates for specific actions without modifying the admin class directly.

### 2. Template Creation and Extension

Create template files that extend the base Sonata Admin templates:

```twig
{# templates/admin/your_entity/show.html.twig #}
{% extends '@SonataAdmin/CRUD/show.html.twig' %}

{% block show %}
    {# Display the standard show content first #}
    {{ parent() }}

    {# Add your custom sections #}
    {% if object.someCollection is defined and object.someCollection.count() > 0 %}
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h4 class="box-title">Custom Section</h4>
                    </div>
                    <div class="box-body">
                        {% include 'admin/your_entity/custom_section.html.twig' with {'object': object} %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}
```

## Show Fields vs. Complex Templates

Sonata Admin's `configureShowFields` method works well with simple field displays, but has limitations with complex templates:

### Simple Fields (Works Well)
```php
protected function configureShowFields(ShowMapper $show): void
{
    $show
        ->add('field1')
        ->add('field2')
        ->add('field3');
}
```

### For Complex Templates (Better Approach)
1. Use a basic structure in `configureShowFields`
2. Create a custom show template that extends the Sonata Admin base template
3. Add your complex template sections after the standard fields:

```php
// In your Admin class
protected function configureShowFields(ShowMapper $show): void
{
    $show->with('Basic Information', ['class' => 'col-md-12']);
    $show
        ->add('field1')
        ->add('field2')
        ->add('field3');
    $show->end();

    // Complex sections have been removed from here
    // They will be added through the template override
}
```

```twig
{# In your custom template #}
{% extends '@SonataAdmin/CRUD/show.html.twig' %}

{% block show %}
    {{ parent() }}
    
    {# Add your complex section here #}
    {% include 'admin/your_entity/complex_section.html.twig' %}
{% endblock %}
```

## Best Practices

1. **Separation of Concerns**: 
   - Use `configureShowFields` for simple fields and basic structure
   - Use template overrides for complex UI components

2. **Template Structure**:
   - Put templates in `templates/admin/your_entity_name/`
   - Follow Symfony's naming conventions

3. **Service Configuration**:
   - Configure template overrides in your service definition
   - Keep admin classes focused on data structure, not presentation

4. **Bootstrap Integration**:
   - Sonata Admin uses AdminLTE/Bootstrap
   - Complex templates should use consistent Bootstrap components (boxes, panels, etc.)

This approach keeps your code more maintainable and follows the Symfony principle of separating business logic from presentation.
