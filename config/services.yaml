# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    from_email: { "<EMAIL>": "IOML Rewards" }
    retry_ttl: 300
    token_ttl: 600
    env(GEMINI_API_KEY): '%env(GEMINI_API_KEY)%'  # Changed to use GEMINI_API_KEY directly

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\EventListener\JWTCreatedListener:
        class: App\EventListener\JWTCreatedListener
        arguments:
            - '@request_stack'
            - '@security.role_hierarchy'
        tags:
            - { name: kernel.event_listener, event: lexik_jwt_authentication.on_jwt_created, method: onJWTCreated }

    App\Security\UserProvider:
        class: App\Security\UserProvider
        arguments:
            - '@sonata.user.manager.user' # Inject the UserManager

    Sonata\UserBundle\Entity\UserManager: '@sonata.user.manager.user'
    Sonata\MediaBundle\Model\MediaManagerInterface: '@sonata.media.manager.media'
    Sonata\MediaBundle\Provider\Pool: '@sonata.media.pool'
    Sonata\MediaBundle\Provider\ImageProvider: '@sonata.media.provider.image'

    App\FuelMeterScanner:
        class: App\FuelMeterScanner
        arguments:
            - '%env(OPENAI_API_KEY)%'

    App\Service\OCR\CurrentOCRProcessor:
        arguments:
            $fuelMeterScanner: '@App\FuelMeterScanner'
            $parameterBag: '@parameter_bag'

    App\Service\OCR\ImprovedOCRProcessor:
        arguments:
            $apiKey: '%env(OPENAI_API_KEY)%'
            $parameterBag: '@parameter_bag'

    App\Mailer\UserMailer:
        class: App\Mailer\UserMailer
        arguments:
            - '@router'
            - '@twig'
            - '@mailer'
            - '%from_email%'
            - 'reset_password_email.html.twig'

    App\Controller\ForgotPasswordController:
        class: App\Controller\ForgotPasswordController
        arguments:
            - "@sonata.user.manager.user"
            - "@App\\Mailer\\UserMailer"
            - "@sonata.user.util.token_generator"
            - "%retry_ttl%"
        tags: ['controller.service_arguments']

    App\Controller\ResetPasswordController:
        class: App\Controller\ResetPasswordController
        arguments:
            - "@twig"
            - "@router"
            - "@security.authorization_checker"
            - "@sonata.user.manager.user"
            - "@form.factory"
            - "%token_ttl%"
        tags: ['controller.service_arguments']

    App\Service\OrderService:
        class: App\Service\OrderService
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@logger'

    App\Service\OTPService:
        class: App\Service\OTPService
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@App\Repository\OTPOrderVerificationRepository'
            - '@logger'
            - '@App\Service\OrderService'
            - '@App\Repository\RegistrationOTPRepository'
            - '@App\Service\PhoneValidationService'

    App\Service\PhoneValidationService:
        class: App\Service\PhoneValidationService
