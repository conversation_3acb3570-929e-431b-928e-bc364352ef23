controllers:
    resource:
        path: ../src/Controller/
        namespace: App\Controller
    type: attribute

getapp:
    path: /getapp
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController
    defaults:
        path: 'https://play.google.com/store/apps/details?id=mu.ioml.rewards'
        permanent: false

get_workplace:
    path: /getworkplace
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController
    defaults:
        path: '/uploads/apk/ioml_rewards_18.apk'
        permanent: false

get_petrocard_form:
    path: /petrocard/form
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController
    defaults:
        path: 'http://www.ioml.mu/images/Application_form_for_Indian_Oil_PETROCARD_.pdf'
        permanent: false