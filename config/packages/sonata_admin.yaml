services:
    admin.filling_station_service_details:
        class: App\Admin\FillingStationServiceDetailsAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\FillingStationServiceDetails, manager_type: orm, label: "Service Details" }

    admin.lottery_ticket:
        class: App\Admin\LotteryTicketAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\LotteryTicket, manager_type: orm, label: LotteryTicket }

    admin.filling_station:
        class: App\Admin\FillingStationAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\FillingStation, manager_type: orm, label: "Filling Stations" }

    admin.filling_station_service:
        class: App\Admin\FillingStationServiceAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\FillingStationService, manager_type: orm, label: "Filling Stations Services" }

    admin.payment_details:
        class: App\Admin\PaymentDetailsAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\PaymentDetails, manager_type: orm, label: Payments Details, controller: App\Controller\Admin\PaymentDetailsReviewController }
        calls:
            - [ setTemplate, ['show', 'admin/payment_details/show.html.twig'] ]
            - [ setEntityManager, ['@doctrine.orm.entity_manager'] ]

    admin.payment_details_review:
        class: App\Admin\PaymentDetailsReviewAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\PaymentDetailsReview, manager_type: orm, label: "Payment Details Review", controller: App\Controller\Admin\PaymentDetailsReviewQualityControlController }
        calls:
            - [ setEntityManager, ['@doctrine.orm.entity_manager'] ]
            - [ setTemplate, ['edit', 'admin/payment_details_review/edit.html.twig'] ]
            - [ setTemplate, ['action_buttons', 'admin/payment_details_review/button_quality_control.html.twig'] ]
            - [ setTemplate, ['list', 'admin/payment_details_review/list.html.twig'] ]

    admin.rewards_item:
        class: App\Admin\RewardsItemAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\RewardsItem, manager_type: orm, label: "Rewards Items" }

    admin.rewards_order:
        class: App\Admin\RewardsOrderAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\RewardsOrder, manager_type: orm, label: "Rewards Orders" }

    admin.rewards_item_inventory_movement:
        class: App\Admin\RewardsItemInventoryMovementAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\RewardsItemInventoryMovement, manager_type: orm, label: "Inventory Movement" }

    admin.exclusive_offer:
        class: App\Admin\ExclusiveOfferAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\ExclusiveOffer, manager_type: orm, label: "Exclusive Offers" }

    admin.image_validation_training_data:
        class: App\Admin\ImageValidationTrainingDataAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\ImageValidationTrainingData, manager_type: orm, label: "Image Validation Training Data", controller: App\Controller\Admin\ImageValidationTrainingDataController }
        calls:
            - [ setTemplate, ['show_image', 'admin/image_validation_training_data/show_image.html.twig'] ]
            - [ setTemplate, ['list_image', 'admin/image_validation_training_data/list_image.html.twig'] ]

sonata_admin:
    title: 'IOML Rewards'
    title_logo: /images/logo.png?v=2
    assets:
        extra_javascripts:
            - 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDX00SBtCUl-daFyRl3TL2WiCbU3vME_lE'
            - 'https://unpkg.com/location-picker/dist/location-picker.min.js'
            - '/js/map_picker.js'
            - 'https://cdn.jsdelivr.net/npm/chart.js'
    dashboard:
        groups:
            admin.group.filling_station:
                label: Filling Station
                translation_domain: App
                icon: 'fa fa-gas-pump'
                items:
                    - admin.filling_station
                    - admin.filling_station_service
                    - admin.payment_details
                    - admin.payment_details_review
                    - admin.image_validation_training_data
            admin.group.loyalty_shop:
                label: Loyalty Shop
                translation_domain: App
                icon: 'fa fa-shopping-cart'
                items:
                    - admin.rewards_order
                    - admin.rewards_item
                    - admin.rewards_item_inventory_movement
            admin.group.Lottery_ticket:
                label: Lottery Ticket
                translation_domain: App
                icon: 'fa fa-ticket'
                items:
                    - admin.lottery_ticket
            admin.group.exclusive_offer:
                label: Exclusive Offers
                translation_domain: App
                icon: 'fa fa-fire'
                items:
                    - admin.exclusive_offer
            users:
                icon: 'fa fa-user'
                label: 'User Management'
                label_catalogue: 'SonataUserBundle'
                items:
                    - 'sonata.user.admin.user'

        blocks:
            - { type: sonata.admin.block.admin_list, position: left }

sonata_block:
    blocks:
        sonata.admin.block.admin_list:
            contexts: [admin]
