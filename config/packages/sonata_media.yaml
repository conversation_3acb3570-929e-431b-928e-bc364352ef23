sonata_media:
    class:
        media: App\Entity\SonataMediaMedia
    db_driver: doctrine_orm
    default_context: default
    contexts:
        default:
            providers:
                - sonata.media.provider.dailymotion
                - sonata.media.provider.youtube
                - sonata.media.provider.image
                - sonata.media.provider.file
                - sonata.media.provider.vimeo

            formats:
                small: { width: 100 , quality: 80}
                big:   { width: 500 , quality: 80}

    cdn:
        server:
            path: /uploads/media

    filesystem:
        local:
            directory: "%kernel.project_dir%/public/uploads/media"
            create: false
