security:
    enable_authenticator_manager: true
    password_hashers:
        Sonata\UserBundle\Model\UserInterface: auto
    providers:
        app_user_provider:
            id: App\Security\UserProvider
        sonata_user_bundle:
            id: sonata.user.security.user_provider
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        login:
            pattern: ^/api/login
            stateless: true
            provider: app_user_provider # Use the new provider
            json_login:
                check_path: /api/login_check
                username_path: username
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
        api_token_refresh:
            provider: sonata_user_bundle
            pattern: ^/api/token/refresh
            stateless: true
            refresh_jwt:
                check_path: /api/token/refresh
        api:
            pattern:   ^/api
            stateless: true
            jwt: ~
            provider: sonata_user_bundle
        admin:
            lazy: true
            pattern: /admin(.*)
            provider: sonata_user_bundle
            context: user
            form_login:
                login_path: sonata_user_admin_security_login
                check_path: sonata_user_admin_security_check
                default_target_path: sonata_admin_dashboard
            logout:
                path: sonata_user_admin_security_logout
                target: sonata_user_admin_security_login
            remember_me:
                secret: '%env(APP_SECRET)%'
                lifetime: 2629746
                path: /admin

    role_hierarchy:
        ROLE_MODERATOR: [ROLE_USER]
        ROLE_ADMIN: [ROLE_USER, ROLE_SONATA_ADMIN, ROLE_MODERATOR]
        ROLE_SUPER_ADMIN: [ROLE_ADMIN, ROLE_ALLOWED_TO_SWITCH]

    access_control:
        # Public endpoints should be at the top
        - { path: ^/api/v1/customers/search, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/otp/pending-orders, roles: PUBLIC_ACCESS }
        - { path: ^/api/google/login_check$, role: PUBLIC_ACCESS }
        - { path: ^/api/v1/exclusive-offers$, role: PUBLIC_ACCESS }
        - { path: ^/api/otp, role: PUBLIC_ACCESS }
        - { path: ^/api/v1/filling-stations$, role: PUBLIC_ACCESS }
        - { path: ^/admin/login$, role: PUBLIC_ACCESS }
        - { path: ^/admin/logout$, role: PUBLIC_ACCESS }
        - { path: ^/admin/login_check$, role: PUBLIC_ACCESS }
        - { path: ^/admin/request$, role: PUBLIC_ACCESS }
        - { path: ^/admin/check-email$, role: PUBLIC_ACCESS }
        - { path: ^/admin/reset/.*$, role: PUBLIC_ACCESS }
        - { path: ^/api/login, roles: PUBLIC_ACCESS }
        - { path: ^/api/register, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/forgot-password, roles: PUBLIC_ACCESS }
        - { path: ^/api/token/refresh, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/filling-stations/\d+/services/.+$, roles: PUBLIC_ACCESS }

        # Protected endpoints
        - { path: ^/api/v1/payments-details.*$, roles: ROLE_MODERATOR }
        - { path: ^/api/v1/fuel-meter/scan.*$, roles: ROLE_MODERATOR }
        - { path: ^/api/v1/customers/\d+/pending-orders$, roles: ROLE_MODERATOR }
        - { path: ^/api,       roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/admin/, role: ROLE_ADMIN }
        - { path: ^/.*, role: PUBLIC_ACCESS }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
