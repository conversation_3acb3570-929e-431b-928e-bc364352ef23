services:
  web:
    platform: linux/amd64
    image: sydneyyvan/indian_oil_admin:latest
    ports:
      - "8888:80"
    depends_on:
      - db
    volumes:
      - ./uploads:/var/www/html/public/uploads
    environment:
      OPENAI_API_KEY: "***************************************************"

  db:
    platform: linux/amd64
    image: mariadb:10.4
    restart: unless-stopped
    volumes:
      - mariadb-data:/var/lib/mysql
    ports:
      - 127.0.0.1:3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: mariadb
      MYSQL_DATABASE: mariadb
      MYSQL_USER: mariadb
      MYSQL_PASSWORD: mariadb

  adminer:
    image: adminer:latest
    ports:
      - 127.0.0.1:8890:8080

volumes:
  mariadb-data:
  uploads:
