<VirtualHost *:80>
        DocumentRoot /var/www/html/public
        ErrorLog ${APACHE_LOG_DIR}/error.log
        CustomLog ${APACHE_LOG_DIR}/access.log combined

        SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
        RewriteEngine On
        SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
        RewriteCond %{DOCUMENT_ROOT}%{REQUEST_FILENAME} !-f
        RewriteCond %{DOCUMENT_ROOT}%{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ /index.php [L]
</VirtualHost>
