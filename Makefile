# Variables
IMAGE_NAME = sydneyyvan/indian_oil_admin
DOCKERFILE = Dockerfile.prod
VERSION ?= 0.13.0
PLATFORM = linux/amd64
SSH_HOST = rewards.ioml.mu
SSH_USER = debian
SSH_PATH = /srv/indian_oil_admin

all: build tag-latest

test:
	@echo "Running tests"
	php bin/phpunit --stop-on-failure --stop-on-error

# Commands
build:
	@echo "Building Docker image for platform $(PLATFORM) with tag: $(IMAGE_NAME):$(VERSION)"
	docker buildx build --platform $(PLATFORM) -t $(IMAGE_NAME):$(VERSION) -f $(DOCKERFILE) .

tag-latest:
	@echo "Tagging image $(IMAGE_NAME):$(VERSION) as latest"
	docker tag $(IMAGE_NAME):$(VERSION) $(IMAGE_NAME):latest

push:
	@echo "Pushing $(IMAGE_NAME):$(VERSION) to registry"
	docker push $(IMAGE_NAME):$(VERSION)
	@echo "Pushing $(IMAGE_NAME):latest to registry"
	docker push $(IMAGE_NAME):latest

update-prod:
	@echo "Copying docker-compose.prod.yml to remote server"
	scp docker-compose.prod.yml $(SSH_USER)@$(SSH_HOST):$(SSH_PATH)/docker-compose.prod.yml
	@echo "Running docker-compose up on remote server"
	ssh $(SSH_USER)@$(SSH_HOST) 'sudo docker-compose -f $(SSH_PATH)/docker-compose.prod.yml pull && sudo docker-compose -f $(SSH_PATH)/docker-compose.prod.yml up -d'

deploy: build tag-latest push update-prod
	@echo "Deploying version $(VERSION) of $(IMAGE_NAME) to production"

deploy-migrate: deploy migrate-prod
	@echo "Deploying version $(VERSION) of $(IMAGE_NAME) to production and running migrations"

migrate-prod:
	@echo "Running migrations on remote server"
	ssh $(SSH_USER)@$(SSH_HOST) 'sudo docker-compose -f $(SSH_PATH)/docker-compose.prod.yml exec -T web bin/console doctrine:schema:update -f'

# Clean up dangling images
clean:
	@echo "Cleaning up dangling Docker images"
	docker image prune -f